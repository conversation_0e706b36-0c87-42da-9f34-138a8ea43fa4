const path = require('path');
const fileExists = require('file-exists');

const del = require('del');
const child_process = require('child_process');
const ci = require('miniprogram-ci');

const gulp = require('gulp');


const less = require('gulp-less');
const uglify = require('gulp-uglify');
const cleanCSS = require('gulp-clean-css');
const rename = require('gulp-rename');
const gulpif = require('gulp-if');
const replace = require('gulp-replace');
const alias = require('gulp-path-alias');
const autoprefixer = require('gulp-autoprefixer');

const pkg = require('./package.json');
let projectConfig = require('./project.config.json');
const buildPath = path.join(__dirname, 'dist/');

const argv = require('minimist')(process.argv.slice(1));
const appId = argv["appId"];
if (appId){
  console.log('set appId  = ',appId);
  projectConfig['appid'] = appId;
}


const env = process.env.NODE_ENV
console.log("evn=", env)
const isPro = env === 'production';
console.log("isPro=", isPro)
const branchName = child_process.execSync('git symbolic-ref --short HEAD', {
  encoding: 'utf8',
});

const paths = {
  styles: {
    src: ['src/**/*.less'],
    dest: buildPath,
  },
  images: {
    src: 'src/images/**/*.{png,jpg,jpeg,svg,gif}',
    dest: buildPath,
  },
  scripts: {
    src: 'src/**/*.js',
    dest: buildPath,
  },
  copy: {
    src: [
      'src/**',
      '!src/**/*.less',
      '!src/**/*.js',
      'package.json',
    ],
    dest: buildPath,
  },
};

// 删除构建
function clean() {
  return del([buildPath]);
}

function log() {
  const data = Array.prototype.slice.call(arguments);
  console.log(data);
}

// 任务处理函数
function styles() {
  return gulp
    .src(paths.styles.src, { base: 'src' })
    .pipe(
      alias({
        paths: {
          '@': path.resolve(__dirname, './src/'),
        },
      })
    )
    .pipe(less())
    .pipe(autoprefixer())
    .pipe(gulpif(isPro, cleanCSS()))
    .pipe(rename((path) => (path.extname = '.wxss')))
    .pipe(gulp.dest(paths.styles.dest));
}

function scripts() {
  return (
    gulp
      .src(paths.scripts.src, { base: 'src' })
      .pipe(
        alias({
          paths: {
            '@': path.resolve(__dirname, './src/'), // src 目录
          },
        })
      )
      // .pipe(babel({ presets: ['@babel/env'], 'plugins': [] }))
      .pipe(replace('%ENV%', process.env.NODE_ENV)) // 环境变量静态替换
      .pipe(replace('%VERSION%', pkg.version))
      .pipe(gulpif(isPro, uglify()))
      .pipe(gulp.dest(paths.scripts.dest))
  );
}

// 不需要处理的文件直接复制过去
function copy() {
  return gulp
    .src(paths.copy.src)
    .pipe(gulp.dest(paths.copy.dest));
}

function watchFiles() {
  const w1 = gulp.watch(paths.styles.src, styles).on('unlink', function (file) {
    log(file + ' is deleted');
    const filePath = file.replace(/src\\/, 'dist\\');
    del([filePath]);
  });

  const w2 = gulp
    .watch(paths.scripts.src, scripts)
    .on('unlink', function (file) {
      log(file + ' is deleted');
      const filePath = file.replace(/src\\/, 'dist\\');
      del([filePath]);
    });

  const w3 = gulp.watch(paths.copy.src, copy).on('unlink', function (file) {
    log(file + ' is deleted');
    const filePath = file.replace(/src\\/, 'dist\\');
    del([filePath]);
  });

  return Promise.all([w1, w2, w3]);
}

/**
 * 小程序ci相关函数
 */
let project = {};

const keyFile = fileExists.sync(`./key/private.${appId}.key`);
if (keyFile) {
  project = new ci.Project({
    appid: appId,
    type: 'miniProgram',
    projectPath: './dist',
    privateKeyPath: `./key/private.${appId}.key`,
  });
}
async function npmBuild() {

  await ci.packNpmManually({
    packageJsonPath: './package.json',
    miniprogramNpmDistDir: './src/',
  });
}
const envLabels = {
  'production': '正式环境',
  'development': '测试环境',
  'pre': '预发环境',
};

// 机器人代号，有效范围[1-30]
const robotMap = {
  'development': 1,
  'production': 2,
  'pre': 3,
}

async function mpUpload() {
  log('mpUpload appid',appId);

   if (!appId) {
     console.log('\x1b[35m%s\x1b[0m', `
════════════════════════════════════════════════════════════════════════
⚡【${envLabels[env]}】小程序打包失败，请先执行 export APPID=你的appid 命令，设置appid
════════════════════════════════════════════════════════════════════════
  `);
    return false;
  }
  projectConfig['appid'] = appId;

  log('projectConfig appid',projectConfig.appid);

  const uploadResult = await ci.upload({
    project,
    version: pkg.version,
    desc: `【${envLabels[env]}】${pkg.description}`,
    setting: {
      es7: true,
      es6: true,
      minifyJS: true,
      minifyWXML: true,
      minifyWXSS: true,
      minify: true,
      autoPrefixWXSS: true,
    },
    robot: robotMap[env],
    onProgressUpdate: console.log,
  });
  console.log('[uploadResult:]', uploadResult);
  console.log('\x1b[35m%s\x1b[0m', `
════════════════════════════════════════════════════════════════════════
🚀【${envLabels[env]}】小程序打包已完成，可以去发布了https://mp.weixin.qq.com/
════════════════════════════════════════════════════════════════════════
  `);
}

async function preview() {
  const previewResult = await ci.preview({
    project,
    desc: `【${envLabels[env]}】${pkg.description}`, // 此备注将显示在“小程序助手”开发版列表中
    qrcodeFormat: 'image',
    qrcodeOutputDest: './preview.jpg',
    setting: {
      es7: true,
      es6: true,
      minifyJS: true,
      minifyWXML: true,
      minifyWXSS: true,
      minify: true,
      autoPrefixWXSS: true,
    },
    robot: robotMap[env],
    onProgressUpdate: console.log,
    // pagePath: 'pages/index/index', // 预览页面
    // searchQuery: 'a=1&b=2',  // 预览参数 [注意!]这里的`&`字符在命令行中应写成转义字符`\&`
  });
  console.log('[previewResult:]', previewResult);
  console.log('\x1b[35m%s\x1b[0m', `
════════════════════════════════════════════════════════════════════════
🚀【${envLabels[env] || '测试环境'}】小程序预览已完成，可以去小程序助手中查看了
════════════════════════════════════════════════════════════════════════
  `);
}
exports.watch = watchFiles;
exports.preview = preview;
// ci 自动构建npm
exports.npm = npmBuild;
exports.upload = mpUpload;

exports.default = gulp.series(styles, scripts, copy, watchFiles);

exports.build = gulp.series(clean, styles, scripts, copy);
