{"name": "wealth", "version": "1.1.8", "description": "feat: 增加错别字修改正", "main": "app.js", "scripts": {"serve": "rm -rf ./dist && cross-env NODE_ENV=development gulp", "serve:pro": "rm -rf ./dist && cross-env NODE_ENV=production gulp", "preview": "npm run build:dev && NODE_ENV=development gulp preview", "preview:pro": "npm run build:pro && NODE_ENV=production gulp preview", "build:dev": "rm -rf ./dist && cross-env NODE_ENV=development gulp build", "build:pro": "rm -rf ./dist && cross-env NODE_ENV=production gulp build", "build:npm": "gulp npm", "deploy:dev": "npm run build:dev && NODE_ENV=development gulp upload", "deploy:pro": "npm run build:pro && NODE_ENV=production gulp upload", "prepare": "husky && npm run build:npm"}, "author": "JJ", "license": "ISC", "devDependencies": {"@commitlint/cli": "^14.1.0", "@commitlint/config-conventional": "^14.1.0", "child_process": "^1.0.2", "cross-env": "^7.0.3", "del": "6.1.1", "eslint": "^8.57.0", "file-exists": "^5.0.1", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-if": "^3.0.0", "gulp-less": "^5.0.0", "gulp-path-alias": "^1.2.1", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "gulp-uglify": "^3.0.2", "husky": "^9.0.11", "lint-staged": "^15.2.2", "miniprogram-ci": "^1.1.6", "prettier": "^3.2.5"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json}": ["prettier --write"]}, "dependencies": {"@vant/weapp": "^1.11.7", "cos-wx-sdk-v5": "^1.6.2", "tdesign-miniprogram": "^1.9.5"}}