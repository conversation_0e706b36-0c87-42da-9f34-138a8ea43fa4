.tabs {
  position: fixed;
  left: 0;
  bottom: 0rpx;
  width: 100%;
  height: 204rpx;
  margin: auto;
  display: flex;


  &-bg {
    width: 100%;
    height: 100%;
  }

  &-icon {
    width: 52rpx;
    height: 52rpx;
    margin-bottom: 6rpx;
  }

  &-item {
    position: absolute;
    font-size: 24rpx;
    color: #052031;
    text-align: center;

    &.active {
      color: #2FD2F8;
    }

    &.home {
      top: 66rpx;
      left: 150rpx;
    }

    &.center {
      width: 124rpx;
      left: 0;
      right: 0;
      top: 0;
      margin: auto;
    }

    &.person {
      top: 66rpx;
      right: 130rpx;
    }
  }


  .center {
    .tabs-icon {
      width: 124rpx;
      height: 112rpx;
      margin-bottom: 12rpx;
    }
  }
}