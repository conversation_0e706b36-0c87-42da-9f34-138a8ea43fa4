Component({
  properties: {
    activeIndex: {
      type: [Number],
      default: 0,
    },
  },
  data: {},
  methods: {
    switchTab({
      currentTarget: {
        dataset: { index },
      },
    }) {
      console.log("jump====: ", index);

      if (index == this.data.activeIndex) {
        return;
      }

      console.log("activeIndex: ", this.data.activeIndex);

      if (index == "0") {
        wx.redirectTo({
          url: "/pages/index/index",
        });
      } else if (index == "1") {
        wx.redirectTo({
          //url: "/pages/retrieval/video/index",
          url: "/play-live/site-live/index",
        });
      } else if (index == "2") {
        wx.redirectTo({
          url: "/pages/ucenter/index/index",
        });
      }
    },
  },
});
