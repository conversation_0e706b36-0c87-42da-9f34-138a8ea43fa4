const app = getApp();
Component({
  properties: {
    navData: {
      type: Object,
      value: {},
    },
  },
  attached() {
    const pages = getCurrentPages();

    //获取当前页面
    const currentPage = pages[pages.length - 1];

    const navList = [
      "pages/retrieval/site-all/index",
      "pages/retrieval/video/index",
      "pages/index/index",
      "pages/ucenter/index/index",
    ]
    //获取当前页面在navList中的索引
    const currentPageIndex = navList.indexOf(currentPage.route);
    if (currentPageIndex < 0) {
      this.setData({
        canBack: true,
      });
    }


  },
  data: {
    height: app.globalData.navBarHeight,
    statusHeight: app.globalData.statusHeight,
    canBack: false,
  },
  methods: {
    goBack() {
      console.log("goBack");
      const pages = getCurrentPages();
      console.log("pages", pages);
      if (pages.length > 1) {
        wx.navigateBack();
      } else {
        wx.redirectTo({
          url: "/pages/index/index",
        });
      }
    },
  },
});
