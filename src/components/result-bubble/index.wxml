<view class="bubble-wrap" bindlongpress="showModal" >
  <view class="modal-wrap" wx:if="{{recordStatus == 2}}">
    <modal modal-show="{{modalShow}}"
      index="{{index}}"
      item="{{item}}"
      bindmodalleave="modalLeave"></modal>
  </view>

  <view class="create-time">{{item.create}}</view>
  <view class="section-body" data-index="{{index}}" >
    <view class="send-message">
      <view data-id="{{item.id}}"  class="text-content"  data-index="{{index}}" >
        <view class="text-detail  text-detail-{{item.lfrom}}" >{{item.text}}<waiting-icon wx:if="{{recordStatus == 0}}"></waiting-icon></view>
      </view>
      <navigator
        hover-class="navigator-hover"
        data-text="{{item.text}}"
        data-id="{{item.id}}"
        data-index="{{index}}"
        class="edit-icon"
        wx:if="{{editShow}}"
        data-item="{{item}}"
        url="{{'/pages/translate/edit/edit?content='+item.text+'&index='+index}}">
          <image class="edit-icon-img" src="{{edit_icon_path}}" ></image>
      </navigator>
    </view>
    <view class="line-between"  wx:if="{{recordStatus > 0}}"></view>
    <view class="translate-message" >
      <view class="text-content">
        <view class="text-detail text-detail-{{item.lto}}">{{item.translateText}}<waiting-icon wx:if="{{recordStatus == 1}}"></waiting-icon>
      </view>
      </view>
      <view class="play-icon" catchtap="playTranslateVoice" catchtouchstart="playTranslateVoice" wx:if="{{recordStatus == 2}}">
        <play-icon play-type="{{playType}}"></play-icon>
      </view>
    </view>
  </view>
</view>

