.bubble-wrap {
  position: relative;
}
.wait-point {
  display:inline-block;
  width:6px;
  height:6px;
  border-radius:3px;
  background-color: #ddd;
  margin: 0 2px;

}
.loading {
  position: relative;
}

.line-between {
  height: 1px;
  width: 100%;
  background: #F1F1F1;
  overflow: hidden;
  margin: 30rpx 0;
}

.create-time {
  font-size:28rpx;
  color: #B2B2B2;
  margin-bottom:10px;
  display: flex;
  display: -webkit-flex;
  justify-content: center;
  -webkit-justify-content: center;
}

.section-body{
  word-wrap: break-word;
  position: relative;
  width:100%;
  background: #FFFFFF;
  box-shadow: 0 2px 16px 2px rgba(0,0,0,0.03);
  padding:50rpx 60rpx;
  box-sizing: border-box;
  min-height: 260rpx;
}

.text-detail {
  font-size: 36rpx;
  line-height: 1.231;
  vertical-align: text-bottom;
  box-sizing: border-box;
  font-family: "PingFang-SC-Regular","SimSun","Microsoft Yahei";
  font-variant-ligatures: none;
}

.text-detail-en_US {
  line-height: 1.231;
}

.text-detail-zh_CN {
  line-height: 1.41;
}

.translate-message,
.send-message {
  position: relative;
  padding: 0 2px;
}

.send-message  .text-detail {
  color: #9B9B9B;
}

.edit-icon {
  position: absolute;
  display: flex;
  align-items: center;
  right: 8rpx;
  bottom: 0;
  padding: 0 8rpx;
  bottom: 7rpx;
}

.edit-icon-img {
  width:40rpx;
  height:40rpx;
}

.play-icon {
  position: absolute;
  right: 3rpx;
  bottom: 7rpx;
  padding: 0 8rpx;
  display: flex;
  align-items: center;
}

.edit-icon::before
.play-icon::before {
  content:"";
  position:absolete;
  top:-10rpx;
  left:-10rpx;
  bottom:-10rpx;
  right:-10rpx;
}


.text-content {
  margin: 0 48px 0 0;
  box-sizing: border-box;
}


.modal-wrap {
  position: absolute;
  width: 100%;
  box-sizing:border-box;
}
/* 重置navigator样式 */
.navigator-hover {
  background-color: #fff;
}