Component({
  properties: {
    // 数据集合
    datasets: {
      type: Array,
      value: [
        {
          data: [30, 40, 25, 50, 49, 21, 70, 51],
          color: "#5470C6",
        },
        {
          data: [20, 34, 45, 55, 29, 38, 44, 65],
          color: "#91CC75",
        },
      ],
    },
    // X轴标签
    labels: {
      type: Array,
      value: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月"],
    },
  },

  data: {
    showTooltip: false,
    tooltipX: 0,
    tooltipY: 0,
    tooltipDate: "",
    tooltipValue: "",
    chartWidth: 0,
    chartHeight: 0,
    padding: { top: 40, right: 25, bottom: 60, left: 40 },
  },

  lifetimes: {
    attached() {
      wx.getSystemInfo({
        success: (res) => {
          this.systemInfo = res;
        },
      });
    },
    ready() {
      // 确保DOM已渲染完成再初始化图表
      setTimeout(() => {
        this.initChart();
      }, 300);
    },
  },

  methods: {
    initChart() {
      // 现有代码不变
      if (!this.systemInfo) {
        wx.getSystemInfo({
          success: (res) => {
            this.systemInfo = res;
            // 根据屏幕宽度调整边距
            const screenWidth = res.windowWidth;
            // 窄屏幕使用更小的边距
            const leftRightPadding = screenWidth < 375 ? 15 : 25;
            this.setData({
              padding: {
                top: 40,
                right: leftRightPadding,
                bottom: 60,
                left: leftRightPadding + 15, // 左侧需要更多空间显示Y轴标签
              },
            });
            setTimeout(() => this.initCanvasContext(), 100);
          },
        });
      } else {
        // 同样在这里设置自适应边距
        const screenWidth = this.systemInfo.windowWidth;
        const leftRightPadding = screenWidth < 375 ? 15 : 25;
        this.setData({
          padding: {
            top: 40,
            right: leftRightPadding,
            bottom: 60,
            left: leftRightPadding + 15,
          },
        });
        setTimeout(() => this.initCanvasContext(), 100);
      }
    },
    initCanvasContext() {
      const query = wx.createSelectorQuery().in(this);
      // 先查询父元素
      query
        .select(".chart-container") // 假设父元素class为chart-container
        .boundingClientRect()
        .exec((parentRes) => {
          if (!parentRes[0]) {
            console.error("未找到父容器元素");
            return;
          }

          const parentInfo = parentRes[0];

          // 再查询canvas元素
          const canvasQuery = wx.createSelectorQuery().in(this);
          canvasQuery
            .select(".chart-canvas")
            .fields({ node: true })
            .exec((res) => {
              if (!res[0] || !res[0].node) {
                console.error("未找到canvas节点");
                return;
              }

              const canvas = res[0].node;
              const ctx = canvas.getContext("2d");
              if (!ctx) {
                console.error("无法获取canvas上下文");
                return;
              }

              const dpr = this.systemInfo.pixelRatio;

              // 使用父元素的宽高设置canvas
              const width = parentInfo.width;
              const height = parentInfo.height;

              // 设置Canvas尺寸
              canvas.width = width * dpr;
              canvas.height = height * dpr;
              ctx.scale(dpr, dpr);

              this.canvas = canvas;
              this.ctx = ctx;
              this.setData(
                {
                  chartWidth: width,
                  chartHeight: height,
                },
                () => {
                  this.drawChart();
                },
              );
            });
        });
    },
    drawChart() {
      const { ctx } = this;
      const { chartWidth, chartHeight, padding } = this.data;
      const { datasets, labels } = this.properties;

      // 清除画布
      ctx.clearRect(0, 0, chartWidth, chartHeight);

      // 计算实际绘图区域
      const graphWidth = chartWidth - padding.left - padding.right;
      const graphHeight = chartHeight - padding.top - padding.bottom;

      // 绘制标题
      ctx.font = "bold 32rpx sans-serif";
      ctx.fillStyle = "#333";
      ctx.textAlign = "center";
      // ctx.fillText(title, chartWidth / 2, padding.top / 2);

      // 计算Y轴最大值
      let maxValue = 0;
      datasets.forEach((dataset) => {
        const datasetMax = Math.max(...dataset.data);
        if (datasetMax > maxValue) maxValue = datasetMax;
      });

      // 向上取整到最近的10的倍数
      maxValue = Math.ceil(maxValue / 10) * 10;

      // 绘制Y轴和网格线
      ctx.strokeStyle = "#ddd"; // 使用更明显的灰色
      ctx.lineWidth = 1;
      ctx.setLineDash([3, 1.5]); // 设置虚线样式：4像素线段，4像素间隔

      // 绘制5条水平网格线
      for (let i = 0; i <= 5; i++) {
        const y = padding.top + graphHeight - (i / 5) * graphHeight;
        ctx.beginPath(); // 每条线单独开始一个路径
        ctx.moveTo(padding.left, y);
        ctx.lineTo(chartWidth - padding.right, y);
        ctx.stroke(); // 调用stroke()方法绘制当前路径

        // Y轴刻度
        ctx.fillStyle = "#666";
        ctx.font = "24rpx sans-serif";
        ctx.textAlign = "right";
        ctx.textBaseline = "middle";
        ctx.fillText(Math.round((maxValue * i) / 5), padding.left - 10, y);
      }
      // 重置虚线设置，避免影响后续绘制
      ctx.setLineDash([]);
      // 绘制X轴
      ctx.beginPath();
      ctx.strokeStyle = "#ccc";
      ctx.moveTo(padding.left, padding.top + graphHeight);
      ctx.lineTo(chartWidth - padding.right, padding.top + graphHeight);
      ctx.stroke();

      // 绘制X轴标签
      // 增加水平方向的内边距
      const horizontalPadding = graphWidth * 0.05; // 两侧各增加5%的内边距
      const innerGraphWidth = graphWidth - horizontalPadding * 2;
      const xStep = innerGraphWidth / (labels.length - 1);
      ctx.fillStyle = "#666";
      ctx.font = "24rpx sans-serif";
      ctx.textAlign = "center";

      labels.forEach((label, i) => {
        // 起始位置从padding.left + horizontalPadding开始
        const x = padding.left + horizontalPadding + i * xStep;
        ctx.fillText(label, x, padding.top + graphHeight + 30);
      });

      // 绘制每条曲线
      datasets.forEach((dataset) => {
        const points = [];

        // 收集点信息
        dataset.data.forEach((value, i) => {
          const x = padding.left + horizontalPadding + i * xStep;
          const y =
            padding.top + graphHeight - (value / maxValue) * graphHeight;
          points.push({ x, y, value });

          // 保存点信息用于交互
          if (!this.dataPoints) this.dataPoints = [];
          this.dataPoints.push({
            x,
            y,
            value,
            dataset: dataset.label || "",
            color: dataset.color,
          });
        });
        // 添加裁剪区域，防止曲线低于X轴
        ctx.save();
        ctx.beginPath();
        ctx.rect(padding.left, padding.top, graphWidth, graphHeight);
        ctx.clip();
        // 绘制填充的渐变区域
        ctx.beginPath();
        const gradient = ctx.createLinearGradient(
          0,
          padding.top,
          0,
          padding.top + graphHeight,
        );
        // 将透明度从 '80'(50%) 降低到 '40'(25%)
        gradient.addColorStop(0, dataset.color + "20");
        gradient.addColorStop(1, "#ffffff00");

        if (points.length > 0) {
          ctx.moveTo(points[0].x, points[0].y);

          // 使用改进的单调三次插值法绘制曲线
          drawMonotoneCurve(ctx, points, padding.top + graphHeight);

          // 闭合路径到X轴底部
          ctx.lineTo(points[points.length - 1].x, padding.top + graphHeight);
          ctx.lineTo(points[0].x, padding.top + graphHeight);
          ctx.closePath();

          ctx.fillStyle = gradient;
          ctx.fill();
        }

        // 单独绘制曲线
        ctx.beginPath();
        ctx.strokeStyle = dataset.color;
        ctx.lineWidth = 1.5; // 增加线宽，更清晰
        ctx.lineJoin = "round";

        if (points.length > 0) {
          ctx.moveTo(points[0].x, points[0].y);
          drawMonotoneCurve(ctx, points, padding.top + graphHeight);
        }

        ctx.stroke();
        ctx.restore(); // 恢复裁剪区域
      });

      // 改进的单调三次插值法函数
      function drawMonotoneCurve(ctx, points, xAxisY) {
        if (points.length < 2) return;

        const n = points.length;

        // 计算切线方向
        const m = new Array(n);

        // 计算相邻点的斜率
        const dk = new Array(n - 1);
        for (let i = 0; i < n - 1; i++) {
          dk[i] =
            (points[i + 1].y - points[i].y) / (points[i + 1].x - points[i].x);
        }

        // 初始化端点切线
        m[0] = dk[0];
        m[n - 1] = dk[n - 2];

        // 计算内部点的切线
        for (let i = 1; i < n - 1; i++) {
          // 如果相邻斜率符号相反，设置为0
          if (dk[i - 1] * dk[i] <= 0) {
            m[i] = 0;
          } else {
            // 使用调和平均计算切线
            m[i] = (dk[i - 1] + dk[i]) / 2;
          }
        }

        // 绘制曲线段
        for (let i = 0; i < n - 1; i++) {
          const dx = points[i + 1].x - points[i].x;

          // 计算控制点
          let c1x = points[i].x + dx / 3;
          let c1y = points[i].y + (m[i] * dx) / 3;

          let c2x = points[i + 1].x - dx / 3;
          let c2y = points[i + 1].y - (m[i + 1] * dx) / 3;

          // 限制控制点不低于X轴
          c1y = Math.min(c1y, xAxisY);
          c2y = Math.min(c2y, xAxisY);

          ctx.bezierCurveTo(
            c1x,
            c1y,
            c2x,
            c2y,
            points[i + 1].x,
            points[i + 1].y,
          );
        }
      }
    },

    // 处理触摸事件
    handleTouchStart(e) {
      this.findNearestPoint(e);
    },

    handleTouchMove(e) {
      this.findNearestPoint(e);
    },

    handleTouchEnd() {
      this.setData({ showTooltip: false });
    },

    findNearestPoint(e) {
      if (!this.dataPoints) return;

      const touch = e.touches[0];
      const touchX = touch.x;
      const touchY = touch.y;

      let minDistance = Infinity;
      let nearestPoint = null;

      this.dataPoints.forEach((point) => {
        const distance = Math.sqrt(
          Math.pow(touchX - point.x, 2) + Math.pow(touchY - point.y, 2),
        );

        if (distance < minDistance) {
          minDistance = distance;
          nearestPoint = point;
        }
      });

      if (minDistance < 40) {
        this.setData({
          showTooltip: true,
          tooltipX: nearestPoint.x,
          tooltipY: nearestPoint.y - 30,
          tooltipDate: nearestPoint.label,
          tooltipValue: `${nearestPoint.dataset}: ${nearestPoint.value}`,
        });
      } else {
        this.setData({ showTooltip: false });
      }
    },
  },
});
