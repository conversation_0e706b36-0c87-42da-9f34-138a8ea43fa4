App({
  onLaunch: function (options) {
    const menuInfo = wx.getMenuButtonBoundingClientRect();
    let accountInfoSync = wx.getAccountInfoSync();
    this.globalData.statusHeight = menuInfo.top;
    this.globalData.navBarHeight = menuInfo.bottom - menuInfo.top;
    this.globalData.paddingHeight =
      this.globalData.statusHeight + this.globalData.navBarHeight;
    console.log("app.onLaunch", options);
    this.globalData.user.scene = options.scene;
    this.globalData.sysinfo = wx.getSystemInfoSync();
    const updateManager = wx.getUpdateManager();
    updateManager.onCheckForUpdate(function (res) {
      // 请求完新版本信息的回调
      console.log(res.hasUpdate);
    });
    updateManager.onUpdateReady(function () {
      wx.showModal({
        title: "更新提示",
        content: "新版本已经准备好，是否重启应用？",
        success: function (res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启。
            updateManager.applyUpdate();
          }
        },
      });
    });
    updateManager.onUpdateFailed(function () {
      wx.showModal({
        title: "更新提示",
        content: "新版本下载失败",
        showCancel: false,
      });
    });
    this.globalData.appId = accountInfoSync.miniProgram.appId;
  },
  globalData: {
    sysinfo: {},
    cosDomain: "https://cos.ai-market.top/",
    appId: "",
    statusHeight: void 0,
    navBarHeight: void 0,
    userInfo: {
      userId: 0,
      userCode: 0,
    },
    trial: {},
    user: {
      scene: "0",
      token: "",
      openId: "",
      userCode: "0",
    },
    share_config: {
      imageUrl: "https://k-cos.ai-market.top/wx/base/share.png",
      path: "/pages/index/index",
      title: "酷拍侠 - Show出最酷的瞬间",
    },
    secretKey: "6e60dfe414995d50",
  },
  // 权限询问
  getRecordAuth: function () {
    wx.getSetting({
      success(res) {
        console.log("succ");
        console.log(res);
        if (!res.authSetting["scope.record"]) {
          wx.authorize({
            scope: "scope.record",
            success() {
              // 用户已经同意小程序使用录音功能，后续调用 wx.startRecord 接口不会弹窗询问
              console.log("succ auth");
            },
            fail() {
              console.log("fail auth");
            },
          });
        } else {
          console.log("record has been authed");
        }
      },
      fail(res) {
        console.log("fail");
        console.log(res);
      },
    });
  },
});
