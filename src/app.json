{"pages": ["pages/index/index", "pages/login/index", "pages/privacy/index", "pages/ucenter/index/index", "pages/ucenter/mix-record/index", "pages/ucenter/live-split/index", "pages/ucenter/order-list/index", "pages/ucenter/order-detail/index", "pages/retrieval/video/index", "pages/retrieval/site-all/index", "pages/retrieval/site-search/index", "pages/retrieval/video-result/index", "pages/retrieval/generate-video/index", "pages/retrieval/play-template/index", "pages/retrieval/play/index", "pages/retrieval/play-video/index", "pages/retrieval/template-home/index", "pages/yinge/goods-detail/index", "pages/yinge/search/index", "pages/yinge/webview/index", "pages/yinge/cart/index", "pages/yinge/success/index", "pages/business/index", "pages/mywebview/index", "pages/cloud-travel/index", "pages/ucenter/add-address/index", "pages/ucenter/address-list/index", "pages/ucenter/refund-details/index", "pages/ucenter/yg-express/index", "pages/ucenter/setting/index", "pages/ucenter/area-picker/index", "pages/common/play/index"], "subpackages": [{"root": "store", "pages": ["store/index", "detail/index"]}, {"root": "play-live", "pages": ["living/index", "living-mult/index", "living-mult-v2/index", "site-live/index", "live-list/index"]}], "tabBar1": {"color": "#aaaaaa", "selectedColor": "#333333", "list": [{"iconPath": "images/tab-bar/water.png", "selectedIconPath": "images/tab-bar/water-select.png", "pagePath": "pages/extraction-audio/index", "text": "录音识别"}, {"iconPath": "images/tab-bar/tts.png", "selectedIconPath": "images/tab-bar/tts-select.png", "pagePath": "pages/ocr/index", "text": "图片识别"}, {"pagePath": "pages/ucenter/index/index", "iconPath": "images/tab-bar/ic_menu_me_nor.png", "selectedIconPath": "images/tab-bar/ic_menu_me_pressed.png", "text": "我的"}]}, "window": {"navigationStyle": "custom", "backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black"}, "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序效果展示"}}, "plugins": {}, "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "optimization": {"subPackages": true}}