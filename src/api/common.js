import request from "./request";

// 获取通用配置
export function getCommonConfigApi(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/common-config/get?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}

// 获取省市区
export function getAreaListApi() {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/area/tree";

  return request.get(url, {});
}
