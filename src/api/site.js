import request from "./request";

// 查询站点列表
export function querySiteListApi(data) {
  //data.open = 1;
  return request.post("/app-api/system/site/page", data);
}

// 查询站点详情
export function querySiteDetailApi(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/site/get?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}

// 根据经纬度查询站点
export function searchPositionApi(data) {
  //data.open = 1;
  return request.post("/app-api/system//site/position/search", data);
}

// 查询设备
export function querySitDevListApi(data) {
  return request.post("/app-api/system/site-dev/page", data);
}

// 查询设备 直播列表
export function querySitDevLivingListApi(data) {
  return request.post("/app-api/system/site-dev/page/living", data);
}

// 查询站点模板列表（有状态）
export function querySitTemplateListApi(data) {
  return request.post("/app-api/system/site-template/query/mix", data);
}

// 查询站点模板列表（无状态）
export function querySitTemplateListNoStatusApi(data) {
  return request.post("/app-api/system/site-template/page", data);
}

// 查询站点模板详情
export function queryOneSitTemplateApi(data) {
  return request.post("/app-api/system/site-template/get", data);
}

// 查询站点模板素材
export function querySitTemplateMaterialListApi(data) {
  return request.post("/app-api/system/site-template-material/page", data);
}

// 获得设备表（摄像头等）
export function getDeviceStatus(data) {
  let url = "/app-api/system/site-dev/get?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);
  return request.get(url, data);
}


// 直播回放
export function liveReplay(data) {
  let url = "/app-api/system/site-dev/ali/live/replay?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);
  return request.get(url, data);
}
