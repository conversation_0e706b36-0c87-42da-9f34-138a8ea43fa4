import request from "./request";

// 创建订单
export function createOrderApi(data) {
  return request.post("/app-api/trade/mix-video-order/createOrder", data);
}

// 创建通用订单
export function commonCreateOrderApi(data) {
  return request.post("/app-api/trade/mix-video-order/commonCreateOrder", data);
}

// 查询订单
export function queryOrderApi(data) {
  return request.get("/app-api/trade/mix-video-order/page", data);
}

// 创建yinge订单
export function yingeCreateOrderApi(data) {
  return request.post("/app-api/trade/mix-video-order/yinGeCreateOrder", data);
}

// 取消订单
export function cancelYingeOrderApi(data) {
  return request.post("/app-api/trade/mix-video-order/cancelYinGeOrder", data);
}

// 确认交易订单收货
export function confirmYingeOrderApi(data) {
  return request.get("/app-api/trade/mix-video-order/yinge/completed", data);
}

// 查询yinge订单
export function queryYingeOrderApi(data) {
  return request.post("/app-api/trade/mix-video-order/yinge/page", data);
}

// 查询yinge订单详情
export function queryYingeOrderDetailApi(data) {
  return request.get("/app-api/trade/mix-video-order/yinge/detail", data);
}

// 查询yinge订单物流详情
export function getAYingExpress(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/trade/yin-ge-express/get?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}

// 混剪创建订单
export function sliceVideoCreateOrderApi(data) {
  return request.post(
    "/app-api/trade/mix-video-order/asset-pkg/create-order",
    data,
  );
}
