import {
  BASE_URL,
  KPX_BASE_URL,
  SUCCESS,
  EXPIRE_LOGIN,
  version,
  EXCEED_LIMIT,
} from "../config/index";
const sign = require("@/utils/sign.js");

const app = getApp();

class Request {
  get(url, data) {
    return this.request({ url, method: "GET", data });
  }

  post(url, data) {
    return this.request({ url, method: "POST", data });
  }

  put(url, data) {
    return this.request({ url, method: "PUT", data });
  }

  delete(url, data) {
    return this.request({ url, method: "DELETE", data });
  }

  // put delete等请求 如有需要再添加

  // 处理头部 参数
  getHeaders(upload) {
    const headers = {
      version,
    };

    if (upload) {
      headers["Conetent-Type"] = "multipart/form-data";
    }
    return headers;
  }

  request(options) {
    options.header = this.getHeaders(options.upload);
    options.data.appId = app.globalData.appId;
    options.data.requestId = this.geneUuid();
    options.header["lp-token"] = app.globalData.user.token;
    options.header["Authorization"] = app.globalData.user.token;
    options.header["miniAppId"] = app.globalData.appId;
    options.header["tenant-id"] = "1";
    options.data.userCode = app.globalData.user.userCode;
    let signedData = sign.sign(options.data);
    options.data = signedData;

    return this.interceptRequest(options);
  }

  // 请求拦截器
  interceptRequest(options) {
    if (options.upload) {
      return this.uploadRequest(options);
    }
    return new Promise((resolve, reject) => {
      let base_url = KPX_BASE_URL;
      if (options.url.includes("zero")) {
        base_url = BASE_URL;
      }
      //console.log("base_url", base_url);

      wx.request({
        url: base_url + options.url,
        header: options.header,
        method: options.method,
        data: options.data || {},
        timeout: 20000,
        success({ data = {} }) {
          wx.hideLoading();
          if (data.code === SUCCESS || data.code === 0) {
            resolve(data);
          } else if (data.code === EXPIRE_LOGIN) {
            wx.hideLoading();
            wx.navigateTo({
              url: "/pages/index/index",
            });
          } else if (data.code === EXCEED_LIMIT) {
            wx.hideLoading();
            reject(data);
          } else {
            wx.hideLoading();
            console.log(
              "接口调用失败",
              JSON.stringify(
                {
                  请求路径: base_url + options.url,
                  请求方式: options.method,
                  请求参数: options.data,
                  响应参数: data,
                },
                null,
                2,
              ),
            );
            wx.showToast({
              title: data.msg,
              icon: "none",
            });
            resolve(data);
          }
        },
        fail(err) {
          wx.hideLoading();
          wx.showToast({
            title: err,
            icon: "none",
          });
        },
      });
    });
  }

  // 上传文件
  uploadRequest(options) {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: options.url,
        filePath: options.path,
        header: options.header,
        name: "file",
        success(res) {
          resolve(res);
        },
        fail(err) {
          reject(err);
        },
      });
    });
  }

  geneUuid() {
    var s = [];
    var hexDigits = "0123456789abcdef";
    for (var i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = "-";

    var uuid = s.join("");
    return uuid;
  }
}

export default new Request();
