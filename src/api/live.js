import request from "./request";

// 创建直播任务
export function createLiveTaskApi(data) {
  return request.post("/app-api/trade/live-user-task/save", data);
}

// 完成直播任务
export function completeLiveTaskApi(data) {
  return request.put("/app-api/trade/live-user-task/update", data);
}

// 查询直播任务列表
export function queryLiveTaskListApi(data) {
  return request.post("/app-api/trade/live-user-task/page", data);
}

// 查询直播切片列表
export function queryLiveSplitListApi(data) {
  return request.post("/app-api/trade/live-split-data/page", data);
}

// 查询单个直播切片
export function queryOneSplitApi(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/trade/live-split-data/get?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}

// 查询直播播放记录
export function queryWatchLiveRecordApi(data) {
  return request.post("/app-api/member/watch-live-record/page", data);
}

// 删除直播播放记录
export function deleteWatchLiveRecordApi(data) {
  return request.post("/app-api/member/watch-live-record/clear", data);
}

// 添加直播播放记录
export function addWatchLiveRecordApi(data) {
  return request.post("/app-api/member/watch-live-record/create", data);
}

// 提交切片任务
export function submitSlicingTask(data) {
  return request.post("/app-api/system/site-dev-live-slice/submit-job", data);
}

// 获得设备直播切片分页
export function slicingTaskPage(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/site-dev-live-slice/page?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}

// 删除设备直播切片
export function deleteSlicingTask(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/site-dev-live-slice/delete?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.delete(url, data);
}

// 获取权益
export function acquiringRights(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/site-dev-live-slice/get-benefit?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);
  return request.get(url, data);
}
