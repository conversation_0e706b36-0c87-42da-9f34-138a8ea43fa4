import request from "./request";

// 是否是场地管理员
export function isSiteAdminApi(data) {
  let url = "/app-api/member/site-admin/is-admin";
  return request.get(url, data);
}

// 获得管理员关联的场所分页
export function pageAdminSiteList(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/admin-site-relation/page?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);
  return request.get(url, data);
}

// 获得管理员关联的场所设备分页
export function pageAdminSiteDevicesList(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/admin-site-dev-relation/page?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);
  return request.get(url, data);
}

// 更新设备表（摄像头等）
export function updateDevice(data) {
  return request.put("/app-api/system/site-dev/update-by-id", data);
}

// 流量数据
export function flowsData(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/member/watch-live-record/site-user-statistics?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);
  return request.get(url, data);
}

// 交易数据
export function transactionData(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/trade/mix-video-order/site-trade-statistics?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);
  return request.get(url, data);
}
