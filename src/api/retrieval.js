import request from "./request";

// 视频检索
export function retrievalApi(data) {
  console.log("retrievalApi", data);
  return request.post("/app-api/system/mix-video-task/createTask", data);
}

// 查询视频检索任务
export function queryMixVideoTaskApi(data) {
  return request.post("/app-api/system/mix-video-task/query", data);
}

// 查询单个视频检索任务
export function getOneMixVideoTaskApi(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/mix-video-task/get?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}

// 查询点位切片视频
export function querySpotSliceVideo(data) {
  return request.post("/app-api/system/spot-slice-video/page", data);
}

// 查询单个视频
export function getOneSpotSliceVideoApi(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/spot-slice-video/get?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}

/* 查询 通用模板列表 site-common-template
{
    "pageNo": 1,
    "pageSize": 10,
    "materialCount":[0,99],
    "templateTime":[0,99]
}
 */
export function querySiteCommonTemplate(data) {
  return request.post("/app-api/system/site-common-template/app/page", data);
}

// 查询单个通用模板
export function queryOneSiteCommonTemplateApi(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/system/site-template/getSiteTemplateNew?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}
