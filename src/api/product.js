import request from "./request";




// 商品列表
export function productListApi(data) {
  return request.post("/app-api/product/spu/page_post", data);
}

// 商品详情
export function productDetailApi(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/product/spu/get-detail?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}