import request from "./request";

// 查询个人信息
export function currentApi(data) {
  return request.post("/zero/user/current", data);
}

// 登陆 - kpx
export function loginByphoneApi(data) {
  return request.post("/app-api/member/auth/weixin-mini-app-login", data);
}

// 登陆 - openid - kpx
export function loginByOpenidApi(data) {
  console.log("loginByOpenidApi", data);
  return request.post("/app-api/member/auth/mini-openid-login", data);
}

// 人脸识别 - 分页查询
export function facePageApi(data) {
  return request.post("/app-api/system/face/page", data);
}

// 获取收货地址
export function getAddressListApi(data) {
  let url = "/app-api/member/address/list";
  return request.get(url, data);
}

// 添加收货地址
export function createAddressApi(data) {
  let url = "/app-api/member/address/create";
  return request.post(url, data);
}

// 更新收货地址
export function updateAddressApi(data) {
  let url = "/app-api/member/address/update";
  return request.put(url, data);
}

// 查询地址详情
export function getAddressDetailApi(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/member/address/get?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.get(url, data);
}

// 删除地址
export function deleteAddress(data) {
  //从data 中获取所有参数，然后拼成一个url
  let url = "/app-api/member/address/delete?";
  for (let key in data) {
    url += key + "=" + data[key] + "&";
  }
  url = url.slice(0, -1);

  return request.delete(url, data);
}
