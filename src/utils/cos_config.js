var COS = require("cos-wx-sdk-v5.min");
import { getTmpSecretApi } from "@/api/cos";
const nowTime = new Date();
/**
 *
 * @param {*} file              文件
 * @param {string} fileName     文件名，例如：'1.jpg'或'qiun-wx-ucharts.txt'
 * @param {object} res          COS上传参数{TmpSecretId, TmpSecretId, SecurityToken, Bucket, region}
 * @returns
 */
export const uploadFileToCos = (file, fileName, scene) => {
  // 创建桶的实例
  const cos = new COS({
    // 必选参数
    getAuthorization: (options, callback) => {
      getTmpSecretApi({
        location: "index",
      }).then(({ data }) => {
        const obj = {
          TmpSecretId: data.tmpSecretId,
          TmpSecretKey: data.tmpSecretKey,
          SimpleUploadMethod: "putObject",
          ExpiredTime: data.expiredTime,
          SecurityToken: data.sessionToken,
        };
        callback(obj);
      });
    },
  });

  wx.showLoading({
    title: "上传中，请稍候",
  });

  // 上传图片
  return new Promise((resolve, reject) => {
    ///* 存储在桶里的对象键（例如1.jpg，a/b/qiun-wx-ucharts.txt），必须字段。也就是在桶文件中的位置，如果想要放在text文件下，可以定义为text/文件名。文件地址可以前端自己定义，也可以后端返回。 */,
    if (scene == undefined) {
      scene = "video";
    }

    let key =
      "/lp/" +
      scene +
      "/" +
      `${nowTime.getFullYear()}${nowTime.getMonth() + 1}/${fileName}`;
    cos.uploadFile(
      {
        Bucket: "video-extract-1331373411",
        Region: "ap-shanghai" /* 存储桶所在地域，必须字段 */,
        Key: key,
        FilePath: file,
      },
      function (err, data) {
        // 这里我用的是回调函数的形式，也可以用promise方式
        if (err) {
          console.log("上传失败", err);
          reject(err);
        } else {
          console.log("上传成功", data);
          data["Key"] = key;
          resolve(data);
        }
      },
    );
  });
};
