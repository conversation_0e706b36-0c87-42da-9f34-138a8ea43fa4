var sha1Util = require("./sha1.js"); //加密算法引入
const app = getApp();

function jsonVAL(json) {
  var v = "";
  for (var i in json) {
    v += json[i];
  }
  return v;
}

function getTimestamp() {
  return Date.parse(new Date()) / 1000;
}

function mtRand(min, max) {
  var result = Math.random() * (max - min + 1) + min;
  return parseInt(result);
}

function sign(json) {
  //console.log("json=", json);
  json.timestamp = getTimestamp();
  json.rand = mtRand(100000, 999999);
  json.appkey = app.globalData.secretKey;

  let valueArray = [];
  for (let key in json) {
    valueArray.push(json[key]);
  }
  valueArray.sort();

  let signStr = jsonVAL(valueArray);

  json.sign = sha1Util.sha1(signStr);
  delete json.appkey;
  return json;
}

var x = {
  sign: sign,
};

module.exports = x;
