function formatTime(date) {
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();

  var hour = date.getHours();
  var minute = date.getMinutes();
  var second = date.getSeconds();

  return (
    [year, month, day].map(formatNumber).join("-") +
    " " +
    [hour, minute, second].map(formatNumber).join(":")
  );
}

function formatTimeWithoutSecond(date) {
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();

  var hour = date.getHours();
  var minute = date.getMinutes();
  var second = date.getSeconds();
  return (
    [year, month, day].map(formatNumber).join("-") +
    " " +
    [hour, minute].map(formatNumber).join(":")
  );
}

function validatePhone(phone) {
  const re = /^((13|14|15|16|17|18|19)[0-9]{1}\d{8})$/;
  return re.test(phone);
}

/**
 * 时间戳转化为年 月 日 时 分 秒
 * number: 传入时间戳
 * format：返回格式，支持自定义，但参数必须与formateArr里保持一致
 */
function nformatTime(number, format) {
  var formateArr = ["Y", "M", "D", "h", "m", "s"];
  var returnArr = [];

  var date = new Date(number * 1000);
  returnArr.push(date.getFullYear());
  returnArr.push(formatNumber(date.getMonth() + 1));
  returnArr.push(formatNumber(date.getDate()));

  returnArr.push(formatNumber(date.getHours()));
  returnArr.push(formatNumber(date.getMinutes()));
  returnArr.push(formatNumber(date.getSeconds()));

  for (var i in returnArr) {
    format = format.replace(formateArr[i], returnArr[i]);
  }
  return format;
}

/**
 * 封封微信的的request
 */
function request(
  url,
  data = {},
  method = "GET",
  contentType = "application/json",
) {
  return new Promise(function (resolve, reject) {
    wx.request({
      url: url,
      data: data,
      method: method,
      header: {
        "Content-Type": contentType,
      },
      success: function (res) {
        if (res.statusCode == 200) {
          if (res.data.errno == 401) {
            //需要登录后才可以操作
            wx.showModal({
              title: "",
              content: "请先登录",
              success: function (res) {
                if (res.confirm) {
                  wx.removeStorageSync("userInfo");
                  wx.removeStorageSync("token");
                  wx.switchTab({
                    url: "/pages/ucenter/index/index",
                  });
                }
              },
            });
          } else {
            resolve(res.data);
          }
        } else {
          reject(res.errMsg);
        }
      },
      fail: function (err) {
        reject(err);
      },
    });
  });
}

function getQueryString(url, name) {
  var reg = new RegExp("(^|&|/?)" + name + "=([^&|/?]*)(&|/?|$)", "i");
  var r = url.substr(1).match(reg);
  if (r != null) {
    return r[2];
  }
  return null;
}

function showErrorToast(msg) {
  wx.showToast({
    title: msg,
    icon: "error",
  });
}

function showToast(msg) {
  wx.showToast({
    title: msg,
    icon: "none",
  });
}

function showSuccessToast(msg) {
  wx.showToast({
    title: msg,
  });
}

function accSub(arg1, arg2) {
  var r1, r2, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2)); //last modify by deeka //动态控制精度长度
  n = r1 >= r2 ? r1 : r2;
  return ((arg1 * m - arg2 * m) / m).toFixed(n);
}

/**
 * 下载
 */
function downloadToAlbum(url) {
  // 首先验证 URL 是否有效
  if (!url || typeof url !== "string") {
    wx.showToast({
      title: "下载链接无效",
      icon: "error",
    });
    return;
  }

  wx.getSetting({
    withSubscriptions: true,
    success: (res) => {
      if (res.authSetting["scope.writePhotosAlbum"] === undefined) {
        wx.authorize({
          scope: "scope.writePhotosAlbum",
          success() {
            res.authSetting["scope.writePhotosAlbum"] = true;
            console.log("打开了授权, 重新设置了权限值");
            doDownloadToAlbum(url);
          },
          fail(err) {
            wx.showToast({
              title: "授权失败",
              icon: "none",
            });
            console.log("授权失败", err);
          },
        });
        return;
      }

      console.info(
        "scope.writePhotosAlbum",
        res.authSetting["scope.writePhotosAlbum"],
      );
      if (res.authSetting["scope.writePhotosAlbum"]) {
        wx.showLoading({
          title: "请稍后",
        });
        doDownloadToAlbum(url);
      } else {
        wx.showModal({
          cancelColor: "cancelColor",
          title: "提示",
          content: "请开启相册访问权限",
          success: (res) => {
            if (res.confirm)
              wx.openSetting({
                withSubscriptions: true,
              });
          },
        });
      }
    },
    fail: (err) => {
      console.error("获取设置失败", err);
      wx.showToast({
        title: "获取权限失败",
        icon: "error",
      });
    },
  });
}

function doDownloadToAlbum(url) {
  console.log("下载视频", url);
  wx.showLoading({
    title: "请稍候",
  });
  wx.downloadFile({
    url: url,
    success: (res) => {
      if (res.statusCode === 200) {
        //保存到相册
        wx.saveVideoToPhotosAlbum({
          filePath: res.tempFilePath,
          success: (res) => {
            console.info(res);
            wx.showToast({
              title: "保存已到相册",
              icon: "success",
            });
          },
          fail: (res) => {
            wx.showToast({
              title: res.errMsg || "保存失败",
              icon: "error",
            });
            console.info("保存视频失败", res);
          },
          complete: (res) => {
            wx.hideLoading();
          },
        });
      } else {
        wx.hideLoading();
        wx.showToast({
          title: "下载失败",
          icon: "error",
        });
        console.error("下载失败，状态码：", res.statusCode);
      }
    },
    fail: (err) => {
      wx.hideLoading();
      wx.showToast({
        title: "下载失败",
        icon: "error",
      });
      // wx.showModal({
      //   title: "下载失败",
      //   content: JSON.stringify(err, null, 2),
      //   showCancel: false,
      //   confirmText: "确定"
      // });
      console.error("下载文件失败", JSON.stringify(err, null, 2));
    },
  });
}

/**
 * 下载 并 保存到disk
 * @param url
 */
function downloadAndSave2Disk(url) {
  console.log("下载视频", url);
  wx.showLoading({
    title: "请稍候",
  });
  wx.downloadFile({
    url: url,
    success: (res) => {
      //保存到相册
      wx.saveFileToDisk({
        filePath: res.tempFilePath,
        success: (res) => {
          console.info(res);
          wx.showToast("保存成功");
        },
        fail: (res) => {
          wx.showToast({
            title: res.errMsg,
          });
          console.info("保存视频失败", res);
        },
        complete: (res) => {
          wx.hideLoading();
        },
      });
    },
  });
}

function extractUrl(s) {
  var reg = /(http:\/\/|https:\/\/)((\w|=|\?|\.|\/|&|-)+)/g;
  var reg =
    /(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
  s = s.match(reg);
  return s && s.length ? s[0] : null;
}

function recordTime(date) {
  var month = date.getMonth() + 1;
  var day = date.getDate();

  var hour = date.getHours();
  var minute = date.getMinutes();

  return (
    [month, day].map(formatNumber).join("/") +
    " " +
    [hour, minute].map(formatNumber).join(":")
  );
}

const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : "0" + n;
};

//清除配音tag
function lenWithOutVoiceTag(str) {
  let primitiveTtsTextWithoutTag = str.replace(/#.*#/g, "");
  primitiveTtsTextWithoutTag = primitiveTtsTextWithoutTag.replace(
    /\[(\d*\.\d*)秒\]/g,
    "",
  );
  return primitiveTtsTextWithoutTag.length;
}

/**
 * 替换配音tag
 *  暂停替换    [0.5秒] ->  <speak><break time=\"500ms\"/></speak>  其中0.5要转成500ms
 *  多音字替换  里#li3# -> <speak><phoneme alphabet=\"py\" ph=\"li3\">里</phoneme></speak>
 * @param str
 */
function replaceVoiceTag(primitiveStr) {
  let ttsText = primitiveStr.replace(/\[(\d*\.\d*)秒\]/g, (word, speed) => {
    return `<speak><break time="${speed * 1000}ms"/></speak>`;
  });

  ttsText = ttsText.replace(/(\S{1})#[a-z]+([1-5])#/g, (match) => {
    let parts = match.split("#");
    return `<speak><phoneme alphabet="py" ph="${parts[1]}">${parts[0]}</phoneme></speak>`;
  });

  return ttsText;
}

function throttle(fn, time) {
  if (!time && time !== 0) {
    time = 200;
  }
  let _lastTime = null;
  return function () {
    let _nowTime = new Date().getTime();
    if (_nowTime - _lastTime > time || !_lastTime) {
      fn.apply(this, arguments);
      _lastTime = _nowTime;
    }
  };
}
function formatterMinute(second) {
  const timeM = Math.floor(second / 60);
  const timeS = second % 60;
  return `${timeM > 9 ? timeM : "0" + timeM}:${timeS > 9 ? timeS : "0" + timeS}`;
}

function geneUuid() {
  var s = [];
  var hexDigits = "0123456789abcdef";
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = "-";

  var uuid = s.join("");
  return uuid;
}

function requestSuccess(code) {
  return code === 200 || code === 0;
}

module.exports = {
  formatterMinute,
  throttle,
  nformatTime,
  showErrorToast,
  showToast,
  showSuccessToast,
  validatePhone,
  getQueryString,
  accSub,
  geneUuid,
  formatTime: formatTime,
  recordTime: recordTime,
  downloadToAlbum,
  downloadAndSave2Disk,
  formatTimeWithoutSecond,
  lenWithOutVoiceTag,
  replaceVoiceTag,
  extractUrl,
  requestSuccess,
};
