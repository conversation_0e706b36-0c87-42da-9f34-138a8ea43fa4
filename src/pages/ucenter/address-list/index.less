page {
  background-color: #ebfbfc;

}

.result {
  padding:24rpx 0 40rpx;
  font-size: 32rpx;
  color: #052031;

}

.result_m{
  padding-bottom: 220rpx;
}
.footerBtn{
  bottom: 0;
  left: 0;
  width: 100%;
  position: fixed;
  height: 104rpx;
  background: #fff;
  padding: 24rpx 0 60rpx;
  .video-btn {
    width: calc(100% - 100rpx);
    height: 100rpx;
    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    border-radius: 100rpx 100rpx 100rpx 100rpx;

  }
}

.empty-view{
  display: flex;
  padding-top: 100rpx;
  align-items: center;
}
.empty-icon{
  width:426rpx;
  margin: 0 auto;
}
.address-li{
  background: #fff;
  box-shadow: 0px 10rpx 20rpx 0px rgba(54,127,137,0.1);
  padding: 24rpx;
  font-size: 28rpx;
  color: #052031;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  .left-text{
    flex: 1;
    .name{
      font-size: 32rpx;
      margin-bottom: 10rpx;
      display: flex;
      align-items: center;
      // justify-content: center;
    }
    .tel{
      margin: 0 10rpx;
      display: inline-block;
    }
    .default-icon{
      color: #FF4F28;
      background: rgba(255,79,40,0.1);
border-radius: 10rpx;
border: 1rpx solid #FF4F28;
font-size: 22rpx;
display: inline-block;
padding: 2rpx 14rpx 4rpx;
    }
  }
  .right-edit{
    text-align: right;
    width: 70rpx;
    .edit-img{
      width: 48rpx;
    }
  }
}
