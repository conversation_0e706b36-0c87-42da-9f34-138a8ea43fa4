const { getAddressListApi } = require("@/api/user");
const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "我的地址",
    },
    addressList: [],
    isEmpty: true,
    fromPage: "",
  },
  onLoad(options) {
    console.log(" order options", options);
    this.setData({
      fromPage: options.from || "",
    });
    this.getAddressList();
  },
  onShow(callback) {
    console.log("onShow onReady", callback);
    this.getAddressList();
  },
  getAddressList() {
    getAddressListApi({ a: 1 }).then((res) => {
      console.log("getAddressList", res);
      let addressList = res.data;
      console.log("addressList.length ", addressList.length);
      let isEmpty = false;
      if (addressList.length == 0) {
        isEmpty = true;
      }
      this.setData({
        addressList: addressList,
        isEmpty: isEmpty,
      });
    });
  },
  gotoAddAddress() {
    wx.navigateTo({
      url: "/pages/ucenter/add-address/index",
    });
  },
  editAddress(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ucenter/add-address/index?id=${id}`,
    });
  },
  selectAddress(e) {
    const index = e.currentTarget.dataset.index;
    const address = this.data.addressList[index];

    if (this.data.fromPage === "cart") {
      // 获取页面栈
      const pages = getCurrentPages();
      // 获取上一个页面
      const prevPage = pages[pages.length - 2];

      // 设置上一个页面的地址信息
      prevPage.setData({
        currentAddress: address,
      });

      // 返回上一页
      wx.navigateBack();
    }
  },
});
