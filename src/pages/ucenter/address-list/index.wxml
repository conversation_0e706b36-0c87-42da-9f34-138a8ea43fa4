<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result" style="margin-top: {{ paddingHeight }}px">
<view wx:if="{{ !isEmpty }}">
  <view class="result_m">
<view class="address-li" wx:for="{{addressList}}" wx:key="id" bindtap="selectAddress" data-index="{{index}}">
<view class="left-text">
<view class="name">{{item.name}}<text class="tel">{{item.mobile}}</text>
<text class="default-icon" wx:if="{{item.defaultStatus}}">默认</text>
</view>
<text>{{item.detailAddress}}</text>
</view>
<view class="right-edit">
  <image class="edit-img" mode="widthFix" catch:tap="editAddress" data-id="{{item.id}}" src="/images/addaddress/icon-3.png" />
</view>
</view>
  </view>
  <view class="footerBtn">
    <button class="video-btn" bindtap = "gotoAddAddress">新增收货地址</button>

  </view>
</view>
<!-- 没有地址 -->
<view wx:else="">
  <view class="result_m">
  <view class="empty-view">
    <image class="empty-icon" mode="widthFix"  src="https://k-cos.ai-market.top/wx/address/none.png" /></view>
  </view>
  <view class="footerBtn">
    <button class="video-btn" bindtap = "gotoAddAddress">新增收货地址</button>
  </view>
</view>
</view>
