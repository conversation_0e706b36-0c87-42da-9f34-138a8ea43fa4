<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result " style="margin-top: {{ paddingHeight }}px">
  <view class="userinfo" wx:if="{{ login == true }}">
    <view class="container-avatar">

      <image class="userinfo-avatar" src="{{ userInfo.avatar }}" background-size="cover"></image>

    </view>
    <view class='marleft'>
      <view class="usrtop-flex">
        <view class="userinfo-nickname"><text class="name">{{ userInfo.username }}</text></view>
        <view class="userinfo-mobile"><text class="id_box">ID：{{ userInfo.userCode }}</text> <text class="copy" bindtap='copyId'>复制</text></view>
      </view>
      <view class="merchant" bindtap="merchantBackend" wx:if="{{ isSiteAdmin}}">
        <text>商户后台</text>
        <image class="userinfo-arrow" src="/images/home/<USER>" mode="widthFix"></image>
      </view>
    </view>
  </view>

  <view class="personal" wx:if="{{ login == false }}">

    <view class="personal-login">
      <view class="login-left">
        <view class="login-left_title">欢迎来到 酷拍侠</view>
        <view class="login-left_tips">更好的会员服务，注册登录即可体验</view>
      </view>
      <view class="login-btn"  bindtap="handleQueryAuth">
        <block> 立即登录 </block>
      </view>
    </view>

  </view>



  <view class="user-nav">
    <view class="title">
      我的订单
      <view class="more" bindtap="gotoOrderList" data-status="-1">全部订单
        <image class="right_icon" src="/images/store/arrow-right.png" />
      </view>
    </view>
    <view class="user-list-five">
      <view class="list" bindtap="gotoOrderList" data-status="0">
        <image class="img-icon" src="/images/addaddress/user-nav-1.png" />待付款
      </view>
      <view class="list" bindtap="gotoOrderList" data-status="10">
        <image class="img-icon" src="/images/addaddress/user-nav-2.png" />待发货
      </view>
      <view class="list" bindtap="gotoOrderList" data-status="20">
        <image class="img-icon" src="/images/addaddress/user-nav-3.png" />待收货
      </view>
      <view class="list" bindtap="gotoOrderList" data-status="30">
        <image class="img-icon" src="/images/addaddress/user-nav-4.png" />已完成
      </view>
      <view class="list" bindtap="gotoOrderList" data-status="40">
        <image class="img-icon" src="/images/addaddress/user-nav-5.png" />已取消
      </view>
    </view>
  </view>

  <!-- 我的服务 -->

  <view class="user-nav">
    <view class="title">
      我的服务

    </view>
    <view class="user-list-five">
      <view class="four" bindtap="showServiceWin">
        <image class="img-icon" src="/images/addaddress/user-nav-6.png" />联系客服
      </view>
      <view class="four" bindtap="gotoMixRecord">
        <image class="img-icon" src="/images/addaddress/user-nav-7.png" />我的Vlog
      </view>
      <view class="four" bindtap="gotoLiveSplit">
        <image class="img-icon" src="/images/addaddress/user-nav-8.png" />直播切片
      </view>
      <view class="four" bindtap="gotoAddressList">
        <image class="img-icon" src="/images/addaddress/address_icon.png" />我的地址
      </view>

    </view>
  </view>
</view>

<TabBar activeIndex="{{ 2 }}" />

  <!-- 生成弹窗 -->
<van-overlay show="{{ visibleDialog }}" z-index="99">
  <view class="wrapper_window">
    <view  class="dialog">
      <image
        class="close"
        src="/images/dubbing/close.png"
        bind:tap="onClickHide"
      />
      <view class="body">
        有事请Q我
      </view>

      <image class="loading" src="https://k-cos.ai-market.top/wx/business/qr.png" show-menu-by-longpress="{{true}}" />
    </view>


  </view>
</van-overlay>
