import { isSiteAdminApi } from "@/api/merchant";
var util = require("../../../utils/util.js");
import { currentApi, loginByphoneApi } from "@/api/user";
var app = getApp();

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "我的",
    },
    visibleDialog: false,
    userInfo: {},
    shareAsset: {},
    hasMobile: "",
    fUser: {},
    commission: {
      allProfit: 0,
      getProfit: 0,
    },
    login: false,
    isSiteAdmin: false, // 是否是场所管理员
  },
  onLoad: function (options) {},
  onReady: function () {
    console.log(
      "app.globalData.userInfo.userId",
      app.globalData.userInfo.userId,
    );
    if (
      app.globalData.userInfo.userId == 0 ||
      app.globalData.userInfo.userId == null
    ) {
      this.setData({
        login: false,
      });
    } else {
      this.setData({
        login: true,
      });
    }
  },
  onShareAppMessage: function () {
    return {
      title: app.globalData.share_config.title,
      imageUrl: app.globalData.share_config.imageUrl,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  onShow: function () {
    let userInfo = wx.getStorageSync("user");
    this.setData({
      userInfo: userInfo,
    });
    this.checkSiteAdmin();
  },
  // 检查是否为场所管理员
  checkSiteAdmin: function () {
    isSiteAdminApi({ a: 1 })
      .then((res) => {
        if (res.code === 0) {
          this.setData({
            isSiteAdmin: res.data,
          });
        }
      })
      .catch((err) => {
        console.log("检查场所管理员失败", err);
      });
  },
  onHide: function () {
    // 页面隐藏
  },
  onUnload: function () {
    // 页面关闭
  },

  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    console.log("头像：", avatarUrl);
    app.globalData.userInfo.avatar = avatarUrl;
    wx.setStorageSync("user", app.globalData.userInfo);
    this.setData({
      userInfo: app.globalData.userInfo,
    });
  },
  gotoShare: function () {
    console.log("ddddd");
    wx.showShareMenu({
      menus: ["shareAppMessage", "shareTimeline"],
    });
  },
  // 隐藏
  onClickHide() {
    this.setData({
      visibleDialog: false,
    });
  },
  // 显示
  showServiceWin() {
    this.setData({
      visibleDialog: true,
    });
  },
  // 评价
  showReview() {
    var plugin = requirePlugin("wxacommentplugin");
    plugin.openComment({
      // wx_pay_id: '4200001729202306024807578', // 交易评价类账号选填
      success: (res) => {
        console.log("plugin.openComment success", res);
      },
      fail: (res) => {
        console.log("plugin.openComment fail", res);
      },
    });
  },

  copyId() {
    let that = this;
    let body = "欢迎使用小程序，我的id:" + that.data.userInfo.userCode;
    wx.setClipboardData({ data: body })
      .then(async (res) => {
        if (res.errMsg === "setClipboardData:ok") {
          await util.showSuccessToast("复制成功");
        } else {
          await util.showErrorToast("复制失败");
        }
      })
      .catch(async (error) => {
        await util.showErrorToast("复制异常");
        console.error(error);
      });
  },
  // 跳转视频列表
  gotoMixRecord() {
    wx.navigateTo({
      url: "/pages/ucenter/mix-record/index",
    });
  },

  // 授权页跳转
  handleQueryAuth() {
    console.log("授权页跳转");
    wx.navigateTo({
      url: "/pages/login/index",
    });
  },
  gotoLiveSplit() {
    wx.navigateTo({
      url: "/pages/ucenter/live-split/index",
    });
  },
  merchantBackend() {
    wx.navigateTo({
      url: "/store/store/index",
    });
  },
  gotoOrderList(e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `/pages/ucenter/order-list/index?status=${status}`,
    });
  },
  gotoAddressList() {
    wx.navigateTo({
      url: "/pages/ucenter/address-list/index",
    });
  },
});
