page {
  background-color: #ebfbfc;

}

.result {
  padding: 44rpx 0 40rpx;
  font-size: 32rpx;
  color: #052031;

}

.userinfo {
  display: flex;
  align-items: center;
  padding-bottom: 20rpx;
}

.container-avatar {
  margin-left: 20rpx;
}

.marleft {
  flex: 1;
  margin-left: 14rpx;
  display: flex;
  font-size: 32rpx;
  padding-right: 24rpx;
  align-items: center;

}



.userinfo-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 24rpx;
  border-radius: 50%;

}

.avatar-btn {
  border: 0;
  background: none;
  width: 100%;
}

.name {
  font-size: 30rpx;
  font-weight: bold;
}

.userinfo-mobile {
  height: 50rpx;
  margin-top: 10rpx;
  text-align: left;
  position: relative;

  .a {
    display: flex;
    flex-direction: row;
    justify-content: center;
  }

  .txt {
    height: 50rpx;
    line-height: 40rpx;
    font-size: 30rpx;
    color: #b4282d;
    float: left;
    background: none;
  }

  .copy {
    font-size: 24rpx;
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 2rpx 10rpx;

  }

  .id_box {
    margin-right: 16rpx;
    overflow: hidden;
    white-space: nowrap;
    width: 400rpx;
    font-size: 30rpx;
    text-overflow: -o-ellipsis-lastline;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;

  }

}

.usrtop-flex {
  flex: 1;
}

.merchant {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  padding: 2rpx 16rpx 4rpx 20rpx;
  justify-content: center;
  height: 36rpx;
  justify-items: center;
  text-align: center;
  background: #fff;
  border-radius: 20rpx;

  border: 1px solid #00CD88;
  color: #00CD88;

  .userinfo-arrow {
    width: 30rpx;
    display: inline-block;
  }
}

.user-nav {
  margin: 24rpx 24rpx 0;

  background: #FFFFFF;
  box-shadow: 0px 4rpx 20rpx 0px rgba(54, 127, 137, 0.1);
  border-radius: 16rpx;
  padding: 24rpx 24rpx 10rpx;

  .title {
    display: flex;
    font-size: 32rpx;
    padding-bottom: 18rpx;
    color: #052031;
    align-items: center;

    .more {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: flex-end;
      text-align: right;
      font-size: 28rpx;
      // color: rgba(0,0,0,0.5);
      color: #052031;
      opacity: 0.5;

      .right_icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.user-list-five {
  display: flex;
  flex-wrap: wrap;

  align-items: center;
  font-size: 24rpx;

  .list,.four {
    flex-basis: 20%;
    /* 约等于 100% / 5，留出一些空间用于间隙 */
    padding-bottom: 20rpx;
    text-align: center;
    box-sizing: border-box;
    /* 确保宽度包含内边距和边框 */
  }
.four{
  flex-basis: 25%;
font-size: 28rpx;
}
  .img-icon {
    width: 56rpx;
    height: 56rpx;
    display: block;
    margin: 12rpx auto;
  }
}




.personal {
  &-bg {
    position: fixed;
    right: 20px;
    top: 73px;

    width: 184px;
    height: 92px;
  }

  &-login {
    padding: 25px 20px;

    background-color: #fff;
    display: flex;
    align-items: center;

    font-family: PingFangSC-Medium, PingFang SC;

    position: relative;
  }

  &-card {
    padding-top: 24px;
    position: relative;
    z-index: 1;

    &_main {
      position: relative;
      background-color: #fff;
    }
  }
}
.login {
  &-left {
    flex: 1;
    &_title {
      font-size: 18px;
      font-weight: 500;
      color: #333333;
      line-height: 24px;
    }
    &_tips {
      margin-top: 10px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #666666;
      line-height: 16px;
      font-size: 12px;
    }
  }

  &-btn {
    padding: 7px 16px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 18px;
    font-size: 12px;

    background: #333333;
    border-radius: 16px;
    white-space: nowrap;
  }
}

.wrapper_window {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .dialog {
    position: relative;
    width: 544rpx;
    height: 610rpx;
    border-radius: 32rpx;
    background: linear-gradient(to bottom, rgb(235, 221, 210), rgb(239, 239, 239));
    border: 2px solid rgba(255, 255, 255, .7);

    .body {
      font-size: 28rpx;
      color: #222222;
      padding: 20rpx 22rpx;
      box-sizing: border-box;
      text-align: center;
      line-height: 46rpx;
      font-weight: 500;
    }

    .bottom {
      bottom: 40rpx;
    }

    .close {
      position: absolute;
      right: -18rpx;
      top: -84rpx;
      width: 54rpx;
      height: 54rpx;
    }

    .loading {
      display: block;
      position: relative;
      left: 0;
      right: 0;
      margin: 10rpx auto 0;
      width: 478rpx;
      height: 488rpx;
    }
  }

  .generate {
    position: relative;
    height: 606rpx;

    .txt {
      width: 100%;
      font-weight: 500;
      margin-top: 30rpx;
      text-align: center;
      font-size: 28rpx;
      color: #222222;
    }

    .btn {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 50rpx;
      margin: auto;
      width: 444rpx;
      height: 80rpx;
    }
  }

  .block {
    position: absolute;
    right: 0;
    top: 0;
    box-sizing: border-box;
    width: 558rpx;
    height: 100%;
    overflow-y: scroll;
    background-color: #fff;
    padding: 30rpx;

    .title {
      font-size: 36rpx;
      margin: 44rpx 0 24rpx;
    }
  }
}
