import {
  createAddressApi,
  updateAddress<PERSON>pi,
  deleteAddress,
  getAddressDetailApi,
} from "@/api/user";
import { getAreaListApi } from "@/api/common";

const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "添加收货地址",
    },
    id: "", // 地址ID，编辑时使用
    name: "", // 收货人姓名
    mobile: "", // 手机号码
    areaId: "", // 地区ID
    areaName: "", // 地区名称
    detailAddress: "", // 详细地址
    defaultStatus: false, // 是否默认

    provinces: [], // 省份列表
    cities: [], // 城市列表
    districts: [], // 区县列表

    provinceIndex: -1,
    cityIndex: -1,
    districtIndex: -1,

    isEdit: false, // 是否为编辑模式
    multiArray: [[], [], []], // 多列选择器的数据
    multiIndex: [0, 0, 0], // 多列选择器的选中项
    isMultiPickerShow: false, // 是否显示多列选择器
  },

  onLoad(options) {
    // 获取省市区数据
    this.getAreaData().then(() => {
      if (this.data.provinces.length > 0) {
        // 初始化多列选择器的数据
        this.initMultiPicker();
      }
    });

    // 如果有id参数，则为编辑模式
    if (options.id) {
      this.setData({
        id: options.id,
        isEdit: true,
        navData: {
          title: "编辑收货地址",
        },
      });
      this.getAddressDetail(options.id);
    }
  },

  // 获取地址详情
  getAddressDetail(id) {
    getAddressDetailApi({ id: id }).then((res) => {
      if (res.code === 0) {
        const address = res.data;
        this.setData({
          name: address.name,
          mobile: address.mobile,
          areaId: address.areaId,
          areaName: address.areaName,
          detailAddress: address.detailAddress,
          defaultStatus: address.defaultStatus,
        });

        // 解析地区名称，设置省市区选择器
        if (address.areaName) {
          const areas = address.areaName.split(" ");
          if (areas.length === 3) {
            // 找到对应的省市区索引
            this.findAreaIndices(areas[0], areas[1], areas[2]);
          }
        }
      }
    });
  },

  // 根据地区名称找到对应的索引
  findAreaIndices(provinceName, cityName, districtName) {
    const { provinces } = this.data;

    // 查找省份索引
    const provinceIndex = provinces.findIndex((p) => p.name === provinceName);
    if (provinceIndex >= 0) {
      // 获取该省份下的城市列表
      const cities = provinces[provinceIndex].children || [];

      // 查找城市索引
      const cityIndex = cities.findIndex((c) => c.name === cityName);
      if (cityIndex >= 0) {
        // 获取该城市下的区县列表
        const districts = cities[cityIndex].children || [];

        // 查找区县索引
        const districtIndex = districts.findIndex(
          (d) => d.name === districtName,
        );

        // 更新选择器数据
        this.setData({
          cities,
          districts,
          provinceIndex,
          cityIndex,
          districtIndex,
          multiIndex: [provinceIndex, cityIndex, districtIndex],
        });

        // 更新多列选择器的数据
        this.updateMultiPickerData();
      }
    }
  },

  // 获取省市区数据
  getAreaData() {
    return new Promise((resolve) => {
      getAreaListApi().then((res) => {
        if (res.code === 0) {
          this.setData({ provinces: res.data });
          resolve();
        }
      });
    });
  },

  // 初始化多列选择器
  initMultiPicker() {
    const { provinces } = this.data;

    // 第一列：省份
    const provinceNames = provinces.map((item) => item.name);

    // 默认选中第一个省份
    const cities = provinces[0]?.children || [];

    // 第二列：城市
    const cityNames = cities.map((item) => item.name);

    // 默认选中第一个城市
    const districts = cities[0]?.children || [];

    // 第三列：区县
    const districtNames = districts.map((item) => item.name);

    this.setData({
      cities,
      districts,
      "multiArray[0]": provinceNames,
      "multiArray[1]": cityNames,
      "multiArray[2]": districtNames,
    });
  },

  // 更新多列选择器的数据
  updateMultiPickerData() {
    const { provinces, cities, districts, provinceIndex, cityIndex } =
      this.data;

    // 更新省份列
    const provinceNames = provinces.map((item) => item.name);

    // 更新城市列
    const cityNames = cities.map((item) => item.name);

    // 更新区县列
    const districtNames = districts.map((item) => item.name);

    this.setData({
      "multiArray[0]": provinceNames,
      "multiArray[1]": cityNames,
      "multiArray[2]": districtNames,
    });
  },

  // 显示多列选择器
  showMultiPicker() {
    wx.showActionSheet({
      itemList: ["选择省市区"],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.openMultiPicker();
        }
      },
    });
  },

  // 打开多列选择器
  openMultiPicker() {
    const that = this;
    wx.navigateTo({
      url: "/pages/ucenter/area-picker/index",
      events: {
        // 监听选择结果
        selectArea: function (data) {
          that.setData({
            areaId: data.areaId,
            areaName: data.areaName,
          });
        },
      },
    });
  },

  // 多列选择器列变化时触发
  bindMultiPickerColumnChange(e) {
    const { column, value } = e.detail;
    const data = {
      multiIndex: this.data.multiIndex,
    };
    data.multiIndex[column] = value;

    switch (column) {
      case 0: // 第一列（省）变化
        // 获取选中的省份下的城市列表
        const cities = this.data.provinces[value].children || [];

        // 更新第二列（市）
        const cityNames = cities.map((item) => item.name);

        // 获取第一个城市下的区县列表
        const districts = cities[0]?.children || [];

        // 更新第三列（区）
        const districtNames = districts.map((item) => item.name);

        this.setData({
          cities,
          districts,
          "multiArray[1]": cityNames,
          "multiArray[2]": districtNames,
          "multiIndex[1]": 0,
          "multiIndex[2]": 0,
        });
        break;

      case 1: // 第二列（市）变化
        // 获取选中的城市下的区县列表
        const newDistricts = this.data.cities[value].children || [];

        // 更新第三列（区）
        const newDistrictNames = newDistricts.map((item) => item.name);

        this.setData({
          districts: newDistricts,
          "multiArray[2]": newDistrictNames,
          "multiIndex[2]": 0,
        });
        break;
    }

    this.setData(data);
  },

  // 多列选择器选择完成后触发
  bindMultiPickerChange(e) {
    const { multiIndex } = this.data;
    const province = this.data.provinces[multiIndex[0]];
    const city = this.data.cities[multiIndex[1]];
    const district = this.data.districts[multiIndex[2]];

    this.setData({
      areaId: district.id,
      areaName: `${province.name} ${city.name} ${district.name}`,
      provinceIndex: multiIndex[0],
      cityIndex: multiIndex[1],
      districtIndex: multiIndex[2],
    });
  },

  // 输入框变化
  inputChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [field]: e.detail.value,
    });
  },

  // 切换默认地址
  toggleDefault() {
    this.setData({
      defaultStatus: !this.data.defaultStatus,
    });
  },

  // 保存地址
  saveAddress() {
    const {
      id,
      name,
      mobile,
      areaId,
      areaName,
      detailAddress,
      defaultStatus,
      isEdit,
    } = this.data;

    // 表单验证
    if (!name) {
      wx.showToast({ title: "请输入收货人姓名", icon: "none" });
      return;
    }

    if (!mobile) {
      wx.showToast({ title: "请输入手机号码", icon: "none" });
      return;
    }

    // 简单的手机号验证
    if (!/^1\d{10}$/.test(mobile)) {
      wx.showToast({ title: "手机号格式不正确", icon: "none" });
      return;
    }

    if (!areaId || !areaName) {
      wx.showToast({ title: "请选择所在地区", icon: "none" });
      return;
    }

    if (!detailAddress) {
      wx.showToast({ title: "请输入详细地址", icon: "none" });
      return;
    }

    // 构建请求数据
    const data = {
      name,
      mobile,
      areaId,
      areaName,
      detailAddress,
      defaultStatus,
    };
    wx.showLoading({ title: "保存中..." });
    // 编辑模式需要传入ID
    if (isEdit) {
      data.id = id;
      updateAddressApi(data)
        .then((res) => {
          wx.hideLoading();
          if (res.code === 0) {
            wx.showToast({ title: "保存成功", icon: "success" });
            // 返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          } else {
            wx.showToast({ title: res.msg || "保存失败", icon: "none" });
          }
        })
        .catch((err) => {
          wx.hideLoading();
          wx.showToast({ title: "网络异常，请重试", icon: "none" });
        });
    } else {
      createAddressApi(data)
        .then((res) => {
          wx.hideLoading();
          if (res.code === 0) {
            wx.showToast({ title: "保存成功", icon: "success" });
            // 返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          } else {
            wx.showToast({ title: res.msg || "保存失败", icon: "none" });
          }
        })
        .catch((err) => {
          wx.hideLoading();
          wx.showToast({ title: "网络异常，请重试", icon: "none" });
        });
    }
  },

  // 删除地址
  deleteAddress() {
    const { id } = this.data;

    wx.showModal({
      title: "提示",
      content: "确定要删除该地址吗？",
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: "删除中..." });

          deleteAddress({ id: id })
            .then((res) => {
              wx.hideLoading();
              if (res.code === 0) {
                wx.showToast({ title: "删除中成功", icon: "success" });
                // 返回上一页
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              } else {
                wx.showToast({ title: res.msg || "删除失败", icon: "none" });
              }
            })
            .catch((err) => {
              wx.hideLoading();
              wx.showToast({ title: "网络异常，请重试", icon: "none" });
            });
        }
      },
    });
  },
});
