<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result" style="margin-top: {{ paddingHeight }}px">

  <view class="result_m">
    <view class="topweixin_address" wx:if="{{false}}">
      <image class="left-img" src="/images/addaddress/icon_1.png" />
      <view class="middle_text"> <text>获取微信收货地址</text></view>
      <image class="right_icon" src="/images/store/arrow-right.png" />
    </view>
    <view class="from_C">
      <view class="from_li">
        <text class="left-text">联系人名</text>
        <input class="weui-input address_item" placeholder-class="placeholder-class" placeholder="请输入收货人姓名" value="{{name}}" bindinput="inputChange" data-field="name" />
      </view>
      <view class="from_li">
        <text class="left-text">手机号码</text>
        <input class="weui-input address_item" placeholder-class="placeholder-class" placeholder="请输入收货人手机" value="{{mobile}}" bindinput="inputChange" data-field="mobile" type="number" maxlength="11" />
      </view>
      <view class="from_li from-choose">
        <text class="left-text">所在地区</text>
        <picker mode="multiSelector" style="margin-left: 0;" bindchange="bindMultiPickerChange" bindcolumnchange="bindMultiPickerColumnChange" value="{{multiIndex}}" range="{{multiArray}}">
          <view class="weui-input">
            <view wx:if="{{!areaName}}" class="picker_text">选择省/市/区</view>
            <view wx:else class="picker">{{areaName}}</view>
          </view>
        </picker>
        <image class="right_icon" src="/images/store/arrow-right.png" />
      </view>
      <view class="from_li">
        <text class="left-text">详细地址</text>
        <input class="weui-input address_item" placeholder-class="placeholder-class" placeholder="请输入收货人地址" value="{{detailAddress}}" bindinput="inputChange" data-field="detailAddress" />
      </view>
      <view class="from_li">
        <checkbox value="是否默认" class="checkoutBox" checked="{{defaultStatus}}" bindtap="toggleDefault"></checkbox>
        设为默认
      </view>
    </view>
    <view class="Agreement" wx:if="{{false}}">
      保存地址即视为您同意
      <text class="link-text">《隐私协议》</text>
      <text class="link-text"> 《服务协议》</text>
    </view>
  </view>
  <view class="footerBtn">
    <view class="btn-container" wx:if="{{isEdit}}">
      <button class="delete-btn" bindtap="deleteAddress">删除地址</button>
      <button class="video-btn" bindtap="saveAddress">保存地址</button>
    </view>
    <button wx:else class="video-btn" bindtap="saveAddress">保存收货地址</button>
  </view>
</view>
