page {
  background-color: #ebfbfc;

}

.result {
  padding:24rpx 0 40rpx;
  font-size: 32rpx;
  color: #052031;

}
.result_m{
  padding-bottom: 220rpx;
}
.topweixin_address{
  display: flex;
  padding: 24rpx 34rpx;
background: #fff;
justify-items: center;
justify-content: center;
margin: 10rpx 0;

.left-img{
  width: 44rpx;
  height: 44rpx;
}
.middle_text{
  flex: 1;
  padding: 0 24rpx;
  color: #000;
  font-weight: bold;
  font-size: 32rpx;
}
}
.right_icon{
  width: 40rpx;
  height: 40rpx;
}
.footerBtn{
  bottom: 0;
  left: 0;
  width: 100%;
  position: fixed;
  height: 104rpx;
  background: #fff;
  padding: 24rpx 0 60rpx;
  .btn-container {
    display: flex;
    justify-content: space-between;
    width: calc(100% - 100rpx);
    margin: 0 auto;

    .delete-btn {
      width: 48%;
      height: 104rpx;
      background: #FF4F28;
      color: #fff;
      border-radius: 100rpx;
      font-size: 32rpx;
      line-height: 104rpx;
    }

    .video-btn {
      width: 48%;
      height: 104rpx;
      background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
      color: #000;
      border-radius: 100rpx;
      font-size: 32rpx;
      line-height: 104rpx;
    }
  }

  // Original style for the add address button (non-edit mode)
  .video-btn {
    width: calc(100% - 100rpx);
    height: 104rpx;
    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    color: #000;
    border-radius: 100rpx;
    font-size: 32rpx;
    line-height: 104rpx;
  }
}
.from_C{
  background: #fff;
  padding: 24rpx;
  .from_li{
    padding: 20rpx 14rpx;
    display: flex;
    border-bottom: 1rpx solid rgba(5,32,49,0.1);
align-items: center;
font-size: 26rpx;
    justify-items: center;
    padding-bottom: 16rpx;
    .left-text{
      width: 120rpx;
      font-size: 28rpx;
      color: #052031;
    }
    .weui-input{
      margin-left: 30rpx;
      font-size: 26rpx;
      .picker_text{
        text-align: right;
      }
    }
    .placeholder-class{
      color: #1A367F89;
      text-align: left;
    }
    .checkoutBox{
      margin-right: 10rpx;

    }
    checkbox .wx-checkbox-input{
      border-radius: 50%;/* 圆角 */
      width: 40rpx; /* 背景的宽 */
      height: 40rpx; /* 背景的高 */
   }
  }
.from-choose{
padding-right: 10rpx;
}
}
.Agreement{
  margin: 20rpx;
  font-size: 24rpx;
  text-align: center;
  .link-text{
    color: #1FB5D7;
    margin: 0 5rpx;
  }
}
.area-picker {
  background: #fff;
  padding: 10rpx 24rpx;

  .picker-row {
    display: flex;
    justify-content: space-between;
  }

  .picker-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    color: #999;

    &.selected {
      color: #052031;
    }
  }
}

.address_item{
  width: 500rpx;
}
