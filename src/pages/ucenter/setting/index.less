page {
  background-color: #ebfbfc;

}

.result {
  padding:44rpx 0 40rpx;
  font-size: 32rpx;
  color: #052031;

}
.setting-ul{
  margin-bottom: 20rpx;
  background: #fff;
  box-shadow: 0px 10rpx 20rpx 0px rgba(54,127,137,0.1);
  padding:14rpx 24rpx;
  font-size: 28rpx;
  .setting-li{
    border-bottom:1rpx solid rgba(5,32,49,0.1);
    padding: 20rpx 0;
    display: flex;
    align-items: center;
    .right-icon{
      width: 130rpx;
      display: flex;
      align-items: center;
      text-align: right;
      justify-content: flex-end;
  .head-img{
    width: 80rpx;
    border-radius: 50%;
    height: 80rpx;
    margin-right: 10rpx;
  }
    }
    .left-text{
      flex: 1;
    }
    &:last-child{
      border-bottom: 0;
    }
  }
  .right_icon{
    width: 40rpx;
    height: 40rpx;
    margin-left: 10rpx;
  }
}