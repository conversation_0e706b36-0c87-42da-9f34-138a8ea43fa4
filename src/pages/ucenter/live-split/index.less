page {
  background-color: #EBFBFC;
}

.result {
  padding: 40rpx 24rpx;

  font-size: 32rpx;
  color: #052031;

  .empty {
    display: block;
    width: 332rpx;
    height: 266rpx;
    margin: 200rpx auto 0;
  }

  .footer {
    position: fixed;
    bottom: 100rpx;
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
    font-size: 26rpx;
    color: #052031;

    .btn {
      margin-top: 20rpx;
    }
  }

  .btn-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 8rpx;
  }

  .title {
    text-align: center;
    margin: 20rpx 0 28rpx;
    font-weight: bold;
    font-size: 40rpx;
    color: #052031;
  }

  .desc {
    text-align: center;
    font-size: 26rpx;
    color: #052031;
    line-height: 52rpx;
    padding: 0 90rpx;
  }

  &-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 4rpx;
  }

  &-card {
    position: relative;
    width: 702rpx;
    height: 396rpx;
    border-radius: 32rpx 32rpx 32rpx 32rpx;
    overflow: hidden;

    &__bg {
      width: 100%;
      height: 100%;
    }

    &__similar {
      position: absolute;
      right: 24rpx;
      top: 24rpx;
      text-align: center;

      .similar-text {
        background: #052031;
        border-radius: 40rpx 40rpx 40rpx 40rpx;
        opacity: 0.5;
        padding: 4rpx 14rpx;
        font-size: 24rpx;
        color: #ffffff;
      }

      .result-avatar {
        width: 50rpx;
        height: 50rpx;
        border: 4rpx solid #ef0808;
        border-radius: 96rpx;
      }
    }

    &__footer {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 70rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #052031;
      background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
      opacity: 0.9;
      display: flex;
      border-radius: 32rpx;
      justify-content: center;
      align-items: center;
    }
  }

  &-item {
    margin-bottom: 40rpx;

    &__position {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
    }
  }
}

.popup {
  width: 624rpx;
  height: 414rpx;
  background: #FFFFFF;
  text-align: center;
  padding: 60rpx 64rpx;
  box-sizing: border-box;

  &-title {
    font-weight: 800;
    font-size: 36rpx;
    color: #052031;
  }

  &-desc {
    font-weight: 500;
    font-size: 28rpx;
    color: #052031;
    margin: 24rpx 0 60rpx;
    opacity: .5;
    line-height: 40rpx;
  }
}

.btn {
  width: 464rpx;
  height: 80rpx;
  margin: 0 auto;
  display: flex;
  font-size: 36rpx;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
  border-radius: 100rpx 100rpx 100rpx 100rpx;
}

.loading-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7); /* 半透明遮罩 */
  display: flex;
  justify-content: center;
  align-items: center;
}


@keyframes spin {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(100%);
  }
}

.loading-animation {

  animation: spin 2s linear infinite;
}

.left{
   width: 200rpx;
   left: 60rpx;
   bottom: 20rpx;
}

.right{
   width: 200rpx;
   left: 440rpx;
   bottom: 20rpx;
   right: 40rpx;
}

.loading {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}
