<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result" style="margin-top: {{ paddingHeight }}px">
  <block wx:if="{{ isEmpty }}">
    <view>
      <image class="empty" src="https://k-cos.ai-market.top/wx/base/video/empty.png" />
      <view class="desc">切片正在生成，请稍后</view>
    </view>
  </block>
  <block wx:else>
    <scroll-view scroll-y="true" style="height: calc(100vh - {{paddingHeight}}px - 100rpx);" bindscrolltolower="loadMore" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
      <view class="result-item" wx:for="{{ list }}" wx:key="id" >
        <view class="result-item__position">
          <image class="result-icon" src="/images/video/position.png" />
          <view>{{ item.createTime }}</view>
        </view>

        <view class="result-card">
          <image class="result-card__bg" src="{{ item.mixVideoCoverUrl }}"  />
        <view class="result-card__similar" wx:if="{{ item.buy }}">
          <view class="result-avatar" >购</view>
        </view>

        <view class="result-card__footer right" wx:if="{{ item.status == 1 }}" bind:tap="mergeVideo" data-id="{{ item.id }}" data-type="2">预览视频</view>
        </view>
      </view>

      <view wx:if="{{ loading }}" class="loading">加载中...</view>
      <view wx:if="{{ noMore && list.length > 0 }}" class="no-more">没有更多切片了</view>
    </scroll-view>
  </block>
</view>


