import { queryLiveSplitListApi } from "@/api/live";
const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "直播切片",
    },
    list: [],
    state: [
      {
        id: 100,
        label: "视频检索中",
      },
      {
        id: 0,
        label: "等待合成",
      },
      {
        id: 1,
        label: "合成中",
      },
      {
        id: 2,
        label: "点击观看",
      },
      {
        id: -1,
        label: "合成失败",
      },
    ],
    process_state_list: [100, 0, 1],
    isEmpty: false,
    show: false,
    taskId: null,
    // 分页相关
    pageNo: 1,
    pageSize: 10,
    loading: false,
    noMore: false,
    refreshing: false,
  },
  onLoad(options) {
    let taskId = options.taskId;
    this.setData({ taskId: taskId });
    this.queryMixVideoTask(true);
  },
  queryMixVideoTask(isRefresh = false) {
    if (this.data.loading) return;

    let taskId = this.data.taskId;
    var userId = app.globalData.userInfo.userId;
    if (userId == 0 || userId == null) {
      console.log("未登陆 userId", userId);
      return;
    }

    const { pageNo, pageSize } = this.data;
    this.setData({ loading: true });

    var postData = {
      status: 1,
      userId: userId,
      pageNo: pageNo,
      pageSize: pageSize,
    };
    if (taskId) {
      postData.taskId = taskId;
    }

    let that = this;
    queryLiveSplitListApi(postData)
      .then((res) => {
        console.log(res);

        let newList = res.data.list.map((item) => {
          item.mixVideoCoverUrl =
            "https://k-cos.ai-market.top/wx/base/logo_horizontal.png?v=0716";
          // 时间戳 格式化 yyyy-mm-dd hh:mm:ss
          item.createTime = new Date(item.createTime).toLocaleString();
          return item;
        });

        if (isRefresh) {
          that.setData({
            list: newList,
            isEmpty: newList.length === 0,
            refreshing: false,
          });
        } else {
          that.setData({
            list: [...that.data.list, ...newList],
          });
        }

        that.setData({
          loading: false,
          noMore: newList.length < pageSize,
          pageNo: pageNo + 1,
        });
      })
      .catch((err) => {
        console.error("获取直播切片列表失败", err);
        that.setData({
          loading: false,
          refreshing: false,
        });
        wx.showToast({
          title: "获取直播切片列表失败",
          icon: "none",
        });
      });
  },
  onRefresh() {
    this.setData({
      refreshing: true,
      pageNo: 1,
      noMore: false,
    });
    this.queryMixVideoTask(true);
  },

  loadMore() {
    if (!this.data.noMore) {
      this.queryMixVideoTask();
    }
  },

  onClose() {
    this.setData({ show: false });
  },
  gotoPhoto() {
    wx.navigateTo({
      url: "/pages/selfie/index",
    });
  },
  mergeVideo(e) {
    let id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: "/pages/retrieval/play/index?id=" + id + "&type=2",
    });
  },
  /**
   * 自定义合成
   */
  customMerge(e) {
    let template = e.currentTarget.dataset.template;
    let site = e.currentTarget.dataset.site;
    wx.navigateTo({
      url:
        "/pages/retrieval/generate-video/index?templateId=" +
        template +
        "&siteId=" +
        site,
    });
  },
});
