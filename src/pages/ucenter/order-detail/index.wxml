<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="order-detail-page" style="margin-top: {{ paddingHeight }}px">
  <!-- 订单状态栏 -->
  <view class="order-status-bar">
    <image class="status-icon" wx:if="{{order.statusLabel=='已完成'}}" src="/images/yinge/<EMAIL>" />
    <image class="status-icon" wx:if="{{order.statusLabel=='待发货'}}"  src="/images/yinge/<EMAIL>" />
    <image class="status-icon" wx:if="{{order.statusLabel=='待收货'}}" src="/images/yinge/<EMAIL>" />
    <image class="status-icon" wx:if="{{order.statusLabel=='已取消'}}" src="/images/yinge/<EMAIL>" />
    <image class="status-icon" wx:if="{{order.statusLabel=='待支付'}}" src="/images/yinge/<EMAIL>" />
    <view class="status-info">
      <text class="status-text pending">{{ order.statusLabel }}</text>
      <text wx:if="{{ false }}" class="status-desc">交易完成</text>
    </view>
  </view>
  <view class="divider"></view>

  <!-- 地址信息 -->
  <view class="order-address">
    <image class="address-icon" src="/images/yinge/<EMAIL>" />
    <view class="address-info">
      <view>
        <text class="name">{{ order.name }}</text>
        <text class="phone">{{ order.mobile }}</text>
        <text class="tag" wx:if="{{order.isDefault}}">默认</text>
      </view>
      <view class="address-detail">{{ order.address }}</view>
    </view>
  </view>

  <!-- 虚线分割线 -->
  <view class="dashed-line">
    <image  src="/images/yinge/Groupline.png"  mode="widthFix" />
  </view>

  <!-- 商品卡片+价格明细 -->
  <view class="goods-block">
    <view class="goods-card">
      <image class="goods-img" src="{{ order.productImg || 'https://k-cos.ai-market.top/wx/product/dz-1.png' }}" />
      <view class="goods-info">
        <view class="goods-title">{{ order.title }}</view>
        <view class="goods-spec">规格：默认</view>
        <view class="goods-price-row">
          <text class="goods-price">¥<text class="text">{{ order.payPrice }}</text></text>
          <text class="goods-qty">x{{ order.quantity }}</text>
        </view>
      </view>
    </view>
    <view class="price-detail" style="padding-top:20rpx;">
      <view class="price-row">
        <text>商品总价</text>
        <text>¥{{ order.payPrice }}</text>
      </view>
      <view class="price-row">
        <text>运费</text>
        <text>¥{{ 0 }}</text>
      </view>
      <view class="price-row total">
        <text>实付款</text>
        <text class="total-price">¥<text class="text">{{ order.payPrice }}</text></text>
      </view>
      <view class="textRight"> <button class="btn-kefu" bindtap="showServiceWin">联系客服</button></view>
    </view>
  </view>
  <view class="divider"></view>
  <!-- 订单信息 -->
  <view class="order-info-block">
    <view class="info-title">
      订单信息
      <view class="logistics-link" wx:if="{{order.status >= 0}}" bindtap="goToExpress">
        <text>查看物流</text>
        <image class="logistics-icon" src="/images/yinge/arrow-right.png" />
      </view>
    </view>
    <view class="info-row">
      <text>订单编号：</text>
      <text>{{ order.no }}</text>
      <text class="copy-btn" bindtap="copyOrderNo">复制</text>
    </view>
    <view class="info-row">
      <text>下单时间：</text>
      <text>{{ order.createTime }}</text>
    </view>
  </view>
  <view class="divider"></view>
  <!-- 吸底操作栏 -->
  <view class="order-bottom-bar">
    <view class="total-amount">合计：<text class="total-price">¥<text class="text">{{ order.totalPrice }}</text></text></view>
    <button class="btn cancel-btn" wx:if="{{order.status < 20}}" bindtap="cancelOrder">取消订单</button>
    <button class="btn pay-btn" wx:if="{{order.status == 20}}" bindtap="confirmReceipt">确认收货</button>
  </view>
</view>


<!-- 生成弹窗 -->
<van-overlay show="{{ visibleDialog }}" z-index="99">
  <view class="wrapper_window">
    <view  class="dialog">
      <image
        class="close"
        src="/images/dubbing/close.png"
        bind:tap="onClickHide"
      />
      <view class="body">
        有事请Q我
      </view>

      <image class="loading" src="https://k-cos.ai-market.top/wx/business/qr.png" show-menu-by-longpress="{{true}}" />
    </view>


  </view>
</van-overlay>
