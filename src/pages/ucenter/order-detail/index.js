import {
  queryYingeOrderDetailApi,
  cancelYingeOrder<PERSON>pi,
  confirmYingeOrderApi,
} from "@/api/order";
const app = getApp();

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "订单详情",
    },
    order: {},
    statusDescription: "",
    statusMap: {
      0: "待支付",
      10: "待发货",
      20: "已发货",
      30: "已完成",
      40: "已取消",
    },
    statusDescMap: {
      0: "请在24小时内完成支付",
      10: "商家正在备货中，请耐心等待",
      20: "商品已发出，请注意查收",
      30: "订单已完成，感谢您的购买",
      40: "订单已取消",
    },
    visibleDialog: false,
    orderNo: null,
  },

  onLoad(options) {
    const no = options.no;
    this.setData({
      orderNo: no,
    });

    this.loadOrderDetail(no);
  },

  loadOrderDetail(id) {
    wx.showLoading({
      title: "加载中...",
    });

    queryYingeOrderDetailApi({
      no: id,
    })
      .then((res) => {
        const orderData = res.data;
        // 处理订单数据
        const order = {
          ...orderData,
          totalPrice: (orderData.totalPrice / 100).toFixed(2),
          payPrice: (orderData.payPrice / 100).toFixed(2),
          shippingFee: "0.00",
        };

        this.setData({
          order: order,
          statusDescription: this.data.statusDescMap[order.status] || "",
        });

        wx.hideLoading();
      })
      .catch((err) => {
        console.error("获取订单详情失败", err);
        wx.hideLoading();
        wx.showToast({
          title: "获取订单详情失败",
          icon: "none",
        });
      });
  },

  copyOrderNo() {
    wx.setClipboardData({
      data: this.data.orderNo,
      success: () => {
        wx.showToast({
          title: "复制成功",
          icon: "success",
        });
      },
    });
  },

  cancelOrder() {
    wx.showModal({
      title: "提示",
      content: "确定要取消该订单吗？",
      success: (res) => {
        if (res.confirm) {
          cancelYingeOrderApi({
            oid: this.data.order.no,
            reason: "用户取消",
          }).then((res) => {
            if (res.code == 200) {
              wx.showToast({
                title: "订单已取消",
                icon: "success",
              });
              this.loadOrderDetail(this.data.order.id);
            } else {
              wx.showToast({
                title: res.msg,
                icon: "none",
              });
            }
          });
        }
      },
    });
  },

  payOrder() {
    // 这里需要添加支付逻辑
    wx.showToast({
      title: "跳转支付...",
      icon: "loading",
    });
  },

  //确认收货
  confirmReceipt() {
    const oid = this.data.orderNo; //e.currentTarget.dataset.id;
    wx.showModal({
      title: "提示",
      content: "确认已收到商品？",
      success: (res) => {
        if (res.confirm) {
          // 这里确认收货的API调用
          confirmYingeOrderApi({
            no: oid,
          }).then((res) => {
            if (res.code == 0 && res.data == true) {
              wx.showToast({
                title: "订单已确认",
                icon: "success",
              });
              this.onRefresh();
            } else {
              wx.showToast({
                title: res.msg,
                icon: "none",
              });
            }
          });
          wx.showToast({
            title: "确认收货成功",
            icon: "success",
          });
          this.loadOrderDetail(oid);
        }
      },
    });
  },

  applyRefund() {
    wx.navigateTo({
      url: `/pages/ucenter/refund/index?id=${this.data.order.id}`,
    });
  },
  goToExpress() {
    wx.navigateTo({
      url: `/pages/ucenter/yg-express/index?no=${this.data.order.no}`,
    });
  },
  // 隐藏
  onClickHide() {
    this.setData({
      visibleDialog: false,
    });
  },
  // 显示
  showServiceWin() {
    this.setData({
      visibleDialog: true,
    });
  },
});
