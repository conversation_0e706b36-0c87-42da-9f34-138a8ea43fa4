page {
  // background: #f6fbfd;
  background: #EBFBFC;
}
.order-detail-page {
  padding-bottom: 160rpx;
}
.order-status-bar {
  display: flex;
  align-items: flex-start;
  background: none;
  padding: 32rpx 24rpx 0 24rpx;
  .status-icon {
    width: 44rpx;
    height: 44rpx;
    margin-right: 18rpx;
    margin-top: 2rpx;
  }
  .status-info {
    display: flex;
    flex-direction: column;
    .status-text {
      font-size: 32rpx;
      font-weight: 700;
      color: #ff4f28;
      margin-bottom: 4rpx;
      &.pending { color: #ff4f28; }
    }
    .status-desc {
      font-size: 24rpx;
      color: #8a9399;
      margin-top: 0;
    }
  }
}
.divider {
  height: 1rpx;
  background: #e6e6e6;
  margin: 16rpx 24rpx 0 ;
}
.textRight{
  text-align: right;
}
.btn-kefu {
  width: 160rpx;
  display: inline-block;
 margin: 0;
  height: 56rpx;
  line-height: 50rpx;
  border-radius: 56rpx;
  font-size: 24rpx;
  border: 1rpx solid #e6e6e6;
  background: #fff;
  color: #052031;}
.order-address {
  display: flex;
  align-items: flex-start;
  // background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0 0 0;
  padding: 24rpx 24rpx 16rpx;
  .address-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 18rpx;
  }
  .address-info {
    flex: 1;
    .name {
      font-weight: 700;
      margin-right: 16rpx;
      color: #222;
    }
    .phone {
      color: #8a9399;
      margin-right: 12rpx;
    }
    .tag {
      display: inline-block;
      font-size: 20rpx;
      color: #FF4F28;
      background: rgba(255, 79, 40, 0.1);
      border: 1rpx solid #FF4F28;
      border-radius: 8rpx;
      padding: 2rpx 10rpx;
      margin-left: 6rpx;
      vertical-align: middle;
    }
    .address-detail {
      color: #222;
      font-size: 26rpx;
      margin-top: 8rpx;
      line-height: 1.5;
    }
  }
}
.dashed-line {
  // border-bottom: 2rpx dashed;
  // border-image: linear-gradient(90deg, #ff7e2d 0%, #0bb6ff 100%) 1;
  // margin: 20rpx 0 0 0;
  image,img{

    width: 100%;
  }
}
.goods-block {
  background: none;
  margin: 0;
  padding: 0;
}
.goods-card {
  display: flex;
  // background: #fff;
  border-radius: 16rpx;
  margin: 16rpx 0 0 0rpx;
  padding:16rpx 24rpx;
  .goods-img {
    width: 136rpx;
    height: 136rpx;
    border-radius: 12rpx;
    object-fit: cover;
    margin-right: 18rpx;
    background: #f6fbfd;
  }
  .goods-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .goods-title {
      font-size: 30rpx;
      font-weight: 700;
      color: #222;
      margin-bottom: 8rpx;
      line-height: 1.3;
      height: 78rpx;
      // word-break: break-all;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
     text-overflow: ellipsis;
     width: 100%;
    }
    .goods-spec {
      font-size: 24rpx;
      color: #888;
      margin-bottom: 8rpx;
    }
    .goods-price-row {
      display: flex;
      align-items: center;
      .goods-price {
        color: #ff4f28;
        font-size: 24rpx;
        font-weight: 100;
        margin-right: 18rpx;
        flex: 1;
        .text{
          font-size: 40rpx;
          font-weight: 700;
          line-height: 1;
        }
      }
      .goods-qty {
        color: #8a9399;
        font-size: 26rpx;
      }
    }
  }
}
.price-detail {
  background: none;
  border-radius: 0;
  margin: 0 ;
  padding: 0 24rpx 12rpx 24rpx;
  position: relative;
  .price-row {
    display: flex;
    justify-content: space-between;
    font-size: 28rpx;
    color: #052031;
    margin-bottom: 24rpx;
    &.total {

      .total-price {
        color: #ff4f28;
        font-size: 26rpx;
        .text{
          font-size: 32rpx;
        }
      }
    }
  }
  .btn.service-btn {
    position: absolute;
    right: 0;
    top: 0;
    height: 48rpx;
    line-height: 48rpx;
    padding: 0 24rpx;
    font-size: 24rpx;
    color: #0bb6ff;
    border: 1rpx solid #0bb6ff;
    border-radius: 24rpx;
    background: #fff;
  }
}
.order-info-block {
  // background: #e6f6fd;
  border-radius: 16rpx;
  margin: 24rpx 0 0 0;
  padding: 24rpx;
  .info-title {
    font-size: 28rpx;
    font-weight: 700;
    margin-bottom: 18rpx;
    color: #222;
  }
  .info-row {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    // color: #8a9399;
    color: rgba(5, 32, 49, 0.5);
    margin-bottom: 20rpx;
    .copy-btn {
      color: #1FB5D7;
      font-size: 28rpx;
      margin-left: 12rpx;
      // border: 1rpx solid #0bb6ff;
      border-radius: 8rpx;
      padding: 2rpx 10rpx;
      // background: #fff;
    }
  }
}
.order-bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-top: 1rpx solid #e6e6e6;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  height: 110rpx;
  z-index: 99;
  box-shadow: 0 -4rpx 24rpx 0 rgba(11,182,255,0.08);
  .total-amount {
    flex: 1;
    font-size: 30rpx;
    color: #222;
    .total-price {
      color: #ff4f28;
      font-size: 36rpx;

      margin-left: 6rpx;
      .text{
        font-weight: bold;
      }
    }
  }
  .btn {
    min-width: 160rpx;
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
    border-radius: 35rpx;
    font-size: 28rpx;
    padding: 0 30rpx;

    margin-left: 18rpx;
    &.cancel-btn {
      color: rgba(5, 32, 49, 1);
      border: 1rpx solid #e6e6e6;
      background: #fff;
    }
    &.pay-btn {
      background: linear-gradient(90deg, #0bb6ff 0%, #1ee6b6 100%);
      color:#052031;
      font-weight: 500;
      border: none;
      margin-left: 24rpx;
    }
  }
}

.info-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logistics-link {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #1FB5D7;
  font-weight: 100;
}

.logistics-icon {
  width: 36rpx;
  height: 36rpx;
  vertical-align: middle;
  margin-left: 8rpx;
}
.wrapper_window {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .dialog {
    position: relative;
    width: 544rpx;
    height: 610rpx;
    border-radius: 32rpx;
    background: linear-gradient(to bottom, rgb(235, 221, 210), rgb(239, 239, 239));
    border: 2px solid rgba(255, 255, 255, .7);

    .body {
      font-size: 28rpx;
      color: #222222;
      padding: 20rpx 22rpx;
      box-sizing: border-box;
      text-align: center;
      line-height: 46rpx;
      font-weight: 500;
    }

    .bottom {
      bottom: 40rpx;
    }

    .close {
      position: absolute;
      right: -18rpx;
      top: -84rpx;
      width: 54rpx;
      height: 54rpx;
    }

    .loading {
      display: block;
      position: relative;
      left: 0;
      right: 0;
      margin: 10rpx auto 0;
      width: 478rpx;
      height: 488rpx;
    }
  }

  .generate {
    position: relative;
    height: 606rpx;

    .txt {
      width: 100%;
      font-weight: 500;
      margin-top: 30rpx;
      text-align: center;
      font-size: 28rpx;
      color: #222222;
    }

    .btn {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 50rpx;
      margin: auto;
      width: 444rpx;
      height: 80rpx;
    }
  }

  .block {
    position: absolute;
    right: 0;
    top: 0;
    box-sizing: border-box;
    width: 558rpx;
    height: 100%;
    overflow-y: scroll;
    background-color: #fff;
    padding: 30rpx;

    .title {
      font-size: 36rpx;
      margin: 44rpx 0 24rpx;
    }
  }
}
