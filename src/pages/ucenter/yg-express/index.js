import { queryYingeOrderDetailApi, getAYingExpress } from "@/api/order";
const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "物流详情",
    },
    expressList: [],
    orderNo: null,
  },

  onLoad(options) {
    const no = options.no;
    this.setData({
      orderNo: no,
    });
    this.getExpressList();
  },

  getExpressList() {
    getAYingExpress({
      no: this.data.orderNo,
    }).then((res) => {
      // Parse the nested JSON string data
      let expressData = [];
      try {
        const jsonStr = res.data.expressDetail;
        const parsedData = JSON.parse(JSON.stringify(jsonStr));
        expressData = parsedData.map((item) => {
          return {
            time: item.time,
            context: item.context,
            status: item.status,
            city: item.city,
          };
        });
      } catch (error) {
        console.error("Error parsing express data:", error);
      }

      this.setData({
        expressList: expressData,
      });
    });
  },
});
