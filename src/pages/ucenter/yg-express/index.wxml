<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result step-ul" style="margin-top: {{ paddingHeight }}px">
  <view class="stepFlex" wx:for="{{ expressList }}" wx:key="time">
    <view class="left-icon">
      <view class="yuan-icon"></view>
      <view class="line" wx:if="{{ index !== expressList.length - 1 }}"></view>
    </view>
    <!-- 右侧 -->
    <view class="right-text">
      <view class="title-date">{{ item.context }}
        <view class="date">{{ item.time }}</view>
      </view>
      <view wx:if="{{ item.city }}" class="audit">
        城市：{{ item.city }}
      </view>
    </view>
  </view>


  <block wx:if="{{ expressList.length === 0 }}">
    <view style="text-align: center;">
      <image class="empty" src="https://k-cos.ai-market.top/wx/base/video/empty.png" />
      <view class="title">暂无物流信息</view>
    </view>
  </block>

</view>
