page {
  background-color: #ebfbfc;

}

.result {
  padding:44rpx 0 40rpx;
  font-size: 32rpx;
  color: #052031;

}
.step-ul{
padding-top: 30rpx;

.stepFlex{
  display: flex;
 padding: 0 24rpx 60rpx;


  .left-icon{
    width: 60rpx;
    color: #1FB5D7;
    .yuan-icon{
      width: 36rpx;
height: 36rpx;
background: #1FB5D7;
border-radius: 50%;
    }
    .line{
      margin-left: 17rpx;
      width: 2rpx;
   height: 135%;
  //  background: url('../../../images/addaddress/icon-4.png') repeat-y;
      border-right: 1rpx dotted #1FB5D7;
    }
  }
  .right-text{flex: 1;
  // display: flex;
  font-size: 24rpx;
  .title-date{
    padding-bottom: 14rpx;
    display: flex;
    align-items: center;
    font-size: 28rpx;
    .date{
      color: #999;
      flex: 1;
      font-size: 22rpx;
      text-align: right;
      padding-left: 20rpx;
    }
  }
  .audit{
    margin: 10rpx 0;
  }
  .audit-img{
    image,img{
      width: 200rpx;
      height: 200rpx;
border-radius: 20rpx;
      margin: 10rpx 20rpx 10rpx 0;
    }
  }
}
}
.stepFlex{
    &:last-child{
    .left-icon{
      .line{
        display: none;
      }
    }
  }
}
}

.title {
  text-align: center;
  margin: 20rpx 0 28rpx;
  font-weight: bold;
  font-size: 28rpx;
  color: #052031;
}

.desc {
  font-size: 26rpx;
  color: #052031;
  line-height: 52rpx;
  text-align: center;
}
