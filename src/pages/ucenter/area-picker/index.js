import { getAreaListApi } from "@/api/common";

Page({
  data: {
    provinces: [], // 省份列表
    cities: [], // 城市列表
    districts: [], // 区县列表

    selectedProvince: null, // 选中的省份
    selectedCity: null, // 选中的城市
    selectedDistrict: null, // 选中的区县

    currentLevel: 1, // 当前选择级别：1-省，2-市，3-区

    navData: {
      title: "选择地区",
    },
  },

  onLoad() {
    // 加载省份数据
    this.loadProvinces();
  },

  // 加载省份数据
  loadProvinces() {
    wx.showLoading({ title: "加载中..." });
    getAreaListApi({ parentId: 0 })
      .then((res) => {
        wx.hideLoading();
        if (res.code === 0) {
          this.setData({
            provinces: res.data,
            cities: [],
            districts: [],
            currentLevel: 1,
            navData: { title: "选择省份" },
          });
        }
      })
      .catch(() => {
        wx.hideLoading();
        wx.showToast({ title: "加载失败", icon: "none" });
      });
  },

  // 加载城市数据
  loadCities(provinceId, provinceName) {
    wx.showLoading({ title: "加载中..." });
    getAreaListApi({ parentId: provinceId })
      .then((res) => {
        wx.hideLoading();
        if (res.code === 0) {
          this.setData({
            cities: res.data,
            districts: [],
            selectedProvince: { id: provinceId, name: provinceName },
            currentLevel: 2,
            navData: { title: "选择城市" },
          });
        }
      })
      .catch(() => {
        wx.hideLoading();
        wx.showToast({ title: "加载失败", icon: "none" });
      });
  },

  // 加载区县数据
  loadDistricts(cityId, cityName) {
    wx.showLoading({ title: "加载中..." });
    getAreaListApi({ parentId: cityId })
      .then((res) => {
        wx.hideLoading();
        if (res.code === 0) {
          this.setData({
            districts: res.data,
            selectedCity: { id: cityId, name: cityName },
            currentLevel: 3,
            navData: { title: "选择区县" },
          });
        }
      })
      .catch(() => {
        wx.hideLoading();
        wx.showToast({ title: "加载失败", icon: "none" });
      });
  },

  // 选择省份
  selectProvince(e) {
    const { id, name } = e.currentTarget.dataset;
    this.loadCities(id, name);
  },

  // 选择城市
  selectCity(e) {
    const { id, name } = e.currentTarget.dataset;
    this.loadDistricts(id, name);
  },

  // 选择区县
  selectDistrict(e) {
    const { id, name } = e.currentTarget.dataset;

    // 构建完整的地区信息
    const areaData = {
      areaId: id,
      areaName: `${this.data.selectedProvince.name} ${this.data.selectedCity.name} ${name}`,
    };

    // 返回上一页并传递选择的地区数据
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];

    // 调用上一页的方法，传递地区数据
    prevPage.setData({
      areaId: areaData.areaId,
      areaName: areaData.areaName,
    });

    wx.navigateBack();
  },

  // 返回上一级
  goBack() {
    const { currentLevel } = this.data;

    if (currentLevel === 1) {
      wx.navigateBack();
    } else if (currentLevel === 2) {
      this.loadProvinces();
    } else if (currentLevel === 3) {
      this.loadCities(
        this.data.selectedProvince.id,
        this.data.selectedProvince.name,
      );
    }
  },
});
