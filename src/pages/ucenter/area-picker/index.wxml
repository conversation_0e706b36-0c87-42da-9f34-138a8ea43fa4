<PageHeader />
<NavBar nav-data="{{ navData }}" bind:back="goBack" />
<view class="area-picker">
  <!-- 省份列表 -->
  <view class="area-list" wx:if="{{ currentLevel === 1 }}">
    <view class="area-item" wx:for="{{ provinces }}" wx:key="id" bindtap="selectProvince" data-id="{{ item.id }}" data-name="{{ item.name }}">
      {{ item.name }}
    </view>
  </view>
  
  <!-- 城市列表 -->
  <view class="area-list" wx:if="{{ currentLevel === 2 }}">
    <view class="area-item" wx:for="{{ cities }}" wx:key="id" bindtap="selectCity" data-id="{{ item.id }}" data-name="{{ item.name }}">
      {{ item.name }}
    </view>
  </view>
  
  <!-- 区县列表 -->
  <view class="area-list" wx:if="{{ currentLevel === 3 }}">
    <view class="area-item" wx:for="{{ districts }}" wx:key="id" bindtap="selectDistrict" data-id="{{ item.id }}" data-name="{{ item.name }}">
      {{ item.name }}
    </view>
  </view>
</view>