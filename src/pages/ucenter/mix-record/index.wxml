<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result" style="margin-top: {{ paddingHeight }}px">
  <block wx:if="{{ isEmpty }}">
    <view>
      <image class="empty" src="https://k-cos.ai-market.top/wx/base/video/empty.png" />
      <view class="title">温馨提示</view>
      <view class="desc">系统未发现您的视频，您可以这样做： </view>
      <view class="desc">1.换个拍摄角度</view>
      <view class="desc">2.邀请您一起出游的朋友试试</view>
      <view class="desc">3.点击【订阅我的视频】，合成后将第一时间推送</view>

      <view class="footer">
        <view> 正在整理合成数据，请稍等.... </view>
        <view class="btn" bind:tap="gotoPhoto">
          <image class="btn-icon" src="/images/video/search.png" />
          <view>再搜索一次</view>
        </view>
      </view>
    </view>
  </block>
  <block wx:else>
    <scroll-view scroll-y="true" style="height: calc(100vh - {{paddingHeight}}px - 100rpx);" bindscrolltolower="loadMore" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
      <view class="result-item" wx:for="{{ list }}" wx:key="id" >
        <view class="result-item__position">
          <image class="result-icon" src="/images/video/position.png" />
          <view>{{ item.address }}</view>
        </view>

        <view class="result-card">
          <image class="result-card__bg" src="{{ item.mixVideoCoverUrl }}?v=0716"  />
        <view class="result-card__similar" wx:if="{{ item.buy }}">
          <view class="result-avatar" >购</view>
        </view>
        <view class="loading-animation" wx:if="{{ item.process }}"></view>
        <view class="result-card__footer" wx:if="{{ item.process }}" bind:tap="mergeVideo" data-id="{{ item.id }}" data-type="1">查看进度</view>
        <view class="result-card__footer left" wx:if="{{ item.taskStatus == 2 }}" bind:tap="mergeVideo" data-id="{{ item.id }}" data-type="1">再次合成</view>
        <view class="result-card__footer" wx:if="{{ item.taskStatus == -1 }}" bind:tap="customMerge" data-template="{{ item.siteTemplateId }}" data-site="{{ item.siteId }}" >抓拍不清晰，可上传素材合成→</view>
        <view class="result-card__footer right" wx:if="{{ item.taskStatus == 2 }}" bind:tap="mergeVideo" data-id="{{ item.id }}" data-type="2">{{ item.stateLabel }}</view>
        </view>
      </view>

      <view wx:if="{{ loading }}" class="loading">加载中...</view>
      <view wx:if="{{ noMore && list.length > 0 }}" class="no-more">没有更多视频了</view>
    </scroll-view>
  </block>
</view>


