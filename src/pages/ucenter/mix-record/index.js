import { queryMixVideoTaskApi } from "@/api/retrieval";
const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "我的VLOG",
    },
    list: [],
    state: [
      {
        id: 100,
        label: "视频检索中",
      },
      {
        id: 0,
        label: "等待合成",
      },
      {
        id: 1,
        label: "合成中",
      },
      {
        id: 2,
        label: "点击观看",
      },
      {
        id: -1,
        label: "合成失败",
      },
    ],
    process_state_list: [100, 0, 1],
    isEmpty: false,
    show: false,
    // 分页相关
    pageNo: 1,
    pageSize: 10,
    loading: false,
    noMore: false,
    refreshing: false,
  },
  onLoad() {
    this.queryMixVideoTask(true);
  },

  queryMixVideoTask(isRefresh = false) {
    if (this.data.loading) return;

    var userId = app.globalData.userInfo.userId;
    if (userId == 0 || userId == null) {
      console.log("未登陆 userId", userId);
      return;
    }

    const { pageNo, pageSize } = this.data;
    this.setData({ loading: true });

    let that = this;
    queryMixVideoTaskApi({
      userId: userId,
      pageNo: pageNo,
      pageSize: pageSize,
    })
      .then((res) => {
        console.log(res);

        let newList = res.data.list.map((item) => {
          let process_state = this.data.process_state_list.includes(
            item.taskStatus,
          );
          console.log("process_state=", process_state);
          item.process = false;
          if (process_state) {
            item.process = true;
          }
          let state = this.data.state.find((a) => a.id == item.taskStatus);
          item.stateLabel = state.label;
          return item;
        });

        if (isRefresh) {
          that.setData({
            list: newList,
            isEmpty: newList.length === 0,
            refreshing: false,
          });
        } else {
          that.setData({
            list: [...that.data.list, ...newList],
          });
        }

        that.setData({
          loading: false,
          noMore: newList.length < pageSize,
          pageNo: pageNo + 1,
        });
      })
      .catch((err) => {
        console.error("获取视频列表失败", err);
        that.setData({
          loading: false,
          refreshing: false,
        });
        wx.showToast({
          title: "获取视频列表失败",
          icon: "none",
        });
      });
  },
  onRefresh() {
    this.setData({
      refreshing: true,
      pageNo: 1,
      noMore: false,
    });
    this.queryMixVideoTask(true);
  },

  loadMore() {
    if (!this.data.noMore) {
      this.queryMixVideoTask();
    }
  },

  onClose() {
    this.setData({ show: false });
  },
  gotoPhoto() {
    wx.navigateTo({
      url: "/pages/selfie/index",
    });
  },
  mergeVideo(e) {
    let id = e.currentTarget.dataset.id;
    let taskStatus = e.currentTarget.dataset.taskStatus;
    let type = e.currentTarget.dataset.type;
    if (type == 1) {
      wx.navigateTo({
        url: "/pages/retrieval/video-result/index?id=" + id,
      });
    } else {
      let url = e.currentTarget.dataset.url;
      let img = e.currentTarget.dataset.img;
      wx.navigateTo({
        url:
          "/pages/retrieval/play/index?url=" +
          url +
          "&img=" +
          img +
          "&id=" +
          id,
      });
    }
  },
  /**
   * 自定义合成
   */
  customMerge(e) {
    let template = e.currentTarget.dataset.template;
    let site = e.currentTarget.dataset.site;
    wx.navigateTo({
      url:
        "/pages/retrieval/generate-video/index?templateId=" +
        template +
        "&siteId=" +
        site,
    });
  },
});
