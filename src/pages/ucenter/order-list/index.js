import {
  queryYingeOrderApi,
  cancelYingeOrderApi,
  confirmYingeOrderApi,
} from "@/api/order";
const app = getApp();

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "订单列表",
    },
    activeTab: "all",
    list: [],
    pageNo: 1,
    pageSize: 10,
    loading: false,
    noMore: false,
    isEmpty: false,
    refreshing: false,
    statusMap: {
      0: "待支付",
      10: "待发货",
      20: "已发货",
      30: "已完成",
      40: "已取消",
    },
    tabMap: {
      all: -1,
      pending: 0,
      shipping: 10,
      receiving: 20,
      completed: 30,
      cancelled: 40,
    },
    status: -1,
  },

  onLoad(options) {
    const status = options.status;
    // 处理status参数，如果没有或为空则设置为-1
    if (status === undefined || status === "" || status === null) {
      this.setData({
        activeTab: "all",
        status: -1,
      });
    } else {
      // 根据status值反向查找对应的tab名称
      const tabEntries = Object.entries(this.data.tabMap);
      for (const [tab, value] of tabEntries) {
        if (value == status) {
          this.setData({
            activeTab: tab,
            status: status,
          });
          break;
        }
      }
    }
    this.loadOrderList(true);
  },

  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab,
      pageNo: 1,
      list: [],
      noMore: false,
    });
    this.loadOrderList(true);
  },

  loadOrderList(isRefresh = false) {
    if (this.data.loading) return;
    const { pageNo, pageSize, activeTab, tabMap } = this.data;
    this.setData({ loading: true });
    let queryParams = {
      userId: app.globalData.userInfo.userId,
      pageNo: pageNo,
      pageSize: pageSize,
    };
    const statusValue = tabMap[activeTab];
    if (statusValue !== -1) {
      queryParams.status = statusValue;
    } else {
      queryParams.status = -1;
    }

    queryYingeOrderApi(queryParams)
      .then((res) => {
        const newList = res.data.list.map((item) => {
          return {
            ...item,
            totalPrice: (item.totalPrice / 100).toFixed(2),
            statusText: this.data.statusMap[item.status] || "未知状态",
          };
        });
        if (isRefresh) {
          this.setData({
            list: newList,
            isEmpty: newList.length === 0,
            refreshing: false,
          });
        } else {
          this.setData({
            list: [...this.data.list, ...newList],
          });
        }
        this.setData({
          loading: false,
          noMore: newList.length < pageSize,
          pageNo: pageNo + 1,
        });
      })
      .catch((err) => {
        console.error("获取订单列表失败", err);
        this.setData({
          loading: false,
          refreshing: false,
        });
        wx.showToast({
          title: "获取订单列表失败",
          icon: "none",
        });
      });
  },

  loadMore() {
    if (!this.data.noMore) {
      this.loadOrderList();
    }
  },

  onRefresh() {
    this.setData({
      refreshing: true,
      pageNo: 1,
      noMore: false,
    });
    this.loadOrderList(true);
  },

  gotoOrderDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ucenter/order-detail/index?no=${id}`,
    });
  },

  cancelOrder(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: "提示",
      content: "确定要取消该订单吗？",
      success: (res) => {
        if (res.confirm) {
          cancelYingeOrderApi({
            oid: id,
            reason: "用户取消",
          }).then((res) => {
            if (res.code == 0 && res.data == true) {
              wx.showToast({
                title: "订单已取消",
                icon: "success",
              });
              this.onRefresh();
            } else {
              wx.showToast({
                title: res.msg,
                icon: "none",
              });
            }
          });
        }
      },
    });
  },

  continuePayment(e) {
    const id = e.currentTarget.dataset.id;
    // 这里需要添加继续支付的逻辑
    wx.showToast({
      title: "跳转支付...",
      icon: "loading",
    });
  },

  //确认收货
  confirmReceipt(e) {
    const oid = e.currentTarget.dataset.id;
    wx.showModal({
      title: "提示",
      content: "确认已收到商品？",
      success: (res) => {
        if (res.confirm) {
          // 这里确认收货的API调用
          confirmYingeOrderApi({
            no: oid,
          }).then((res) => {
            if (res.code == 0 && res.data == true) {
              wx.showToast({
                title: "订单已确认",
                icon: "success",
              });
              this.onRefresh();
            } else {
              wx.showToast({
                title: res.msg,
                icon: "none",
              });
            }
          });
          wx.showToast({
            title: "确认收货成功",
            icon: "success",
          });
          this.onRefresh();
        }
      },
    });
  },

  applyRefund(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ucenter/refund/index?id=${id}`,
    });
  },
});
