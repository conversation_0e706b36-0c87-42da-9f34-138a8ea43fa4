<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result" style="margin-top: {{ paddingHeight }}px">
  <view class="tab-container">
    <view class="tab-item {{activeTab === 'all' ? 'active' : ''}}" data-tab="all" bind:tap="switchTab">
      <view class="text-zi">
        <view class="nav">全部</view>
        <view class="after-line"></view>
      </view>


    </view>
    <view class="tab-item {{activeTab === 'pending' ? 'active' : ''}}" data-tab="pending" bind:tap="switchTab">
      <view class="text-zi">
        <view class="nav">待付款</view>
        <view class="after-line"></view>
      </view>
    </view>
    <view class="tab-item {{activeTab === 'shipping' ? 'active' : ''}}" data-tab="shipping" bind:tap="switchTab">
      <view class="text-zi">
        <view class="nav">待发货</view>
        <view class="after-line"></view>
      </view>
    </view>
    <view class="tab-item {{activeTab === 'receiving' ? 'active' : ''}}" data-tab="receiving" bind:tap="switchTab">
      <view class="text-zi">
        <view class="nav">已发货</view>
        <view class="after-line"></view>
      </view>
    </view>
    <view class="tab-item {{activeTab === 'completed' ? 'active' : ''}}" data-tab="completed" bind:tap="switchTab">
      <view class="text-zi">
        <view class="nav">已完成</view>
        <view class="after-line"></view>
      </view>
    </view>
    <view class="tab-item {{activeTab === 'cancelled' ? 'active' : ''}}" data-tab="cancelled" bind:tap="switchTab">
      <view class="text-zi">
        <view class="nav">已取消</view>
        <view class="after-line"></view>
      </view>
    </view>
    
  </view>

  <block wx:if="{{ isEmpty }}">
    <view>
      <image class="empty" src="https://k-cos.ai-market.top/wx/base/video/empty.png" />
      <view class="title">暂无订单</view>
      <view class="desc">您还没有相关订单</view>
    </view>
  </block>
  <block wx:else>
    <scroll-view scroll-y="true" style="height: calc(100vh - {{paddingHeight}}px - 100rpx);" bindscrolltolower="loadMore" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
      <view class="result-item" wx:for="{{ list }}" wx:key="id">
        <view class="order-header" bind:tap="gotoOrderDetail" data-id="{{ item.no }}">
          <view class="order-number">订单号: {{ item.no }}</view>
          <view class="order-status {{item.statusText=='已取消'||item.statusText=='已完成' ? 'color052031' : 'colorFF4F28'}}">{{ item.statusText }}</view>
        </view>
        <view class="order-main" bind:tap="gotoOrderDetail" data-id="{{ item.no }}">
          <image class="order-img" src="{{ item.productImg || 'https://k-cos.ai-market.top/wx/product/dz-1.png' }}" />
          <view class="order-info">
            <view class="product-name">{{ item.title }}</view>
            <view class="product-spec">规格：默认</view>
          </view>
          <view class="product-quantity">x{{ item.quantity || 1 }}</view>
        </view>
        <view class="order-bottom">
          <view class="order-total">总额：<text class="price">￥<text class="text">{{ item.totalPrice }}</text></text></view>
          <view class="order-actions">
            <button class="btn cancel" wx:if="{{item.status < 20}}" bind:tap="cancelOrder" data-id="{{ item.no }}">取消订单</button>
            <button class="btn confirm" wx:if="{{item.status == 20}}" bind:tap="confirmReceipt" data-id="{{ item.no }}">确认收货</button>

          </view>
        </view>
      </view>

      <view wx:if="{{ loading }}" class="loading">加载中...</view>
      <view wx:if="{{ noMore }}" class="no-more">没有更多订单了</view>
    </scroll-view>
  </block>
</view>