page {
  background-color: #EBFBFC;
}

.result {
  padding: 0 24rpx 40rpx;
  font-size: 32rpx;
  color: #052031;
}

.tab-container {
  display: flex;
  background-color: #FFFFFF;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
   justify-content: flex-start; 
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  color: #052031;
  padding: 10rpx 0;
  position: relative;
  min-width: 130rpx;
}

.tab-item.active {
  color: #052031;
  font-weight: 600;
  position: relative;
  font-size: 36rpx;
  padding-top: 4rpx;
}

.tab-item.active .text-zi {
  display: inline-block;
}

.tab-item.active  .nav {
  position: relative;
  z-index: 10;
  display: block;
}

.tab-item.active  .after-line {
  bottom: 7rpx;
  z-index: 1;
  background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
  border-radius: 2rpx;
  height: 4rpx;
  position: relative;
  width: 100%;

}

.empty {
  display: block;
  width: 332rpx;
  height: 266rpx;
  margin: 200rpx auto 0;
}

.title {
  text-align: center;
  margin: 20rpx 0 28rpx;
  font-weight: bold;
  font-size: 40rpx;
  color: #052031;
}

.desc {
  font-size: 26rpx;
  color: #052031;
  line-height: 52rpx;
  text-align: center;
}

.result-item {
  background: #fff;
  border-radius: 18rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(11, 182, 255, 0.08);
  margin-bottom: 32rpx;
  padding: 28rpx 24rpx 20rpx 24rpx;
  display: flex;
  flex-direction: column;
  gap: 18rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 26rpx;
 color: rgba(5,32,49,0.5);
  margin-bottom: 0;
  font-family: PingFang SC, PingFang SC;
  .colorFF4F28{
    color: #FF4F28;
    font-weight: 500;
  }
  .color052031{
    color: #052031;font-weight: 500;
  }
  .order-status {
    font-weight: 500;

    &.canceled {
      color: #8a9399;
    }

    &.shipping {
      color: #ff7e2d;
    }

    &.receiving {
      color: #0bb6ff;
    }

    &.completed {
      color: #52c41a;
    }
  }
}

.order-main {
  display: flex;
  align-items: center;
  gap: 18rpx;

  .order-img {
    width: 136rpx;
    height: 136rpx;
    border-radius: 12rpx;
    object-fit: cover;
    background: #f6fbfd;
    flex-shrink: 0;
  }

  .order-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10rpx;

    .product-name {
      font-size: 30rpx;
      color: #222;
      margin-bottom: 0;
      line-height: 1.3;
      word-break: break-all;
      height: 78rpx;
      font-weight: 600;  -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
     text-overflow: ellipsis;
     width: 100%;
     position: relative;
    
    }

    .product-spec {
      font-size: 24rpx;
      color: #052031;
    }
  }

  .product-quantity {
    font-size: 26rpx;
    color: #8a9399;
    margin-left: 18rpx;
    flex-shrink: 0;
  }
}

.order-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;

  .order-total {
    font-size: 26rpx;
    color: #222;

    .price {
      color: #222;
    
      font-size: 24rpx;
      .text{  font-weight: 700;
        font-size: 32rpx;
      }
    }
  }

  .order-actions {
    display: flex;
    gap: 18rpx;

    .btn {
      min-width: 120rpx;
      padding: 0 24rpx;
      height: 56rpx;
      line-height: 56rpx;
      border-radius: 32rpx;
      font-size: 26rpx;
      border: 1rpx solid #e6e6e6;
      background: #fff;
      color: #052031;

      &.confirm {
        background: linear-gradient(90deg, #0bb6ff 0%, #1ee6b6 100%);
        color: #fff;
        border: none;
        font-weight: 700;
      }

      &.cancel,
      &.refund {
        color: #052031;
        border: 1rpx solid rgba(5,32,49,0.2);
        background: #fff;
   line-height: 50rpx;
      
      }
    }
  }
}

.loading,
.no-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #999999;
}

/* 选项卡导航样式 */
.tab-header {
  display: flex;
  height: 80rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  color: #666;
  font-size: 28rpx;
}

.tab-nav-item.active {
  color: #07c160;
  /* 激活时文字为绿色 */
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 0;
  height: 4rpx;
  background: transparent;
  transition: all 0.3s;
}

.tab-nav-item.active .tab-line {
  width: 40rpx;
  background: #07c160;
  /* 激活时下方有绿色下划线 */
}

/* 内容区域样式 */
.tab-container {
  padding: 20rpx;
  white-space: nowrap;
  overflow-x: auto;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
  }
}

.data-item {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;

}
