page {
  background-color: #EBFBFC;
}

.photo {
  padding: 40rpx 24rpx 300rpx;

  .tips {
    text-align: center;
    font-size: 24rpx;
    color: #000000;
    opacity: 0.5;
    margin-top: 20rpx;
  }

  .status {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    background: #FF4F28;
    border-radius: 100rpx 100rpx 100rpx 100rpx;
    padding: 10rpx 28rpx;
    color: #ffffff;
    font-size: 24rpx;
  }

  &-item {
    position: relative;
    margin-bottom: 40rpx;

    &__current {
      width: 100%;
      height: 396rpx;
    }

    .checkbox {
      position: absolute;
      top: 24rpx;
      left: 24rpx;
    }
  }

  .footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: calc(100% - 56rpx);
    height: 112rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #FFFFFF;
    box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(54, 127, 137, 0.1);
    border-radius: 32rpx 32rpx 0rpx 0rpx;
    padding: 16rpx 32rpx 84rpx 24rpx;
    font-size: 28rpx;
    color: #052031;

    .all {
      margin-left: 8rpx;
    }

    &-right {
      display: flex;
      align-items: center;
    }

    &-left {
      display: flex;
      align-items: center;
    }

    .money {
      font-weight: bold;
      font-size: 36rpx;
      color: #FF4F28;
    }

    .total {
      font-size: 24rpx;
    }

    .btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 258rpx;
      height: 80rpx;
      background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
      border-radius: 100rpx 100rpx 100rpx 100rpx;
      font-weight: bold;
      font-size: 32rpx;
      color: #052031;
      margin-left: 20rpx;
    }
  }
}

.checkbox {
  width: 48rpx;
  height: 48rpx;
}