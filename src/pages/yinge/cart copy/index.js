const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "专属照片",
    },
    list: [
      {
        id: 1,
        img: "https://k-cos.ai-market.top/wx/base/logo-launch.png",
        checked: false,
      },
    ],
    selectAll: false,
    productId: "",
    thumbUrl: "",
    customizeNo: "",
  },

  onLoad(options) {
    console.log(" order options", options);
    //制作完成传递给业务方定制单号
    let customizeNo = options.customizeNo;
    // 缩略图
    let thumbUrl = options.thumbUrl;
    let productId = options.productId;
    this.setData({
      productId,
      thumbUrl,
      customizeNo,
    });
  },

  handleTap(e) {
    const index = e.currentTarget.dataset.index;
    const list = this.data.list;
    if (list[index].pay) return;
    list[index].checked = !list[index].checked;
    const selectAll = list.every((item) => item.checked || item.pay);
    this.setData({
      list,
      selectAll,
    });
  },

  handleSelectAll() {
    const list = this.data.list;
    const selectAll = list.every((item) => item.checked || item.pay);
    list.forEach((item) => {
      item.checked = !selectAll;
    });
    this.setData({
      list,
      selectAll: !selectAll,
    });
  },
});
