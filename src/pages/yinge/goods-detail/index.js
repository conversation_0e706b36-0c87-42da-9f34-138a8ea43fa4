const app = getApp();
const util = require("@/utils/util.js");
const { productDetailApi } = require("@/api/product");

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "商品详情",
    },
    goods: {},
    id: "",
    skuId: "",
  },
  onLoad: function (options) {
    console.log(options);
    let id = options.id;
    this.setData({
      id: id,
    });
    this.getProductDetail();
  },
  submitCustom: function () {
    // 这里先默认取第一个
    let skuId = this.data.goods.skus[0].id;
    console.log("skuId", skuId);

    console.log("submit");
    wx.navigateTo({
      url: `/pages/yinge/webview/index?outProductId=${this.data.goods.yinGeSpuId}&productId=${this.data.goods.id}&skuId=${skuId}`,
    });
  },

  // 从富文本描述中提取图片URL
  extractImagesFromDescription: function (description) {
    if (!description) return [];

    // 使用正则表达式匹配所有img标签的src属性
    const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
    const images = [];
    let match;

    while ((match = imgRegex.exec(description)) !== null) {
      images.push(match[1]);
    }

    return images;
  },

  getProductDetail: function () {
    let that = this;
    productDetailApi({
      id: that.data.id,
    }).then((res) => {
      console.log("getProductDetail", res);
      let data = res.data;
      data.price = (data.price / 100).toFixed(2);

      // 从description中提取图片URL构建detailImages
      if (data.description) {
        data.detailImages = that.extractImagesFromDescription(data.description);
        console.log("提取的详情图片:", data.detailImages);
      } else {
        data.detailImages = [];
      }

      // 处理introduction，如果为空字符串或只包含空格则设为null
      if (data.introduction && data.introduction.trim() === "") {
        data.introduction = null;
      }

      that.setData({
        goods: data,
      });
    });
  },
  callbackTest: function () {
    console.log("callbackTest");

    // 模拟回调参数
    let customizeNo = "CUSTOM_" + Date.now(); // 生成一个模拟的定制编号
    let thumbUrl = "https://k-cos.ai-market.top/wx/product/dz-3.png";
    // 需要对thumbUrl进行编码，以便在URL中传递
    thumbUrl = encodeURIComponent(thumbUrl);

    // 构建带有模拟参数的URL
    const url = `/pages/yinge/cart/index?productId=${this.data.goods.id}&customizeNo=${customizeNo}&thumbUrl=${thumbUrl}`;
    console.log("模拟回调URL:", url);

    wx.navigateTo({
      url: url,
    });
  },
});
