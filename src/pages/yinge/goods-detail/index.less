page {
  background-color: #EBFBFC;
}

.detail {
  position: relative;
  padding-top: 16rpx;
  z-index: 20;

  .detail-images {
    margin: 24rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 20rpx;
  }

  .imgs {
    width: 100%;
    height: auto;
    display: block;
    margin-bottom: 30rpx;
    border-radius: 10rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .zero {
    font-size: 0;
    padding-bottom: 200rpx;
  }

  .banner {
    width: 100%;
    height: 750rpx;

    &-img,
    &-swiper {
      width: 100%;
      height: 750rpx;
    }
  }

  .content {
    margin: 24rpx;
    padding: 18rpx 24rpx 32rpx;
    background-color: #ffffff;
    border-radius: 20rpx;

    &-tips {
      opacity: .5;
      color: #052031;
      font-size: 24rpx;
    }

    .price {
      display: flex;
      align-items: baseline;
      color: #ff4f28;
      font-size: 24rpx;
    }

    .unit {
      font-size: 24rpx;
    }

    .price {
      font-size: 56rpx;
    }

    .medium {
      font-size: 36rpx;
      margin-right: 8rpx;
    }

    .title {
      margin-top: 20rpx;
      font-weight: bold;
      font-size: 40rpx;
      color: #052031;
    }
  }

  .desc {
    margin: 24rpx;
    display: flex;
    align-items: center;
    padding: 30rpx 24rpx;
    color: #052031;
    font-size: 24rpx;
    background-color: #ffffff;
    border-radius: 20rpx;

    &-tips {
      margin-right: 16rpx;
      opacity: .5;
    }
  }

  .intro {
      flex: 1;
      margin: 24rpx;
      padding: 30rpx 24rpx;
      color: #052031;
      font-size: 24rpx;
      background-color: #ffffff;
      border-radius: 20rpx;
      word-break: break-all;
      white-space: normal;
      overflow-wrap: break-word;
      &-content {
        width: 100%;
        word-break: break-all;
        white-space: normal;
        overflow-wrap: break-word;
      }

  }

  .tips {
    margin: 40rpx auto 20rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #052031;
    opacity: .5;
    text-align: center;
  }

  .bottom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 186rpx;
    background-color: #ffffff;

    .btn {
      margin: 20rpx auto 0;
      font-size: 36rpx;
      color: #052031;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 622rpx;
      height: 88rpx;
      background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
      border-radius: 100rpx 100rpx 100rpx 100rpx;
    }
  }
}
