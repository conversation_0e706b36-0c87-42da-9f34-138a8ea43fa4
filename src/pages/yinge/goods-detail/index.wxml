<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="detail" style="margin-top: {{ paddingHeight }}px">
  <view class="banner">
    <swiper
      indicator-dots
      autoplay
      indicator-active-color="#ffffff"
      indicator-color="rgba(255,255,255,.3)"
      circular
      class="banner-swiper"
    >
      <swiper-item wx:for="{{ goods.sliderPicUrls }}" wx:key="id">
        <image class="banner-img" src="{{ item }}" />
      </swiper-item>
    </swiper>
  </view>
  <view class="content">
    <view class="price">
      <view class="unit">￥</view>
      <view class="big">{{ goods.price}}.</view>
      <view class="content-tips">{{ 1 }}件起售</view>
    </view>
    <view class="title">{{ goods.name }}</view>
  </view>

  <view class="desc">
    <view class="desc-tips">发货</view>
    <view>现在下单，预计3个工作日内陆续发货</view>
  </view>

  <view class="tips">- 宝贝详情 -</view>
  <view class="intro" wx:if="{{ goods.introduction }}">
    <view class="intro-content">{{ goods.introduction }}</view>
  </view>
  <view class="detail-images" >
    <image wx:for="{{ goods.detailImages }}" wx:key="index" class="imgs" src="{{ item }}" mode="widthFix" />
  </view>



  <button style="width: 60%;height: 100px; color: #000;margin-bottom: 120rpx; display: none;"
  bind:tap="callbackTest">回调测试</button>

  <view class="zero"></view>
  <view class="bottom" bind:tap="submitCustom">
    <view class="btn">立即定制</view>
  </view>
</view>
