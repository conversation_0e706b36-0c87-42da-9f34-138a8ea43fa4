const app = getApp();
Page({
  data: {
    navData: {
      title: "商务合作",
    },
    url: "",
    outProductId: "",
    productId: "",
  },

  onLoad(options) {
    console.log(options);
    let outProductId = options.outProductId;
    let productId = options.productId;
    let skuId = options.skuId;

    let callback = "/pages/yinge/cart/index?scene=yinge&productId=" + productId;
    // 对callback进行encodeURIComponent
    callback = encodeURIComponent(callback);

    let url = `https://songzhaopian.com/reseller-wenzang?outProductId=${outProductId}&outUserId=${app.globalData.userInfo.userCode}&outSkuId=${productId}&callback=${callback}`;

    console.log("url=", url);

    this.setData({
      url: url,
      outProductId: outProductId,
      productId: productId,
    });
  },
});
