const app = getApp();
const util = require("@/utils/util.js");
const { productListApi } = require("@/api/product");

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "订单提交成功",
    },
    showHistory: false,
    list: [
    ],
    originList: [
    ],
  },
  onLoad: function () {
    console.log("onLoad");
    this.getProductList();
  },

  gotoGoodsDetail: function (e) {
    console.log(e);
    let id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/yinge/goods-detail/index?id=${id}`,
    });
  },
  getProductList: function () {
    let that = this;
    productListApi({
      page: 1,
      pageSize: 10,
    }).then((res) => {
      console.log(res);

       let list = res.data.list;
      // 价格转元，保留两位小数
      list.forEach((item) => {
        item.price = (item.price / 100).toFixed(2);
      });

       this.setData({
        list: list,
        originList: list,
       });
    });
  },
  checkOrder: function (e) {
    console.log(e);
    wx.navigateTo({
      url: `/pages/ucenter/order-list/index`,
    });
  },
});
