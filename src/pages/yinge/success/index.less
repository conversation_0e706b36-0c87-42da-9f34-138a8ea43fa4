page {
  background-color: #EBFBFC;
}

.search {
  padding: 40rpx 24rpx;

  &-ipt {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    background: #FFFFFF;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(54, 127, 137, 0.1);
    border-radius: 100rpx 100rpx 100rpx 100rpx;
    padding: 0 24rpx 0 12rpx;
    box-sizing: border-box;
    margin-bottom: 40rpx;
  }

  .right {
    display: flex;
    align-items: center;
    flex: 1;
  }

  &-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
  }

  &-input {

    flex: 1;
  }

  &-btn {
    width: 64rpx;
    height: 64rpx;
    margin-left: 20rpx;
  }

  .card {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    &-item {
      width: 342rpx;
      border-radius: 30rpx;
      background-color: #ffffff;
      overflow: hidden;
      margin-bottom: 20rpx;
    }

    &-img {
      width: 100%;
      height: 342rpx;
    }

    &-title {
      font-weight: bold;
      font-size: 24rpx;
      color: #052031;
      padding: 10rpx 20rpx 0;
    }

    &-desc {
      font-weight: 500;
      font-size: 20rpx;
      color: #052031;
      opacity: .5;
      margin: 4rpx 0 10rpx;
      padding: 0 20rpx;
    }

    &-price {
      display: flex;
      font-weight: bold;
      font-size: 36rpx;
      color: #FF4F28;
      padding: 0 20rpx 20rpx;
      align-items: baseline;

      .unit {
        font-size: 24rpx;
      }
    }

  }

}

.title {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  color: #333333;
  margin: 36rpx 0 29rpx;
  font-weight: bold;

  &-icon {
    width: 8rpx;
    height: 32rpx;
    padding-left: 2rpx;
    margin-right: 13rpx;
  }
}


.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.success-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 30rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

.success-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 40rpx;
}

.check-order-btn {
  width: 300rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
  border-radius: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #052031;
  font-size: 30rpx;
  font-weight: 500;
}
