<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="search" style="margin-top: {{ paddingHeight }}px">
  
  <view class="success-container">
    <view class="success-icon">
      <image src="/images/yinge/order_success.png" mode="aspectFit"></image>
    </view>
    <view class="success-text">订单提交成功</view>
    <view class="check-order-btn" bind:tap="checkOrder">查看订单</view>
  </view>

  

  <view class="title">
    <image class="title-icon" src="/images/home/<USER>" />
    <view class="title-text">精品推荐</view>
  </view>


  <view wx:if="{{ !showHistory }}" class="card">
    <view class="card-item" wx:for="{{ list }}" wx:key="id" bind:tap="gotoGoodsDetail" data-id="{{ item.id }}">
      <image class="card-img" src="{{ item.picUrl }}" />
      <view class="card-title">{{ item.name }}</view>
      <view class="card-desc">{{ item.desc }}</view>
      <view class="card-price">
        <view class="unit">￥</view>
        <view>{{ item.price }}</view>
      </view>
    </view>
  </view>


</view>
