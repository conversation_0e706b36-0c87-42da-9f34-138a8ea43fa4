page {
  background-color: #ebfbfc;
}
.order-pay {
  padding: 40rpx 24rpx 300rpx;
  .address-section {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
    .address-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 16rpx;
    }
    .address-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
    .address-arrow {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .goods-card {
    display: flex;
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
    .goods-img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      margin-right: 24rpx;
    }
    .goods-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      .goods-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #222;
        margin-bottom: 8rpx;
      }
      .goods-spec {
        font-size: 24rpx;
        color: #888;
        margin-bottom: 16rpx;
      }
      .goods-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .goods-price {
          color: #ff4d4f;
          font-size: 32rpx;
          font-weight: bold;
        }
        .goods-qty {
          display: flex;
          align-items: center;
          .qty-btn {
            width: 48rpx;
            height: 48rpx;
            background: #f3fafd;
            border: 1rpx solid #ddd;
            border-radius: 50%;
            text-align: center;
            line-height: 40rpx;
            font-size: 32rpx;
            color: #333;
          }
          .qty-num {
            margin: 0 16rpx;
            font-size: 28rpx;
          }
        }
      }
    }
  }
  .price-section {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
    .price-row {
      display: flex;
      justify-content: space-between;
      font-size: 28rpx;
      margin-bottom: 12rpx;
      .total-price {
        color: #ff4d4f;
        font-weight: bold;
      }
    }
  }
  .footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: calc(100% - 56rpx);
    height: 112rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(54, 127, 137, 0.1);
    border-radius: 32rpx 32rpx 0rpx 0rpx;
    padding: 16rpx 32rpx 84rpx 24rpx;
    font-size: 28rpx;
    color: #052031;
    .footer-left {
      font-size: 24rpx;
      color: #888;
      margin-right: 24rpx;
    }
    .footer-right {
      display: flex;
      align-items: center;
      .total {
        font-size: 24rpx;
      }
      .money {
        font-weight: bold;
        font-size: 36rpx;
        color: #ff4f28;
      }
      .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 258rpx;
        height: 80rpx;
        background: linear-gradient(90deg, #2fd2f8 0%, #2ff8a8 100%);
        border-radius: 100rpx;
        font-weight: bold;
        font-size: 32rpx;
        color: #052031;
        margin-left: 20rpx;
      }
    }
  }
}