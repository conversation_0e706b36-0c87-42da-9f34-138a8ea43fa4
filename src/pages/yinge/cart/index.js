const app = getApp();
const util = require("@/utils/util.js");
const { productDetailApi } = require("@/api/product");
const { yingeCreateOrderApi } = require("@/api/order");
const { getAddressListApi } = require("@/api/user");
import { payOrderApi } from "@/api/pay";
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "定制",
    },
    selectAll: false,
    productId: "",
    thumbUrl: "",
    customizeNo: "",
    goods: {},
    addressList: [],
    currentAddress: {},
    prePayInfo: {},
  },

  onLoad(options) {
    console.log(" order options", options);
    //制作完成传递给业务方定制单号
    let customizeNo = options.customizeNo;
    // 缩略图
    let thumbUrl = options.thumbUrl;
    // thumbUrl decode
    thumbUrl = decodeURIComponent(thumbUrl);
    let productId = options.productId;
    this.setData({
      productId,
      thumbUrl,
      customizeNo,
    });
    this.getProductDetail();
    this.getAddressList();
  },

  handleTap(e) {
    const index = e.currentTarget.dataset.index;
    const list = this.data.list;
    if (list[index].pay) return;
    list[index].checked = !list[index].checked;
    const selectAll = list.every((item) => item.checked || item.pay);
    this.setData({
      list,
      selectAll,
    });
  },

  handleSelectAll() {
    const list = this.data.list;
    const selectAll = list.every((item) => item.checked || item.pay);
    list.forEach((item) => {
      item.checked = !selectAll;
    });
    this.setData({
      list,
      selectAll: !selectAll,
    });
  },
  getProductDetail: function () {
    let that = this;
    productDetailApi({
      id: that.data.productId,
    }).then((res) => {
      console.log("getProductDetail", res);
      let data = res.data;
      data.price = (data.price / 100).toFixed(2);
      that.setData({
        goods: data,
      });
    });
  },
  submitOrder() {
    wx.showLoading({
      title: "提交订单中",
      mask: true,
    });
    let post_data = {
      pid: this.data.productId,
      customizeNo: this.data.customizeNo,
      customizeCoverUrl: this.data.thumbUrl,
      addressId: this.data.currentAddress.id,
      quantity: 1,
    };
    yingeCreateOrderApi(post_data).then((res) => {
      console.log("yingeCreateOrderApi", res);
      this.payOrder(res.data.payOrderId);
    });
  },
  // 支付
  payOrder(payOrderId) {
    console.log("payOrderId=", payOrderId, app.globalData.userInfo.openid);
    let that = this;
    wx.showLoading({
      title: "获取支付信息...",
    });
    payOrderApi({
      id: payOrderId,
      channelCode: "wx_lite",
      channelExtras: { openid: app.globalData.userInfo.openId },
    }).then((res) => {
      wx.hideLoading();
      console.log("res=", res);
      that.setData({
        prePayInfo: res.data,
      });
      that.callWxPay();
    });
  },
  //调用微信支付
  callWxPay() {
    let that = this;
    // 解析displayContent为对象
    let prePayInfo = that.data.prePayInfo;
    if (prePayInfo && prePayInfo.displayContent) {
      // 将displayContent字符串解析为JSON对象
      let paymentParams = JSON.parse(prePayInfo.displayContent);
      console.log("支付参数:", paymentParams);
      // 发起微信支付
      wx.requestPayment({
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.packageValue,
        signType: paymentParams.signType,
        paySign: paymentParams.paySign,
        success: function (res) {
          console.log("res=", res);
          that.setData({
            payStatus: 1,
          });
          wx.navigateTo({
            url: `/pages/yinge/success/index`,
          });
        },
        fail: function (res) {
          console.log("res=", res);
          wx.showToast({
            title: "支付失败",
            duration: 2000,
          });
        },
      });
    }
  },
  getAddressList() {
    getAddressListApi({ a: 1 }).then((res) => {
      console.log("getAddressList", res);
      let addressList = res.data;
      let currentAddress = {};
      console.log("addressList.length ", addressList.length);
      if (addressList.length > 0) {
        currentAddress = addressList[0];
      }
      this.setData({
        addressList: res.data,
        currentAddress: currentAddress,
      });
    });
  },
  previewImage(e) {
    const image = this.data.thumbUrl;
    wx.previewImage({
      urls: [image],
    });
  },
  gotoAddressList() {
    wx.navigateTo({
      url: "/pages/ucenter/address-list/index?from=cart",
    });
  },
});
