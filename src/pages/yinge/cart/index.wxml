<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="order-pay" style="margin-top: {{ paddingHeight }}px">
  <!-- 地址栏 -->
  <view class="address-section" bindtap="onAddAddress">
    <image class="address-icon" src="/images/yinge/location.png" />
    <text class="address-text" wx:if="{{!currentAddress.detailAddress}}" bindtap="gotoAddressList">添加收货地址</text>
     <text class="address-text"  >{{currentAddress.detailAddress}}</text>
    <image class="address-arrow" src="/images/yinge/arrow-right.png" bindtap="gotoAddressList" />
  </view>

  <!-- 商品卡片 -->
  <view class="goods-card">
    <image class="goods-img" src="{{ thumbUrl }}"  bindtap="previewImage"  />
    <view class="goods-info">
      <view class="goods-title">{{ goods.name }}</view>
      <view class="goods-spec">规格：默认</view>
      <view class="goods-bottom">
        <view class="goods-price">￥{{ goods.price }}</view>
        <view class="goods-qty">
          <view class="qty-btn" bindtap="decreaseQty" wx:if="{{false}}">-</view>
          <view class="qty-num">{{ 1 }} 件</view>
          <view class="qty-btn" bindtap="increaseQty" wx:if="{{false}}">+</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 运费和总价 -->
  <view class="price-section">
    <view class="price-row">
      <text>运费</text>
      <text>￥{{ 0 }}</text>
    </view>
    <view class="price-row">
      <text>商品总价</text>
      <text class="total-price">￥{{ goods.price }}</text>
    </view>
  </view>

  <!-- 底部结算栏 -->
  <view class="footer">
    <view class="footer-left">
      <text class="footer-shipping">含运费￥{{ 0 }}元</text>
    </view>
    <view class="footer-right">
      <view class="total">合计：</view>
      <view class="money">￥{{ goods.price }}</view>
      <view class="btn" bindtap="submitOrder">提交订单</view>
    </view>
  </view>
</view>
