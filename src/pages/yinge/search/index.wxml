<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="search" style="margin-top: {{ paddingHeight }}px">
  <view class="search-ipt">
    <view class="right">
      <image class="search-icon" src="/images/search/icon1.png" bind:tap="search"  />
      <input class="search-input" placeholder="搜索定制产品等" value="{{ searchValue }}" bind:input="searchInput" />
    </view>
    <image class="search-btn" src="/images/search/icon2.png" bind:tap="search" />
  </view>

  <view wx:if="{{ !showHistory }}" class="card">
    <view class="card-item" wx:for="{{ list }}" wx:key="id" bind:tap="gotoGoodsDetail" data-id="{{ item.id }}">
      <image class="card-img" src="{{ item.picUrl }}" />
      <view class="card-title">{{ item.name }}</view>
      <view class="card-desc">{{ item.desc }}</view>
      <view class="card-price">
        <view class="unit">￥</view>
        <view>{{ item.price }}</view>
      </view>
    </view>
  </view>

  <view wx:else class="history">
    <view class="history-title">
      <view>历史搜索</view>
      <view class="history-btn" bind:tap="clearHistory">
        <image class="del" src="/images/search/del.png" />
        <view>删除历史</view>
      </view>
    </view>

    <view class="history-body">
      <view class="history-item" wx:for="{{ historyList }}" wx:key="index">
        {{ item }}
      </view>
    </view>
  </view>
</view>
