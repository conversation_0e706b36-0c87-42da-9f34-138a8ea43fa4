page {
  background-color: #EBFBFC;
}

.search {
  padding: 40rpx 24rpx;

  &-ipt {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    background: #FFFFFF;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(54, 127, 137, 0.1);
    border-radius: 100rpx 100rpx 100rpx 100rpx;
    padding: 0 24rpx 0 12rpx;
    box-sizing: border-box;
    margin-bottom: 40rpx;
  }

  .right {
    display: flex;
    align-items: center;
    flex: 1;
  }

  &-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
  }

  &-input {

    flex: 1;
  }

  &-btn {
    width: 64rpx;
    height: 64rpx;
    margin-left: 20rpx;
  }

  .card {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    &-item {
      width: 342rpx;
      border-radius: 30rpx;
      background-color: #ffffff;
      overflow: hidden;
      margin-bottom: 20rpx;
    }

    &-img {
      width: 100%;
      height: 342rpx;
    }

    &-title {
      font-weight: bold;
      font-size: 24rpx;
      color: #052031;
      padding: 10rpx 20rpx 0;
    }

    &-desc {
      font-weight: 500;
      font-size: 20rpx;
      color: #052031;
      opacity: .5;
      margin: 4rpx 0 10rpx;
      padding: 0 20rpx;
    }

    &-price {
      display: flex;
      font-weight: bold;
      font-size: 36rpx;
      color: #FF4F28;
      padding: 0 20rpx 20rpx;
      align-items: baseline;

      .unit {
        font-size: 24rpx;
      }
    }

  }

  .history {
    font-weight: bold;
    font-size: 32rpx;
    color: #052031;

    .del {
      width: 40rpx;
      height: 40rpx;
      margin-right: 8rpx;
    }

    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &-btn {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      font-weight: 500;
    }

    &-body {
      display: flex;
      flex-wrap: wrap;
      margin-top: 20rpx;
    }

    &-item {
      font-size: 24rpx;
      color: #052031;
      padding: 16rpx 36rpx;
      background-color: #ffffff;
      box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(54, 127, 137, 0.1);
      border-radius: 100rpx 100rpx 100rpx 100rpx;
      margin-right: 24rpx;
      margin-bottom: 24rpx;
    }
  }
}