const app = getApp();
const util = require("@/utils/util.js");
const { productListApi } = require("@/api/product");

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "搜索",
    },
    searchValue: "",
    showHistory: false,
    list: [
    ],
    originList: [
    ],

    historyList: [],
  },
  onLoad: function () {
    console.log("onLoad");
    this.getProductList();
  },
  search: function () {
    console.log("search");
    let that = this;
    var searchValue = that.data.searchValue;
    console.log("search",searchValue, searchValue.length);
    let historyList = that.data.historyList;
    // 根据搜索值，判断是否存在历史搜索，
    if (searchValue.length  >0 ) {
      let isExist = historyList.some((item) => item === that.data.searchValue);
      if (!isExist) {
        historyList.push(that.data.searchValue);
      }
      // 如果多于8个，则删除第一个
      if (historyList.length > 8) {
        historyList.shift();
      }
    }else{
      console.log("searchValue no search", searchValue);
      that.setData({
        list: that.data.originList,
        showHistory: false,
      });
      return;
    }


    // 根据名称过滤
    let list = that.data.originList.filter((item) => item.name.includes(searchValue));
    that.setData({
        historyList: historyList,
        list: list,
        showHistory: false,
    });
    
  },
  searchInput: function (e) {
    console.log(e);
    var searchValue = e.detail.value;
    if (searchValue.length === 0) {
      this.setData({
        searchValue: "",
        list: this.data.originList,
        showHistory: true,
      });
      return;
    }
    this.setData({
      searchValue: e.detail.value,
    });
  },
  clearHistory: function () {
    this.setData({
      historyList: [],
    });
    util.showSuccessToast("清除成功");
  },
  gotoGoodsDetail: function (e) {
    console.log(e);
    let id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/yinge/goods-detail/index?id=${id}`,
    });
  },
  getProductList: function () {
    let that = this;
    productListApi({
      page: 1,
      pageSize: 10,
    }).then((res) => {
      console.log(res);

       let list = res.data.list;
      // 价格转元，保留两位小数
      list.forEach((item) => {
        item.price = (item.price / 100).toFixed(2);
      });

       this.setData({
        list: list,
        originList: list,
       });


    });
  },
});
