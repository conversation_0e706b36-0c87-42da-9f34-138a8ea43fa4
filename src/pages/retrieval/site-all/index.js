const app = getApp();
const util = require("@/utils/util.js");
import { querySiteListApi, searchPositionApi } from "@/api/site";
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "酷拍侠",
    },
    bannerList: [
      {
        img: "https://k-cos.ai-market.top/wx/banner/banner1.png",
      },
      {
        img: "https://k-cos.ai-market.top/wx/banner/banner2.png",
      },
      {
        img: "https://k-cos.ai-market.top/wx/banner/banner3.png",
      },
    ],
    menus: [
      {
        id: 3,
        label: "景区",
      },
      {
        id: 6,
        label: "台球馆",
      },
      {
        id: 4,
        label: "运动馆",
      },
      {
        id: 5,
        label: "儿童乐园",
      },
    ],
    nearList: [],
    scenicList: [],
    // 运动场馆
    sport: [],
    // 台球馆
    table: [],
    // 儿童乐园
    amusement: [],
    activeId: 3,
  },

  handleMenu({
    currentTarget: {
      dataset: { id },
    },
  }) {
    this.setData({
      activeId: id,
    });
  },
  onLoad: function (options) {
    let that = this;
    console.log("query", options);
    that.data.query = options;
    that.getSiteList();
    that.handleGetLocation();
  },
  onShareAppMessage: function () {
    return {
      title: "酷拍侠 - 记录最酷的瞬间",
      imageUrl: app.globalData.share_config.imageUrl,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  gotoFeature: function (e) {
    let feature = e.currentTarget.dataset.feature;
    if (!feature) {
      util.showToast("敬请期待");
      return;
    }
    wx.navigateTo({
      url: e.currentTarget.dataset.feature,
    });
  },
  getSiteList: function () {
    let that = this;
    querySiteListApi({
      hot: 1,
      pageNo: 1,
      pageSize: 100,
    }).then((res) => {
      console.log("res", res);
      // 遍历res.data.list，将categoryId为3的站点添加到scenicList中
      res.data.list.forEach((item) => {
        item.live = false;
      });

      // 根据 categoryId 分别将数据赋值给 scenicList、sport、table、amusement
      that.setData({
        scenicList: res.data.list.filter((item) => item.categoryId == 3),
        sport: res.data.list.filter((item) => item.categoryId == 4),
        table: res.data.list.filter((item) => item.categoryId == 6),
        amusement: res.data.list.filter((item) => item.categoryId == 5),
      });
    });
  },
  gotoSearch: function () {
    wx.navigateTo({
      url: "/pages/retrieval/site-search/index",
    });
  },

  // 获取位置信息
  handleGetLocation() {
    const that = this;
    wx.showLoading({
      title: "搜索附近景区...",
    });
    wx.getLocation({
      type: "gcj02",
      success(ret) {
        that.setData({
          lng: ret.longitude,
          lat: ret.latitude,
          locationState: "OK",
        });
        console.log("ret", ret);

        searchPositionApi({
          lng: ret.longitude,
          lat: ret.latitude,
        }).then((res) => {
          wx.hideLoading();
          console.log("111res", res);

          res.data.forEach((item) => {
            item.live = false;
          });

          // 根据 categoryId 分别将数据赋值给 scenicList、sport、table、amusement
          that.setData({
            nearList: res.data,
          });
        });
      },
      fail: function (errInfo) {
        console.log(errInfo, "errInfo");
        wx.showModal({
          title: "提醒",
          content:
            "您拒绝了位置授权，将无法使用大部分功能，点击确定重新获取授权",
          success(ret) {
            //如果点击确定
            if (ret.confirm) {
              that.onAuthLocation();
            } else {
              util.showToast("您拒绝了位置授权");
              that.setLocationState;
            }
          },
        });
      },
    });
  },

  onAuthLocation() {
    const that = this;
    wx.openSetting({
      success(res) {
        //如果同意了位置授权则userLocation=true
        if (res.authSetting["scope.userLocation"]) {
          console.log(res, "同意授权");
          that.handleGetLocation();
        }
      },
      fail: () => {
        wx.$toast("您拒绝了位置授权");
        that.handleGetLocation();
      },
    });
  },
});
