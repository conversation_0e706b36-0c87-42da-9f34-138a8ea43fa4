<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="home" style="margin-top: {{ paddingHeight }}px">
    <view class="search">
    <image class="search-icon" src="/images/index/search.png" bind:tap="gotoSearch" />
    <input class="search-input" placeholder="搜索景区，目的地"  bindfocus="gotoSearch"/>
  </view>
    <view class="banner">
    <swiper
      indicator-dots
      autoplay
      indicator-active-color="#ffffff"
      indicator-color="rgba(255,255,255,.3)"
      circular
      class="banner-swiper"
    >
      <swiper-item wx:for="{{ bannerList }}" wx:key="id">
        <image class="banner-img" src="{{ item.img }}" />
      </swiper-item>
    </swiper>
  </view>




 <button class="video-btn" bind:tap="handleGetLocation" wx:if="{{ nearList.length == 0 }}">
    <view>请先授权位置信息</view>
  </button>

  <view class="title" wx:if="{{ nearList.length > 0 }}" >
    <image class="title-icon" src="/images/home/<USER>" />
    <view class="title-text">附近景区</view>
  </view>

  <com-scenic wx:if="{{ nearList.length > 0 }}" list="{{ nearList }}" />


  <view class="title">
    <image class="title-icon" src="/images/home/<USER>" />
    <view class="title-text">热门景区</view>
  </view>


  <view class="menu">
    <view
      class="menu-item {{ item.id === activeId ? 'active' : '' }}"
      wx:for="{{ menus }}"
      wx:key="id"
      data-id="{{ item.id }}"
      bind:tap="handleMenu"
    >
      {{ item.label }}
    </view>
  </view>

  <com-scenic wx:if="{{ activeId === 3 }}" list="{{ scenicList }}" />
  <com-scenic wx:elif="{{ activeId === 6 }}" list="{{ table }}" />
  <com-scenic wx:elif="{{ activeId === 4 }}" list="{{ sport }}" />
  <com-scenic wx:elif="{{ activeId === 5 }}" list="{{ amusement }}" />
</view>

<TabBar activeIndex="{{ -1 }}" />
