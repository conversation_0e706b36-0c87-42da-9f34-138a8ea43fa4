
.template-home {
  background: linear-gradient(180deg, #EBFBFC 0%, #FFFFFF 150px);
  padding-bottom: 100rpx;

  // 搜索栏样式
  .search-container {
    padding: 20rpx 24rpx;
    position: relative;
    z-index: 999;
    .search-box {
      display: flex;
      align-items: center;
      gap: 20rpx;
      position: relative;

      .search-input-wrapper {
      width: calc(100% - 160rpx);
        display: flex;
        align-items: center;
        background: #FFFFFF;
        border-radius: 100rpx;
        padding: 20rpx 32rpx;
        box-shadow: 0px 4rpx 20rpx 0px rgba(54, 127, 137, 0.1);

        .search-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
        }

        .search-input {
          flex: 1;
          font-size: 28rpx;
          color: #052031;

          &::placeholder {
            color: rgba(5, 32, 49, 0.5);
          }
        }

        .clear-btn {
          padding: 8rpx;
          position: absolute;
          right: 60rpx;
          top: 0;

          .clear-icon {
            width: 20rpx;
            height: 20rpx;
          }
        }
      }

      .voice-btn {
        width: 64rpx;
        height: 64rpx;
        background: linear-gradient(135deg, #2FD2F8 0%, #2FF8A8 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        right: 120rpx;
        top: 10rpx;

        .voice-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  // 分类标签栏样式
  .category-container {
    padding:16rpx 24rpx 20rpx;

    .category-scroll {
      white-space: nowrap;

      .category-list {
        display: flex;
        align-items: center;
        gap: 32rpx;
        .category-left{
          display: flex;
          flex: 1;
        }
        .category-item {
          padding: 12rpx 0;
          font-size: 32rpx;
          color: #052031;
          white-space: nowrap;
          position: relative;
          margin-right: 40rpx;
          &.active {
            font-weight: bold;
            position: relative;
            .name{
              z-index: 12;
              position: relative;
            }
          .line {
              position: absolute;
              bottom:15rpx;
              left: 0;
              z-index: 1;

              right: 0;
              height: 10rpx;
              background: linear-gradient(90deg, #00B2FF 0%, #2FD2F8 100%);
              border-radius: 5rpx;
            }
          }
        }

        .filter-btn {
          width: 100rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;
          font-size: 28rpx;
          color: #052031;
          margin-left: 40rpx;

          .filter-icon {
            width: 36rpx;
            height: 36rpx;
          }
        }
      }
    }
  }

  // 模板列表样式
  .template-container {
    padding: 0 24rpx;
    width: 100%;
    box-sizing: border-box;


    .template-grid {
      // 瀑布流布局 - 使用 CSS columns 实现
      column-count: 2;
      column-gap: 24rpx;
      padding: 20rpx 0;

      // 确保在不同屏幕尺寸下都显示两列
      @media screen and (max-width: 750rpx) {
        column-gap: 20rpx;
      }

      .template-card {
        background: #FFFFFF;
        border-radius: 32rpx;
        margin-bottom: 24rpx;
        overflow: hidden;
        box-shadow: 0px 4rpx 20rpx 0px rgba(54, 127, 137, 0.1);
        break-inside: avoid; // 防止卡片被分割到不同列
        display: inline-block; // 确保瀑布流效果
        width: 100%; // 占满列宽
        .template-cover {
          position: relative;


          .cover-image {
            width:100%;
            height: auto;
          // aspect-ratio: 1;
            display: block;
            border-radius: 32rpx 32rpx 0 0;
          }
        }

        .template-info {
          padding: 20rpx 20rpx 20rpx 20rpx;

          .template-title {
            font-size: 24rpx;
            color: #052031;
            font-weight: 400;
            margin-bottom: 10rpx;
            line-height: 1.4;
          }

          .template-stats {
            display: flex;
            align-items: center;
            gap:20rpx;

            .stat-item {
              display: flex;
              align-items: center;
              gap: 8rpx;

              .like-icon {
                width: 24rpx;
                height: 24rpx;

                &.liked {
                  opacity: 1;
                }
              }

              .stat-text {
                font-size:20rpx;
                color: rgba(5, 32, 49, 0.5);
              }
            }

            .share-btn {
              margin-left: auto;
              padding: 8rpx;

              .share-icon {
                width: 24rpx;
                height: 24rpx;
                opacity: 0.5;
              }
            }
          }
        }
      }
    }

    // 加载状态
    .loading-container {
      display: flex;
      justify-content: center;
      padding: 40rpx 0;

      .loading-text {
        font-size: 28rpx;
        color: rgba(5, 32, 49, 0.5);
      }
    }

    // 没有更多数据
    .no-more {
      display: flex;
      justify-content: center;
      padding: 40rpx 0;
      font-size: 28rpx;
      color: rgba(5, 32, 49, 0.5);
    }

    // 空状态
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 120rpx 0;

      .empty-icon {
        margin-bottom: 24rpx;
      }

      .empty-text {
        font-size: 28rpx;
        color: rgba(5, 32, 49, 0.5);
      }
    }
  }

  // 一键成片浮动按钮
  .quick-generate-btn {
    position: absolute;
    top: 194rpx;
    right: 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    z-index: 1000;

    .generate-icon {
      width: 48rpx;
      height: 48rpx;
    }

    .generate-text {
      font-size: 24rpx;
      color: #052031;
      font-weight: 400;
    }
  }

  // 底部指示器
  .bottom-indicator {
    position: fixed;
    bottom: 68rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 20rpx;

    .indicator-dot {
      width: 10rpx;
      height: 10rpx;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 50%;
    }

    .indicator-line {
      width: 32rpx;
      height: 10rpx;
      background: #000000;
      border-radius: 100rpx;

      &.active {
        background: #000000;
      }
    }
  }

  // 侧边栏
  .float-sidebar {
    position: fixed;
    right: 24rpx;
    bottom: 120rpx;
    z-index: 100;

    .sidebar-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;
      padding: 16rpx;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 16rpx;
      backdrop-filter: blur(10rpx);

      .sidebar-icon {
        width: 48rpx;
        height: 48rpx;
      }

      .sidebar-text {
        font-size: 20rpx;
        color: #052031;
      }
    }
  }
}


/* WXSS */
.waterfall-container {
  column-count: 2; /* 列数 */
  column-gap: 10rpx; /* 列间距 */
  padding: 10rpx;
}

.waterfall-item {
  break-inside: avoid; /* 防止元素被分割到不同列 */
  margin-bottom: 10rpx;
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.waterfall-item image {
  width: 100%;
  display: block;
}

.waterfall-item text {
  display: block;
  padding: 10rpx;
  font-size: 28rpx;
}
.mask{
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  height:calc(100% - 370rpx);
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 1000;
}
.filtrateList{
  background:#fbffff ;
  padding:24rpx 24rpx 30rpx;
  .title{
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
    padding-bottom: 16rpx;
  }
  .flex-container {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    width: 100%;
    box-sizing: border-box;
  }

  .flex-item,  .flex-DataItem {
    width: calc(20% -  20rpx); /* 每个元素占 20% 宽度（100% / 5 = 20%） */
    /* 或者使用 flex 属性 */
    /* flex: 0 0 20%; */

    /* 可选样式 */
    font-size: 26rpx;
 padding: 14rpx 0;
 margin: 0 20rpx 30rpx 0;
    background: #FFFFFF;
    box-shadow: 0px 4px 20px 0px rgba(54,127,137,0.1);
    border-radius: 100px 100px 100px 100px;
    display: flex;
    justify-content: center;
    align-items: center;

    box-sizing: border-box; /* 防止边框影响宽度计算 */
    &:last-child{
      margin-right: 0;
    }
  }
  .flex-item{
    &:nth-child(5n){
      margin-right: 0;
    }
  }
  .flex-DataItem{
    width: calc(25% - 20rpx);
    &:nth-child(4n){
      margin-right: 0;
    }
  }
  .active-btn{
    background: linear-gradient( 90deg, #2FD2F8 0%, #2FF8A8 100%);
  }

  .footer-btn{
    margin-top: 40rpx;
    margin-bottom: 30rpx;
    display: flex;
    .btn-list{
      font-size: 32rpx;
      text-align: center;
      padding: 24rpx 0;
      font-weight: bold;
      flex: 1;
      margin: 0 20rpx;
      border: 2rpx solid rgba(5,32,49,0.2);
      background: #fff;
      border-radius: 100rpx;
      &:first-child{
        margin-left: 14rpx;
      }
    }
    .confirm{
      background: linear-gradient( 90deg, #2FD2F8 0%, #2FF8A8 100%);
    }

  }
}
.ShowModalHeight{
  overflow: hidden;
  height: calc(100vh - 260rpx);
}
