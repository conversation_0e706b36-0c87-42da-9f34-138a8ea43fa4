const app = getApp();
const util = require("@/utils/util.js");
const { querySiteCommonTemplate } = require("@/api/retrieval.js");

Page({
  data: {
    navData: {
      title: "模板库",
    },
    isShowModal: false,
    paddingHeight: app.globalData.paddingHeight,

    // 搜索相关
    searchValue: "",
    showVoiceBtn: true,

    // 分类相关
    categories: [
      { id: 0, name: "全部", active: true },
      { id: 1, name: "运动", active: false },
      { id: 2, name: "景区", active: false },
      { id: 3, name: "娱乐", active: false },
      { id: 4, name: "亲子", active: false },
    ],

    // 模板列表
    templateList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,

    // 筛选相关
    showFilter: false,
    filterOptions: {
      materialCount: null, // 模板片段数筛选: 1, 2, [3,5], [6,10], [10,99]
      templateTime: null, // 模板时长筛选: [0,15], [15,30], [30,60], [60,999]
    },

    // 筛选选项配置
    materialCountOptions: [
      { label: "1", value: 1, range: [1, 1] },
      { label: "2", value: 2, range: [2, 2] },
      { label: "3-5", value: "3-5", range: [3, 5] },
      { label: "6-10", value: "6-10", range: [6, 10] },
      { label: "10+", value: "10+", range: [10, 99] },
    ],
    templateTimeOptions: [
      { label: "0-15秒", value: "0-15", range: [0, 15] },
      { label: "15-30秒", value: "15-30", range: [15, 30] },
      { label: "30-60秒", value: "30-60", range: [30, 60] },
      { label: "60秒+", value: "60+", range: [60, 999] },
    ],

    selectedMaterialCount: null,
    selectedTemplateTime: null,
  },

  onLoad(options) {
    this.loadTemplateList();
  },

  onShareAppMessage: function () {
    return {
      title: app.globalData.share_config.title,
      imageUrl: app.globalData.share_config.imageUrl,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },

  onShow() {
    // 页面显示时刷新数据
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreTemplates();
    }
  },

  onPullDownRefresh() {
    this.refreshTemplateList();
  },

  // 搜索防抖处理
  searchDebounceTimer: null,

  // 处理API返回的模板数据格式
  formatTemplateData(apiData) {
    return apiData.map((item) => ({
      id: item.id,
      title: item.title || item.name || "未命名模板",
      coverImage:
        item.templateCover || item.coverUrl || "/images/template/default.jpg",
      likeCount: item.likeCount || 0,
      useCount: item.useCount || 0,
      category: item.category || "其他",
      categoryId: item.categoryId || 0,
      isLiked: item.isLiked || false,
      templateId: item.templateId,
      materialCount: item.materialCount || 0, // 模板片段数
      templateTime: item.templateTime || 0, // 模板时长(秒)
    }));
  },
  showModal() {
    this.setData({ isShowModal: true });
  },

  hideModal() {
    this.setData({
      isShowModal: false,
      showFilter: false,
    });
  },
  // 加载模板列表
  loadTemplateList() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    // 构建API请求参数
    const requestData = {
      pageNo: this.data.page,
      pageSize: this.data.pageSize,
      // 默认筛选条件
      materialCount: [0, 99],
      templateTime: [0, 99],
    };

    // 添加用户选择的筛选条件（覆盖默认值）
    if (this.data.filterOptions.materialCount) {
      requestData.materialCount = this.data.filterOptions.materialCount;
    }
    if (this.data.filterOptions.templateTime) {
      requestData.templateTime = this.data.filterOptions.templateTime;
    }

    // 添加搜索关键词
    if (this.data.searchValue) {
      requestData.title = this.data.searchValue;
    }

    // 添加分类筛选
    const activeCategory = this.data.categories.find((cat) => cat.active);
    if (
      activeCategory &&
      activeCategory.name &&
      activeCategory.name !== "全部"
    ) {
      requestData.label = activeCategory.name;
    }

    querySiteCommonTemplate(requestData)
      .then((res) => {
        console.log("模板列表API响应:", res);

        if (res.data && res.data.list) {
          const formattedList = this.formatTemplateData(res.data.list);

          // 根据页码决定是追加还是替换数据
          const newTemplateList =
            this.data.page === 1
              ? formattedList
              : [...this.data.templateList, ...formattedList];

          this.setData({
            templateList: newTemplateList,
            loading: false,
            hasMore: formattedList.length === this.data.pageSize, // 如果返回数据等于页面大小，说明可能还有更多
          });
        } else {
          this.setData({
            templateList: this.data.page === 1 ? [] : this.data.templateList,
            loading: false,
            hasMore: false,
          });
        }

        wx.stopPullDownRefresh();
      })
      .catch((error) => {
        console.error("获取模板列表失败:", error);
        this.setData({
          loading: false,
          hasMore: false,
        });

        wx.showToast({
          title: "获取模板列表失败",
          icon: "none",
        });

        wx.stopPullDownRefresh();
      });
  },

  // 加载更多模板
  loadMoreTemplates() {
    // 模拟加载更多数据
    this.setData({
      page: this.data.page + 1,
    });
    this.loadTemplateList();
  },

  // 刷新模板列表
  refreshTemplateList() {
    this.setData({
      page: 1,
      hasMore: true,
      templateList: [],
    });
    this.loadTemplateList();
  },

  // 搜索输入
  onSearchInput(e) {
    const searchValue = e.detail.value;
    this.setData({
      searchValue: searchValue,
    });

    // 清除之前的定时器
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    // 设置防抖，500ms后执行搜索
    this.searchDebounceTimer = setTimeout(() => {
      this.refreshTemplateList();
    }, 500);
  },

  // 搜索确认
  onSearchConfirm() {
    // 清除防抖定时器，立即执行搜索
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }
    this.refreshTemplateList();
  },

  // 清空搜索
  onSearchClear() {
    this.setData({
      searchValue: "",
    });
    this.refreshTemplateList();
  },

  // 语音搜索
  onVoiceSearch() {
    wx.showToast({
      title: "语音搜索功能开发中",
      icon: "none",
    });
  },

  // 分类切换
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id;
    const categories = this.data.categories.map((cat) => ({
      ...cat,
      active: cat.id === categoryId,
    }));

    this.setData({ categories });
    this.refreshTemplateList();
  },

  // 显示筛选
  onFilterTap() {
    this.setData({
      showFilter: true,
    });
  },

  // 选择模板片段数
  onMaterialCountTap(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      selectedMaterialCount: value,
    });
  },

  // 选择模板时长
  onTemplateTimeTap(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      selectedTemplateTime: value,
    });
  },

  // 重置筛选条件
  onFilterReset() {
    this.setData({
      selectedMaterialCount: null,
      selectedTemplateTime: null,
      filterOptions: {
        materialCount: null,
        templateTime: null,
      },
    });
  },

  // 确认筛选
  onFilterConfirm() {
    const {
      selectedMaterialCount,
      selectedTemplateTime,
      materialCountOptions,
      templateTimeOptions,
    } = this.data;

    // 构建筛选条件
    const filterOptions = {
      materialCount: null,
      templateTime: null,
    };

    // 设置模板片段数筛选条件
    if (selectedMaterialCount) {
      const option = materialCountOptions.find(
        (item) => item.value === selectedMaterialCount,
      );
      if (option) {
        filterOptions.materialCount = option.range;
      }
    }

    // 设置模板时长筛选条件
    if (selectedTemplateTime) {
      const option = templateTimeOptions.find(
        (item) => item.value === selectedTemplateTime,
      );
      if (option) {
        filterOptions.templateTime = option.range;
      }
    }

    this.setData({
      filterOptions: filterOptions,
      showFilter: false,
    });

    // 重新加载数据
    this.refreshTemplateList();
  },

  // 模板卡片点击
  onTemplateTap(e) {
    const templateId = e.currentTarget.dataset.id;
    console.log("templateId", templateId);
    wx.navigateTo({
      url: `/pages/retrieval/generate-video/index?templateId=${templateId}`,
    });
  },

  // 点赞
  onLikeTap(e) {
    const templateId = e.currentTarget.dataset.id;
    const templateList = this.data.templateList.map((item) => {
      if (item.id === templateId) {
        return {
          ...item,
          isLiked: !item.isLiked,
          likeCount: item.isLiked ? item.likeCount - 1 : item.likeCount + 1,
        };
      }
      return item;
    });

    this.setData({ templateList });

    wx.showToast({
      title: templateList.find((item) => item.id === templateId).isLiked
        ? "已点赞"
        : "已取消点赞",
      icon: "none",
    });
  },

  // 分享
  onShareTap(e) {
    const templateId = e.currentTarget.dataset.id;
    wx.showToast({
      title: "分享功能开发中",
      icon: "none",
    });
  },

  // 一键成片
  onQuickGenerate() {
    wx.showToast({
      title: "功能开发中",
      icon: "none",
    });
  },

  // 返回
  goBack() {
    wx.navigateBack();
  },

  // 回到首页
  backToHome() {
    wx.navigateTo({
      url: "/pages/index/index",
    });
  },
});
