<PageHeader />
<NavBar style="z-index: 999" nav-data="{{ navData }}" id="navbar" />


<view class="template-home {{isShowModal?'ShowModalHeight':''}}" style="margin-top: {{ paddingHeight }}px; ">
  <!-- 搜索栏 -->

  <view class="search-container">
    <view class="search-box">
      <view class="search-input-wrapper">
        <image class="search-icon" src="/images/icons/search.png" />
        <input
          class="search-input"
          placeholder="日常生活小碎片"
          value="{{ searchValue }}"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
          confirm-type="search"
        />
        <view wx:if="{{ searchValue }}" class="clear-btn" bindtap="onSearchClear">
          <image class="clear-icon" src="/images/icons/close.png" />
        </view>
      </view>
      <view class="voice-btn" bindtap="onVoiceSearch">
        <image class="voice-icon" src="/images/icons/voice.png" />
      </view>
    </view>
  </view>

  <!-- 分类标签栏 -->
  <view class="category-container">
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
      <view class="category-left">
        <view
          wx:for="{{ categories }}"
          wx:key="id"
          class="category-item {{ item.active ? 'active' : '' }}"
          bindtap="onCategoryTap"
          data-id="{{ item.id }}"
        >
      <view class="name">    {{ item.name }}</view>
          <view class="line"></view>
        </view>
      </view>
        <view class="filter-btn" bindtap="onFilterTap">
          <image class="filter-icon" src="/images/icons/filter.png" />
          <text>筛选</text>
        </view>
      </view>
    </scroll-view>
  </view>

<!-- WXML -->


  <!-- 模板列表 -->
  <view class="template-container">
  <!-- 筛选弹层 -->
  <view class="mask" wx:if="{{showFilter}}" bindtap="hideModal">
    <view class="filtrateList" catchtap="true">
      <view class="title">模板片段数</view>
      <view class="flex-container">
        <view
          wx:for="{{materialCountOptions}}"
          wx:key="value"
          class="flex-item {{selectedMaterialCount === item.value ? 'active-btn' : ''}}"
          bindtap="onMaterialCountTap"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>

      <view class="title">模板时长</view>
      <view class="flex-container">
        <view
          wx:for="{{templateTimeOptions}}"
          wx:key="value"
          class="flex-DataItem {{selectedTemplateTime === item.value ? 'active-btn' : ''}}"
          bindtap="onTemplateTimeTap"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>

      <view class="footer-btn">
        <view class="btn-list" bindtap="onFilterReset">重置</view>
        <view class="btn-list confirm" bindtap="onFilterConfirm">确定</view>
      </view>
    </view>
  </view>
  <!-- 筛选弹层结束 -->
    <view class="template-grid">
      <block wx:for="{{templateList}}" wx:key="id">
      <view
        class="template-card"
        bindtap="onTemplateTap"
        data-id="{{ item.templateId }}"
      >
        <!-- 模板预览图 -->
        <view class="template-cover">
          <image class="cover-image" src="{{ item.coverImage }}" mode="widthFix"  />
        </view>

        <!-- 模板信息 -->
        <view class="template-info">
          <view class="template-title">{{ item.title }}</view>
          <view class="template-stats">
            <view class="stat-item">
              <image
                class="like-icon {{ item.isLiked ? 'liked' : '' }}"
                src="/images/icons/{{ item.isLiked ? 'heart-red' : 'heart-grey' }}.png"
                bindtap="onLikeTap"
                data-id="{{ item.id }}"
                catchtap="true"
              />
              <text class="stat-text">{{ item.likeCount }}</text>
            </view>
            <view class="stat-item">
              <image
                class="like-icon {{ item.isLiked ? 'liked' : '' }}"
                src="/images/icons/{{ item.isLiked ? 'like-red' : 'like' }}.png"
                bindtap="onLikeTap"
                data-id="{{ item.id }}"
                catchtap="true"
              />
              <text class="stat-text">{{ item.useCount }}</text>
            </view>
            <!-- <view class="share-btn" bindtap="onShareTap" data-id="{{ item.id }}" catchtap="true">
              <image class="share-icon" src="/images/icons/share.png" />
            </view> -->
          </view>
        </view>
      </view>
      </block>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{ loading }}" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{ !hasMore && templateList.length > 0 }}" class="no-more">
      <text>没有更多模板了</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{ !loading && templateList.length === 0 }}" class="empty-state">
      <image class="empty-icon" style="text-align: center;" src="https://k-cos.ai-market.top/wx/base/video/empty.png" />
      <text class="empty-text">暂无相关模板</text>
    </view>
  </view>

  <!-- 一键成片浮动按钮 -->
  <view class="quick-generate-btn" bindtap="onQuickGenerate">
    <image class="generate-icon" src="/images/icons/video.png" />
    <text class="generate-text">一键成片</text>
  </view>

  <!-- 底部指示器 -->
  <view class="bottom-indicator">
    <view class="indicator-dot"></view>
    <view class="indicator-dot"></view>
    <view class="indicator-line active"></view>
  </view>


</view>
