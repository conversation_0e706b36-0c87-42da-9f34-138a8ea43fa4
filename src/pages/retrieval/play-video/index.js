const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight + 10,
    statusHeight: app.globalData.statusHeight,
    navData: {
      title: "视频",
    },
    isChecked: false,
    show: false,
    videoUrl: '',
    videList: [
      {
        id: 1,
        videoUrl: 'https://k-cos.ai-market.top/example_video/%E4%B8%89%E4%BA%9A%E6%B0%B4%E6%BB%91%E9%81%93.mp4',
      },
      {
        id: 2,
        videoUrl: 'https://k-cos.ai-market.top/example_video/%E4%B8%9C%E6%B5%92%E5%AF%A8.mp4',
      },
      {
        id: 3,
        videoUrl: 'https://k-cos.ai-market.top/example_video/%E4%B9%9D%E5%8F%A4%E5%A5%87%E6%9D%91.mp4',
      },
      {
        id: 4,
        videoUrl: 'https://k-cos.ai-market.top/example_video/%E5%91%A8%E8%87%B3%E6%B0%B4%E8%A1%97.mp4',
      }, 
      {
        id: 5,
        videoUrl: 'https://k-cos.ai-market.top/example_video/%E5%A4%9C%E9%83%8E%E8%A5%BF.mp4',
      }, 
      {
        id: 6,
        videoUrl: 'https://k-cos.ai-market.top/example_video/%E6%A1%90%E5%90%9B%E5%B1%B1.mp4',
      },
      {
        id: 7,
        videoUrl: 'https://k-cos.ai-market.top/example_video/%E6%B3%B8%E5%B1%B1%E6%A3%AE%E6%9E%97%E5%85%AC%E5%9B%AD.mp4',
      }, 
      {
        id: 8,
        videoUrl: 'https://k-cos.ai-market.top/example_video/%E7%A2%A7%E5%B3%B0%E5%B3%A1.mp4',
      }, 
    ]
  },

  onLoad: function (options) {
    console.log("options", options);

    let url = options.url;
    let img = options.img;
    if(url){
      this.setData({
        videoUrl: url,
        img: img,
      });
      return
    }

    let id = options.id;

    let videoUrl = this.data.videList.find(item => item.id == id).videoUrl;
    if (!videoUrl) {
      videoUrl = this.data.videList.find(item => item.id == 1).videoUrl;
    }
    this.setData({
      id: options.id,
      videoUrl: videoUrl,
      img: img,
    });
  },

  onShareAppMessage: function () {
    return {
      title: "酷拍侠 - 记录最酷的瞬间",
      imageUrl: "https://k-cos.ai-market.top/wx/base/logo-launch.png",
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },


});
