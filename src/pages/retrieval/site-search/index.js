const app = getApp();
const util = require("@/utils/util.js");
import { querySiteListApi } from "@/api/site";
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "搜索景区",
    },
    searchValue: "",
    menus: [
      {
        id: 3,
        label: "景区",
      },
      {
        id: 6,
        label: "台球馆",
      },
      {
        id: 4,
        label: "运动馆",
      },
      {
        id: 5,
        label: "儿童乐园",
      },
    ],

    scenicList: [],
    // 运动场馆
    sport: [],
    // 台球馆
    table: [],
    // 儿童乐园
    amusement: [],
    activeId: 3,
  },

  handleMenu({
    currentTarget: {
      dataset: { id },
    },
  }) {
    this.setData({
      activeId: id,
    });
  },
  onLoad: function (options) {
    let that = this;
    console.log("query", options);
    that.data.query = options;
    that.getSiteList();
  },
  onShareAppMessage: function () {
    return {
      title: "酷拍侠 - 记录最酷的瞬间",
      imageUrl: app.globalData.share_config.imageUrl,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  getSiteList: function () {
    let that = this;
    var siteName = that.data.searchValue;
    querySiteListApi({
      siteName: siteName,
      pageNo: 1,
      pageSize: 100,
    }).then((res) => {
      console.log("res", res);
      // 遍历res.data.list，将categoryId为3的站点添加到scenicList中
      res.data.list.forEach((item) => {
        item.live = false;
      });

      // 根据 categoryId 分别将数据赋值给 scenicList、sport、table、amusement
      that.setData({
        scenicList: res.data.list.filter((item) => item.categoryId == 3),
        sport: res.data.list.filter((item) => item.categoryId == 4),
        table: res.data.list.filter((item) => item.categoryId == 6),
        amusement: res.data.list.filter((item) => item.categoryId == 5),
      });
    });
  },
  search: function (e) {
    console.log("search", e);
    let that = this;
    that.getSiteList();
  },

  searchInput: function (e) {
    console.log(e);
    var searchValue = e.detail.value;
    this.setData({
      searchValue: e.detail.value,
    });
  },
});
