<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="home" style="margin-top: {{ paddingHeight }}px">
    <view class="search">
    <image class="search-icon" src="/images/index/search.png"  />
    <input class="search-input" placeholder="搜索景区，目的地" value="{{ searchValue }}" bind:input="searchInput" />
      <image class="search-btn" src="/images/search/icon2.png" bind:tap="search" />
  </view>


  <com-scenic wx:if="{{ activeId === 3 }}" list="{{ scenicList }}" />
  <com-scenic wx:elif="{{ activeId === 6 }}" list="{{ table }}" />
  <com-scenic wx:elif="{{ activeId === 4 }}" list="{{ sport }}" />
  <com-scenic wx:elif="{{ activeId === 5 }}" list="{{ amusement }}" />
</view>

<TabBar activeIndex="{{ -1 }}" />
