<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result" style="margin-top: {{ paddingHeight }}px">


 <view  wx:for="{{ defaultList }}" wx:key="id">

  <view class="position">
    <image class="icon" src="/images/video/position.png" />
    <view>{{ item.address }}</view>
  </view>
  <view class="gen"  data-img="{{ item.mixVideoCoverUrl }}" data-id="{{ item.id }}" data-task-status="{{ item.taskStatus }}" data-url="{{ item.mixVideoUrl }}">
    <image
      wx:if="{{  item.taskStatus ==2 }}"
      class="gen-bg"
      src="{{ item.mixVideoCoverUrl }}"
    />
    <image wx:else class="gen-bg" src="https://k-cos.ai-market.top/wx/base/camera/gen.png" />
    <view class="process">
      <view class="process-bg" style="width: {{ rate }}%"></view>
      <view class="text"  wx:if="{{ item.taskStatus ==2 }}" bind:tap="downloadVideo">视频合成完毕，点击查看</view>
       <view class="text" style="color:red" wx:if="{{ item.taskStatus ==-1 }}" bind:tap="navigateToGenerateVideo">
         视频合成失败，体验自定义合成
         <image class="arrow-icon" src="/images/video/video-arrow-right.png" />
       </view>
      <view class="text"  wx:if="{{ item.taskStatus !=-1 && item.taskStatus !=2 }}">视频合成{{ rate }}%，请稍候...</view>
    </view>
  </view>
  </view>

  <view class="menu">
    <view
      class="menu-item {{ item.id === activeId ? 'active' : '' }}"
      wx:for="{{ menus }}"
      wx:key="id"
      data-id="{{ item.id }}"
      bind:tap="handleMenu" >
      {{ item.label }}
    </view>
  </view>

  <view class="list" wx:if="{{ activeId == 1 }}">
    <view class="list-item" wx:for="{{ templateList }}" wx:key="id">
      <view class="list-item__left" bind:tap="playVideo" data-img="{{ item.coverUrl }}" data-id="{{ item.mixTaskId }}" data-task-status="{{ item.mixTaskState }}" data-url="{{ item.demoVideo }}">
        <image class="img" src="{{ item.coverUrl }}" />
        <image class="icon" src="/images/camera/play.png"></image>
      </view>
      <view class="list-item__center">
        <view class="list-item__content">
          <view class="list-item__title">{{ item.templateNick }}</view>
          <view class="hot">
            <image class="icon" src="/images/camera/hot.png"></image>
            <view>热度值{{ item.hot }}</view>
          </view>
          <view class="supper">
            <image class="icon" src="/images/camera/supper.png"></image>
            <view>支持{{ item.materialTemplateConfig.length }}段抓拍视频</view>
          </view>
        </view>
        <view wx:if="{{ item.mixTaskState == -100 }}" class="list-item__right" bind:tap="mergeVideo"  data-id="{{ item.id }}" data-task-status="{{ item.mixTaskState }}"  >
          <image class="icon" src="/images/camera/vedio.png"></image>
          <view class="right-title">合成同款</view>
        </view>
        <view wx:elif="{{ item.mixTaskState === 1 }}" class="list-item__right opacity">
          <image class="icon" src="/images/camera/vedio.png"></image>
          <view class="right-title">合成中...</view>
        </view>
        <view wx:if="{{ item.mixTaskState == 2 }}" class="list-item__right complete" bind:tap="downloadVideo"  data-id="{{ item.mixTaskId }}" data-task-status="{{ item.mixTaskState }}"  >
          <image class="icon" src="/images/camera/download.png"></image>
          <view class="right-title">立即下载</view>
        </view>
        <view wx:if="{{ item.mixTaskState == -1 }}" class="list-item__right complete">
          <image class="icon" src="/images/camera/vedio.png"></image>
          <view class="right-title">合成失败</view>
        </view>


      </view>
    </view>
  </view>

  <view  wx:if="{{ activeId == 2 }}">
    <view class="result-item" wx:for="{{ videoList }}" wx:key="id" bind:tap="downloadSplitVideo"  data-id="{{ item.id }}">
      <view class="result-card video" >
        <video
          class="video-player"
          id="myVideo"
          src="{{ item.oriVideoUrl }}"
          controls
          show-center-play-btn
          show-play-btn
          show-fullscreen-btn
          picture-in-picture-mode="push"
          bindplay="onPlay"
          binderror="onError"
          bindended="onEnded"
        />

      </view>
    </view>


  <block wx:if="{{ videoList.length == 0 }}">
    <view>
      <image class="empty" src="https://k-cos.ai-market.top/wx/base/video/empty.png" />
      <view class="desc">正在搜索视频，请稍候...</view>
    </view>
  </block>
  </view>



</view>
<TabBar activeIndex="{{ -1 }}" />
