page {
  background-color: #EBFBFC;
}

.result {
  padding: 40rpx 24rpx 120rpx;

  .position {
    display: flex;
    align-items: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #052031;
    margin-bottom: 24rpx;

    .icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 4rpx;
    }
  }

  .gen {
    position: relative;
    width: 100%;
    height: 396rpx;
    border-radius: 32rpx;
    overflow: hidden;
    margin-bottom: 40rpx;

    &-bg {
      width: 100%;
      height: 100%;
    }

    .process {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 70rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #052031;
      background-color: rgba(255, 255, 255, .5);

      .text {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        cursor: pointer;

        .arrow-icon {
          width: 60rpx;
          height: 60rpx;
          margin-left: 8rpx;
        }
      }

      &-bg {
        position: absolute;
        left: 0;
        bottom: 0;
        height: 100%;
        transition: width 0.3s linear;
        background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
      }
    }
  }

  .list {
    margin-top: 26rpx;

    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 200rpx;
      background: #FFFFFF;
      box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(54, 127, 137, 0.1);
      border-radius: 32rpx;
      margin-bottom: 24rpx;
      overflow: hidden;

      &__left {
        position: relative;
        flex: 0 1 200rpx;
        height: 200rpx;
        width: 200rpx;
        border-radius: 32rpx;
        overflow: hidden;

        .img {
          width: 200rpx;
          height: 200rpx;
        }

        .icon {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          right: 0;
          width: 59rpx;
          height: 59rpx;
          margin: auto;
        }
      }

      &__center {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #052031;
        padding: 22rpx 24rpx 26rpx;

        .hot,
        .supper {
          display: flex;
          align-items: center;
          font-weight: bold;
          font-size: 20rpx;
          color: #FFFFFF;
          border-radius: 100rpx;
          padding: 4rpx 14rpx 4rpx 10rpx;
          background: #2FD2F8;
          flex: 1;

          .icon {
            width: 24rpx;
            height: 24rpx;
            margin-right: 4rpx;
          }
        }

        .hot {
          margin: 14rpx 0 16rpx;
          background: #FF7200;
        }
      }

      &__content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
      }

      &__right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 22rpx;
        color: #052031;
        width: 144rpx;
        height: 144rpx;
        border-radius: 144rpx;
        background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);

        .icon {
          width: 35rpx;
          height: 35rpx;
          margin-bottom: 10rpx;
        }

        &.opacity {
          opacity: 0.5;
        }

        &.complete {
          color: #ffffff;
          background: linear-gradient(180deg, #2FD2F8 0%, #009FC5 100%);
        }
      }
    }
  }

  .menu {
    font-size: 32rpx;
    color: #052031;
    display: flex;
    align-items: baseline;
    padding: 0 16rpx;
    margin-bottom: 26rpx;

    &-item {
      margin-right: 80rpx;

      &.active {
        position: relative;
        font-weight: bold;
        font-size: 36rpx;
        color: #052031;

        &::before {
          position: absolute;
          z-index: -1;
          left: 0;
          bottom: 8rpx;
          content: '';
          width: 72rpx;
          height: 10rpx;
          background: linear-gradient(180deg, #00B2FF 0%, #2FD2F8 100%);
          border-radius: 20rpx 20rpx 20rpx 20rpx;
        }
      }
    }
  }

  &-card {
    position: relative;
    width: 702rpx;
    height: 396rpx;
    border-radius: 32rpx 32rpx 32rpx 32rpx;
    overflow: hidden;

    &__bg {
      width: 100%;
      height: 100%;
    }

    &__similar {
      position: absolute;
      right: 24rpx;
      top: 24rpx;
      text-align: center;

      .similar-text {
        background: #052031;
        border-radius: 40rpx 40rpx 40rpx 40rpx;
        opacity: 0.5;
        padding: 4rpx 14rpx;
        font-size: 24rpx;
        color: #ffffff;
      }

      .result-avatar {
        width: 96rpx;
        height: 96rpx;
        border: 4rpx solid #fff;
        border-radius: 96rpx;
      }
    }

    &__footer {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 70rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #052031;
      background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
      border-radius: 0rpx 0rpx 32rpx 32rpx;
      opacity: 0.9;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  &-item {
    margin-bottom: 40rpx;

    &__position {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
    }
  }
  .empty {
    display: block;
    width: 332rpx;
    height: 266rpx;
    margin: 100rpx auto 0;
  }
  .desc {
    font-size: 26rpx;
    color: #052031;
    line-height: 52rpx;
    padding: 0 90rpx;
    text-align: center;
  }

}


.video {
  &-player {
    width: 100%;
    height: 100%;
  }

}

