const app = getApp();
const util = require("@/utils/util.js");

import { getOneMixVideoTaskApi } from "@/api/retrieval";
import { querySitTemplateListApi } from "@/api/site";
import { retrievalApi, querySpotSliceVideo } from "@/api/retrieval";
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    statusHeight: app.globalData.statusHeight,
    navData: {
      title: "搜索结果",
    },
    activeId: 1,
    rate: 0,
    isSuccess: false,
    menus: [
      { id: 1, label: "模板" },
      { id: 2, label: "视频" },
      //{ id: 3, label: "图片" },
    ],
    defaultList: [],
    templateList: [
      // status 1 默认  2生成中  3成功
    ],
    videoList: [],
    //合成秒数
    mix_sccond: 60,
    id: 0,
    dataInit: false,
    timeOutId: null,
    state: [
      {
        id: 100,
        label: "视频检索中(预计还需50秒)",
        waitSecond: 50,
        color: "#000000",
      },
      {
        id: 0,
        label: "等待合成",
        waitSecond: 35,
        color: "#000000",
      },
      {
        id: 1,
        label: "合成中(预计还需30秒)",
        waitSecond: 30,
        color: "#000000",
      },
      {
        id: 2,
        label: "点击观看",
        color: "#000000",
      },
      {
        id: -1,
        label: "合成失败",
        color: "#FF4500",
      },
    ],
    process_state_list: [100, 0, 1],
    end_state_list: [2, -1],
  },
  onLoad: function (options) {
    console.log("options=", options);
    let id = options.id;
    this.setData({ id: id });
    if (id == 0 || id == null) {
      this.setData({ isEmpty: true });
    } else {
      this.getOneMixVideoTask();

      let timeOutId = setInterval(() => {
        this.getOneMixVideoTask();
      }, 3000);
      this.setData({ timeOutId: timeOutId });
    }
  },
  onUnload() {
    console.log("onUnload");
    if (this.data.timeOutId) {
      clearInterval(this.data.timeOutId);
    }
  },
  init() {
    let that = this;
    setTimeout(() => {
      let rate = this.data.rate + 1;
      let status = this.data.defaultList[0].taskStatus;
      console.log("rate=", rate, status);
      if (rate >= 100 && status != 2) {
        rate = 99;
      }
      if (status == 2) {
        rate = 100;
        this.setData({
          rate: 100,
        });
        return;
      }

      this.setData(
        {
          rate: rate,
        },
        () => {
          if (rate < 100) {
            this.init();
          } else {
            this.setData({
              rat: 100,
            });
          }
        },
      );
    }, 900);
  },

  handleMenu({
    currentTarget: {
      dataset: { id },
    },
  }) {
    this.setData({
      activeId: id,
    });
  },
  getOneMixVideoTask() {
    let that = this;
    let id = this.data.id;
    getOneMixVideoTaskApi({ id: id }).then((res) => {
      wx.hideLoading();
      console.log("res=", res);
      let item = res.data;
      let process_state = this.data.process_state_list.includes(
        res.data.taskStatus,
      );
      console.log("process_state=", process_state);
      item.process = false;
      if (process_state) {
        item.process = true;
      }
      let state = this.data.state.find(
        (item) => item.id == res.data.taskStatus,
      );
      item.stateLabel = state.label;
      let list = [];
      list.push(item);
      this.setData({
        defaultList: list,
      });

      if (res.data.taskStatus == 2) {
        this.setData({
          rat: 100,
        });
      }
      if (!this.data.dataInit) {
        this.setData({
          dataInit: true,
        });
        if (item.process) {
          // 初始化 rate ; 计算逻辑是这样的：根据 当时时间减去 开始时间 然后除以 60 秒 然后乘以 100
          let now = new Date().getTime();
          let start = new Date(res.data.createTime).getTime();
          let rate = ((now - start) / 1000 / this.data.mix_sccond) * 100;
          // 转int
          rate = parseInt(rate);
          if (rate >= 100) {
            rate = 99;
          }
          console.log("rate=", rate);

          this.setData({
            rate: rate,
            defaultList: list,
          });
          that.init();
        }
      }
      that.querySitTemplateList(item.siteCode, item.siteId);
      if (that.data.videoList.length == 0) {
        this.querySpotSliceVideo(item.retrievalTaskId);
      }
    });
  },
  downloadVideo(e) {
    let id = e.currentTarget.dataset.id;
    let taskStatus = e.currentTarget.dataset.taskStatus;
    if (taskStatus !== 2) {
      console.log("未合成，taskStatus=", taskStatus);
      wx.showToast({
        title: "视频正在合成中",
        icon: "none",
      });
      return;
    } else {
      let url = e.currentTarget.dataset.url;
      let img = e.currentTarget.dataset.img;
      console.log("url=", url, img, id);
      wx.navigateTo({
        url: "/pages/retrieval/play/index?type=0&id=" + id,
      });
    }
  },

  downloadSplitVideo(e) {
    let id = e.currentTarget.dataset.id;
    console.log("downloadSplitVideo=", id);
    wx.navigateTo({
      url: "/pages/retrieval/play/index?type=1&id=" + id,
    });
  },

  querySitTemplateList(siteCode, siteId) {
    console.log("querySitTemplateList", this.data.defaultList.length);
    querySitTemplateListApi({
      siteId: siteId,
      siteCode: siteCode,
    }).then((res) => {
      console.log("res=", res);
      // 过滤 prime的模板
      let list = res.data.filter((item) => item.prime == 0);

      // 根据模板id 查询模板详情
      this.setData({
        templateList: list,
      });
    });
  },
  playVideo(e) {
    let id = e.currentTarget.dataset.id;
    let taskStatus = e.currentTarget.dataset.taskStatus;
    let url = e.currentTarget.dataset.url;
    let img = e.currentTarget.dataset.img;

    if (taskStatus == 2) {
      wx.navigateTo({
        url: "/pages/retrieval/play/index?id=" + id + "&type=0",
      });
      return;
    }

    wx.navigateTo({
      url:
        "/pages/retrievalplay-video/index?url=" +
        url +
        "&img=" +
        img +
        "&id=" +
        id,
    });
  },
  doRetrieval(templateId) {
    let that = this;
    console.log("doRetrieval", templateId);
    let preMixTask = this.data.defaultList[0];

    var postData = {
      faceSource: 1,
      siteTemplateId: templateId,
      siteId: preMixTask.siteId,
      siteCode: preMixTask.siteCode,
      faceImageUrl: preMixTask.faceImageUrl,
    };
    wx.showLoading({
      title: "视频检索中",
    });
    console.log("doRetrieval json", JSON.stringify(postData));
    retrievalApi(postData)
      .then(({ data }) => {
        wx.hideLoading();
        console.log("data=", data);
        if (data == 0) {
          util.showErrorToast("检索失败");
        } else {
          console.log("检索成功");
          that.querySitTemplateList(preMixTask.siteCode, 0);
        }
      })
      .catch((error) => {
        util.showErrorToast("检索失败");
        setTimeout(() => {
          this.setData({
            isScan: false,
            isScanError: true,
          });
        }, 3000);
      });
  },
  mergeVideo(e) {
    let id = e.currentTarget.dataset.id;
    this.doRetrieval(id);
  },
  querySpotSliceVideo(taskId) {
    querySpotSliceVideo({
      retrievalTaskId: this.data.defaultList[0].retrievalTaskId,
    }).then((res) => {
      console.log("querySpotSliceVideo=", res);
      let list = res.data.list;
      this.setData({
        videoList: list,
      });
    });
  },

  navigateToGenerateVideo() {
    console.log("navigateToGenerateVideo");
    const { defaultList, templateList } = this.data;

    if (defaultList.length === 0) {
      wx.showToast({
        title: "数据加载中，请稍后",
        icon: "none",
      });
      return;
    }

    const siteId = defaultList[0].siteId;

    if (templateList.length === 0) {
      wx.showToast({
        title: "模板加载中，请稍后",
        icon: "none",
      });
      return;
    }

    // 获取第一个可用模板的ID
    const templateId = templateList[0].id;

    console.log("导航参数:", { templateId, siteId });

    wx.navigateTo({
      url: `/pages/retrieval/generate-video/index?templateId=${templateId}&siteId=${siteId}`,
    });
  },
});
