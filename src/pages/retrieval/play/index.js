var util = require("@/utils/util.js");
import { getOneMixVideoTaskApi,getOneSpotSliceVideoApi } from "@/api/retrieval";
import { createOrderApi,queryOrderApi,commonCreateOrderApi } from "@/api/order";
import { payOrderApi } from "@/api/pay";
import { queryOneSplitApi } from "@/api/live";
const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight + 10,
    statusHeight: app.globalData.statusHeight,
    navData: {
      title: "视频",
    },
    isChecked: false,
    show: false,
    videoUrl: '',
    shareUrl: '',
    id: 0,
    // 0 合成视频 1 点位切片视频,2 直播切片
    type: 0,
    payStatus:0,
    mixVideoTask: {},
    prePayInfo: {},
  },

  onLoad: function (options) {
    console.log("options", options);
    let id = options.id;
    let type = options.type;
    if(type == null || type == undefined){
      type = 0;
    }
    let shareUrl = "/pages/play/index?pCode=" + app.globalData.user.userCode+"&id="+id+"&type="+type;
    this.setData({
      id: id,
      shareUrl: shareUrl,
      type: type,
    });
    this.queryOrder();

    if(type == "0"){
      this.getOneMixVideoTask();
    }
    if(type == "1"){
      this.getOneSpotSliceVideo();
    }
    if(type == "2"){
      this.getOneLiveSplitVideo();
    }


  },

  onShareAppMessage: function () {
    let shareUrl = this.data.shareUrl;
    return {
      title: "酷拍侠 - 记录最酷的瞬间",
      imageUrl: "https://k-cos.ai-market.top/wx/base/logo-launch.png",
      path: shareUrl
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: shareUrl
    };
  },
  downloadVideo: function(){
    console.log("下载视频");
    if(app.globalData.userInfo.userId == 0 || app.globalData.userInfo.userId == null){
      // 登陆
      wx.navigateTo({
        url: "/pages/login/index",
      });
      return
    }

    if(this.data.payStatus == 1){
       console.log("已经支付，直接下载");
       this.saveVideo();
      return
    }
    //创建订单，开始支付

    let that = this;
    wx.showLoading({
      title: '创建订单中...',
    });
    commonCreateOrderApi({
      bizId: this.data.id,
      bizType: this.data.type,
    }).then((res) => {
      wx.hideLoading();
      console.log("res=", res);
      that.payOrder(res.data.payOrderId);
    });

  },
  // 支付
  payOrder(payOrderId) {
    console.log("payOrderId=", payOrderId,app.globalData.userInfo.openid);
    let that = this;
    wx.showLoading({
      title: '获取支付信息...',
    });
    payOrderApi({
      id: payOrderId,
      channelCode:"wx_lite",
      channelExtras:{"openid":app.globalData.userInfo.openId}
    }).then((res) => {
      wx.hideLoading();
      console.log("res=", res);
      that.setData({
        prePayInfo: res.data,
      });
      that.callWxPay();
    });
    
  },

  getOneMixVideoTask() {
    let id = this.data.id;
    getOneMixVideoTaskApi({ id: id }).then((res) => {
      wx.hideLoading();
      console.log("res=", res);
      let mixVideoTask = res.data;
      let url = mixVideoTask.mixVideoUrl;
      let img = mixVideoTask.mixVideoCoverUrl;
      this.setData({
        mixVideoTask: mixVideoTask,
          videoUrl: url,
          img: img,
      });
    });
  },
  getOneSpotSliceVideo() {
    let id = this.data.id;
    getOneSpotSliceVideoApi({ id: id }).then((res) => {
      wx.hideLoading();
      console.log("res=", res);
      let mixVideoTask = res.data;
      mixVideoTask.mixVideoUrl = mixVideoTask.oriVideoUrl;
      let url = mixVideoTask.mixVideoUrl;
      let img = "https://k-cos.ai-market.top/wx/base/logo_horizontal.png";
      this.setData({
        mixVideoTask: mixVideoTask,
        videoUrl: url,
        img: img,
      });
    });
  },
  getOneLiveSplitVideo() {
    let id = this.data.id;
    queryOneSplitApi({ id: id }).then((res) => {
      console.log(" getOneLiveSplitVideo res=", res);

      let mixVideoTask = res.data;
      mixVideoTask.mixVideoUrl = mixVideoTask.videoUrl;

      this.setData({
        mixVideoTask: res.data,
        videoUrl: res.data.videoUrl,
        img: "https://k-cos.ai-market.top/wx/base/logo_horizontal.png",
      });
      
    });
  },

  queryOrder() {
    let id = this.data.id;
     // 构建查询参数，按照图片中的参数拼接
     let queryParams = {
      userId: app.globalData.userInfo.userId,
      bizId: id,
      payStatus: 1,
      pageNo: 1,
      pageSize: 1
    };
    // 查询订单, 判断支付状态
    queryOrderApi(queryParams).then((res) => {
      let that = this;
      wx.hideLoading();
      console.log("res=", res);
      if(res.data.list.length > 0){
        that.setData({
          payStatus: res.data.list[0].payStatus,
        });
      }
    });
  },
  //调用微信支付
  callWxPay(){
    let that = this;
    // 解析displayContent为对象
    let prePayInfo = that.data.prePayInfo;
    if (prePayInfo && prePayInfo.displayContent) {

        // 将displayContent字符串解析为JSON对象
     let paymentParams = JSON.parse(prePayInfo.displayContent);
      console.log("支付参数:", paymentParams);
        // 发起微信支付
      wx.requestPayment({
          timeStamp: paymentParams.timeStamp,
          nonceStr: paymentParams.nonceStr,
          package: paymentParams.packageValue,
          signType: paymentParams.signType,
          paySign: paymentParams.paySign,
          success: function(res) {
            console.log("res=", res);
             that.setData({
              payStatus: 1,
             });
             that.saveVideo();
             // 下载视频
          },
          fail: function(res) {
            console.log("res=", res);
            wx.showToast({
              title: "支付失败",
              duration: 2000
            });
          }
        });
    }
  },
    /**
   * 保存视频到本地
   */
    saveVideo() {
      let that = this;
      let link = that.data.mixVideoTask.mixVideoUrl;
      util.downloadToAlbum(link);
    },

});
