var util = require("@/utils/util.js");
import { queryOneSitTemplateApi } from "@/api/site";
import { payOrderApi } from "@/api/pay";
const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight + 10,
    statusHeight: app.globalData.statusHeight,
    navData: {
      title: "视频",
    },
    show: false,
    videoUrl: "",
    img: "",
    shareUrl: "",
    id: 0,
    // 0 合成视频 1 点位切片视频
    type: 0,
  },

  onLoad: function (options) {
    console.log("options", options);
    let id = options.id;
    let type = options.type;
    let shareUrl =
      "/pages/retrieval/play-template/index?pCode=" +
      app.globalData.user.userCode +
      "&id=" +
      id;
    this.setData({
      id: id,
      shareUrl: shareUrl,
      type: type,
    });
    this.queryOneSitTemplate();
  },

  onShareAppMessage: function () {
    let shareUrl = this.data.shareUrl;
    return {
      title: "酷拍侠 - 记录最酷的瞬间",
      imageUrl: "https://k-cos.ai-market.top/wx/base/logo-launch.png",
      path: shareUrl,
    };
  },
  onShareTimeline: function () {
    let shareUrl = this.data.shareUrl;
    return {
      title: app.globalData.share_config.title,
      path: shareUrl,
    };
  },
  mixVideo: function () {
    let id = this.data.id;
    wx.navigateTo({
      url: "/pages/retrieval/generate-video/index?templateId=" + id,
    });
  },

  queryOneSitTemplate() {
    let id = this.data.id;
    queryOneSitTemplateApi({ templateId: id }).then((res) => {
      wx.hideLoading();
      console.log("res=", res);
      let template = res.data;
      this.setData({
        img: template.coverUrl,
        videoUrl: template.demoVideo,
      });
    });
  },
});
