import { uploadFileToCos } from "@/utils/cos_config.js";
const util = require("@/utils/util.js");
import { querySitTemplateListApi } from "@/api/site";
const app = getApp();
import { retrievalApi } from "@/api/retrieval";
import { searchPositionApi } from "@/api/site";
import { facePageApi } from "@/api/user";
Page({
  data: {
    navData: {
      title: "",
    },
    isScanError: false,
    showBtn: true,
    isScan: false,
    cameraVisible: true, // 新增摄像头状态
    cameraAuth: false, // 新增权限状态标识

    countdown: 3,
    showCountdown: false,
    countdownAnimation: {},

    photoPath: "", // 新增照片存储路径
    paddingHeight: app.globalData.paddingHeight,
    id: 0,
    code: "",
    name: "333",
    locationState: "NO",
    lng: undefined,
    lat: undefined,
    faceList: [],
    selectedFace: -1,
  },

  onReady() {},

  onLoad: function (options) {
    console.log("options", options);
    let id = options.id;
    let code = options.code;
    let name = options.name;
    console.log("id", id);

    this.getFaceList();

    if (id != undefined) {
      console.log("id set", id);
      this.initSite(id, code, name);
    } else {
      this.setData({
        navData: {
          title: "授权位置获取视频",
        },
      });
      this.handleGetLocation();
    }
  },

  onUnload() {},

  // 检查摄像头权限
  checkCameraAuth() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting["scope.camera"]) {
          console.log("检查权限成功");
          this.initCamera();
        } else {
          console.log("检查权限失败");
          this.requestCameraAuth();
        }
      },
      fail: (err) => {
        console.error("检查权限失败：", err);
        wx.showToast({
          title: "权限检查失败",
          icon: "none",
        });
      },
    });
  },

  // 请求摄像头权限
  requestCameraAuth() {
    wx.authorize({
      scope: "scope.camera",
      success: () => this.initCamera(),
      fail: () => this.showAuthGuide(),
    });
  },
  // 初始化摄像头并开始流程
  initCamera() {
    if (this.data.cameraAuth) {
      console.log("initCamera 已经初始化");
      return;
    }

    this.setData({
      cameraAuth: true,
    });
    this.ctx = wx.createCameraContext();
    this.startCountdown();
  },
  // 显示权限引导提示
  showAuthGuide() {
    wx.showModal({
      title: "需要摄像头权限",
      content: "请授权使用摄像头以继续拍照功能",
      confirmText: "去设置",
      success: (res) => {
        if (res.confirm) {
          wx.openSetting({
            success: (res) => {
              if (res.authSetting["scope.camera"]) {
                this.initCamera();
              } else {
                wx.showToast({
                  title: "未授权无法使用",
                  icon: "none",
                });
              }
            },
          });
        }
      },
    });
  },

  resetScan() {
    if (
      app.globalData.userInfo.userId == 0 ||
      app.globalData.userInfo.userId == null
    ) {
      // 登陆
      wx.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }

    //如果摄像头已经初始化了，就不用了
    if (!this.data.cameraAuth) {
      this.checkCameraAuth();
      console.log("checkCameraAuth");
    }

    this.setData(
      {
        countdown: 3,
        isScan: false,
        isScanError: false,
        showCountdown: true,
        cameraVisible: true,
      },
      () => {
        this.startCountdown();
      },
    );
  },

  // 原有倒计时逻辑保持不变
  startCountdown() {
    const animation = wx.createAnimation({
      duration: 1000,
      timingFunction: "ease",
    });

    const run = (num) => {
      if (num <= 0) {
        this.setData({
          showCountdown: false,
        });
        console.log("takePicture ", num);
        this.takePicture();
        return;
      }

      // 第一步：放大显示
      animation.scale(1).opacity(1).step({
        duration: 300,
      });
      // 第二步：缩小并消失
      animation.scale(0.5).opacity(0).step({
        duration: 500,
      });

      // 第二步：缩小并消失
      animation.scale(1).opacity(0).step({
        duration: 200,
      });

      this.setData({
        countdown: num,
        countdownAnimation: animation.export(),
      });

      setTimeout(() => {
        run(num - 1);
      }, 1200);
    };

    run(this.data.countdown);
  },

  takePicture() {
    console.log("takePicture 2");
    let that = this;
    // 执行拍照操作
    this.ctx.takePhoto({
      quality: "high",
      success: (res) => {
        this.setData({
          isScan: true,
          photoPath: res.tempImagePath,
          cameraVisible: false, // 拍照后隐藏摄像头
        });
        console.log("拍照成功：", res.tempImagePath, new Date());
        that.retrieval(res.tempImagePath, 0);
        //this.savePhotoToAlbum(res.tempImagePath);
      },
      fail: (err) => {
        console.error("拍照失败：", err);
      },
    });
    wx.vibrateShort();
  },

  savePhotoToAlbum(tempPath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempPath,
      success: () => {
        wx.showToast({
          title: "照片保存成功",
        });
      },
      fail: (err) => {
        if (err.errMsg.includes("auth")) {
          this.openSetting();
        }
      },
    });
  },

  openSetting() {
    wx.openSetting({
      success: (res) => {
        if (res.authSetting["scope.writePhotosAlbum"]) {
          this.savePhotoToAlbum(this.data.photoPath);
        }
      },
    });
  },

  /**
   * 检索视频
   * source: 人脸来源：0表示拍照的，1表示原有
   */
  retrieval(photoPath, source) {
    let that = this;
    let src = photoPath;
    if (src == "") {
      util.showErrorToast("请上传图片");
      return false;
    }
    //根据文件路径，获取后缀
    let file_suffix = src.substring(src.lastIndexOf(".") + 1);
    let file_name = util.geneUuid() + "." + file_suffix;
    console.log("开始检索");
    uploadFileToCos(src, file_name, "face")
      .then((data) => {
        console.log("url=", data);
        let videoUrl = app.globalData.cosDomain + data.Key;
        that.doRetrieval(videoUrl, source);
      })
      .catch(async (error) => {
        wx.hideLoading();
        await util.showErrorToast("上传失败");
        console.error(error);
      });
  },

  doRetrieval(videoUrl, source) {
    let that = this;
    console.log("doRetrieval", videoUrl, source, that.data.id, that.data.code);
    retrievalApi({
      faceSource: source,
      siteId: that.data.id,
      siteCode: that.data.code,
      faceImageUrl: videoUrl,
    })
      .then(({ data }) => {
        wx.hideLoading();
        console.log("data=", data);
        if (data == 0) {
          that.setData({
            showBtn: false,
            isScan: false,
            isScanError: true,
          });
          return false;
        }
        var url = "/pages/retrieval/video-result/index?id=" + data;
        console.log("page=", url);
        wx.navigateTo({
          url: url,
        });
      })
      .catch((error) => {
        util.showErrorToast("检索失败");
        setTimeout(() => {
          this.setData({
            isScan: false,
            isScanError: true,
          });
        }, 3000);
      });
  },

  /**
   * 跳转到模板页面
   */
  gotoTemplate() {
    let that = this;
    if (that.data.id == 0) {
      util.showErrorToast("请先选择场所");
      return;
    }

    wx.showLoading({
      title: "加载中...",
    });

    // 查询当前场所的模板列表
    querySitTemplateListApi({
      siteId: that.data.id,
      siteCode: that.data.code,
    })
      .then((res) => {
        wx.hideLoading();
        console.log("模板列表=", res);

        // 过滤掉 prime 为 1 的模板
        let list = res.data.filter((item) => item.prime == 0);

        if (list.length > 0) {
          // 获取第一个模板的 ID
          let firstTemplateId = list[0].id;
          console.log(
            "跳转到生成视频页面，模板ID=",
            firstTemplateId,
            "场所ID=",
            that.data.id,
          );

          wx.navigateTo({
            url:
              "/pages/retrieval/generate-video/index?templateId=" +
              firstTemplateId +
              "&siteId=" +
              that.data.id,
          });
        } else {
          util.showErrorToast("暂无可用模板");
        }
      })
      .catch((error) => {
        wx.hideLoading();
        console.error("获取模板列表失败", error);
        util.showErrorToast("获取模板失败");
      });
  },

  goBack() {
    wx.navigateBack();
  },
  rePhoto() {
    wx.navigateBack();
  },

  /**
   * 跳转景区列表
   */
  gotoSiteAll() {
    console.log("gotoSiteAll");
    wx.navigateTo({
      url: "/pages/retrieval/site-all/index",
    });
  },
  // 获取位置信息
  handleGetLocation() {
    const that = this;
    wx.showLoading({
      title: "搜索附近景区...",
    });
    wx.getLocation({
      type: "gcj02",
      success(ret) {
        that.setData({
          lng: ret.longitude,
          lat: ret.latitude,
          locationState: "OK",
        });
        console.log("ret", ret);

        searchPositionApi({
          lng: ret.longitude,
          lat: ret.latitude,
        }).then((res) => {
          wx.hideLoading();
          console.log("111res", res);
          let len = res.data.length;
          if (len > 0) {
            that.initSite(
              res.data[0].id,
              res.data[0].siteCode,
              res.data[0].siteName,
            );
          } else {
            util.showToast("未找到景区，请切换景区");
          }
        });
      },
      fail: function (errInfo) {
        wx.hideLoading();
        console.log(errInfo, "errInfo");
        wx.showModal({
          title: "提醒",
          content:
            "您拒绝了位置授权，将无法使用大部分功能，点击确定重新获取授权",
          success(ret) {
            //如果点击确定
            if (ret.confirm) {
              that.onAuthLocation();
            } else {
              util.showToast("您拒绝了位置授权");
              that.setLocationState;
            }
          },
        });
      },
    });
  },

  onAuthLocation() {
    const that = this;
    wx.openSetting({
      success(res) {
        //如果同意了位置授权则userLocation=true
        if (res.authSetting["scope.userLocation"]) {
          console.log(res, "同意授权");
          that.handleGetLocation();
        }
      },
      fail: () => {
        wx.$toast("您拒绝了位置授权");
        that.handleGetLocation();
      },
    });
  },
  /**
   * 初始化站点
   */
  initSite(id, code, name) {
    console.log("initSite", id, code, name);
    var title = name;
    // 只显示8个字，超过8个字，显示省略号
    if (title.length > 8) {
      title = title.substring(0, 8) + "...";
    }
    this.setData({
      id: id,
      code: code,
      name: name,
      navData: {
        title: title,
      },
    });
  },
  // 获取人脸识别列表
  getFaceList() {
    facePageApi({
      siteId: this.data.id,
    }).then((res) => {
      console.log("res", res);

      if (res.data.list.length > 0) {
        this.setData({
          faceList: res.data.list,
          selectedFace: 0,
          photoPath: res.data.list[0].faceUrl,
        });
      } else {
        console.log("checkCameraAuth getFaceList ");
        this.resetScan();
      }
    });
  },
  selectFace(e) {
    console.log("selectFace", e);
    let index = e.currentTarget.dataset.index;
    console.log("index", index);
    this.setData({
      selectedFace: index,
      isScan: false,
      isScanError: false,
      photoPath: this.data.faceList[index].faceUrl,
    });
  },
  scanByFace() {
    if (
      app.globalData.userInfo.userId == 0 ||
      app.globalData.userInfo.userId == null
    ) {
      // 登陆
      wx.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }

    console.log("scanByFace");
    let index = this.data.selectedFace;
    console.log("index", index, this.data.faceList[index].faceUrl);

    this.setData({
      isScan: true,
      photoPath: this.data.faceList[index].faceUrl,
      cameraVisible: false, // 拍照后隐藏摄像头
      showBtn: false,
      isScanError: false,
    });

    this.doRetrieval(this.data.faceList[index].faceUrl, 1);
  },
  backToHome() {
    wx.navigateTo({
      url: "/pages/index/index",
    });
  },
});
