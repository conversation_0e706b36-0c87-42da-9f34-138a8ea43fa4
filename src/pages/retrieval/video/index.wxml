<PageHeader />
<NavBar style="z-index: 999" nav-data="{{ navData }}" id="navbar" />


<view class="video" style="margin-top: {{ paddingHeight }}px" wx:if="{{ id == 0 }}">
  <view class="video-position" style="top: {{ statusHeight + 8 }}px" >
    <view bind:tap="gotoSiteAll">切换</view>
    <image class="video-position-icon" src="/images/video/arrow.png" bind:tap="gotoSiteAll"/>
  </view>

  <image class="video-bg" src="https://k-cos.ai-market.top/wx/base/bg.png" />
    <view class="video-tips">
    <image class="video-icon" src="/images/camera/error.png"></image>
    <view class="text">授权位置/切换景区即可获取视频</view>
  </view>

  <button class="video-btn" bind:tap="handleGetLocation">
    <view>请先授权位置信息</view>
  </button>

</view>

  <view class="video-position" style="top: {{ statusHeight + 8 }}px" >
    <view bind:tap="gotoSiteAll">切换</view>
    <image class="video-position-icon" src="/images/video/arrow.png" bind:tap="gotoSiteAll"/>
  </view>

<view class="wrap" wx:if="{{ id != 0 }}">

  <view class="camera-box">
    <image
      wx:if="{{ isScan }}"
      class="camera-scan"
      src="/images/camera/scan.png"
    ></image>
    <image class="camera-bg" src="/images/camera/camera.png"></image>
    <camera
      wx:if="{{ cameraVisible && cameraAuth }}"
      device-position="front"
      flash="off"
      class="camera {{ isScanError ? 'error' : '' }}"
    ></camera>
    <view wx:else>
      <image
        class="camera-photo {{ isScanError ? 'blur' : '' }}"
        src="{{ photoPath }}"
      ></image>
    </view>
    <view wx:if="{{ showCountdown }}" class="camera-mask">
      <view>请正对镜头</view>
      <view class="num">{{ countdown }}</view>
    </view>
  </view>

  <view wx:if="{{ isScanError }}" class="tips" bind:tap="gotoTemplate">
    <image class="icon" src="/images/camera/error.png"></image>
    <view class="text">暂未查找到您的图像，体验自定义合成！</view>
    <image class="jump-icon" src="/images/yinge/arrow-right.png"></image>
  </view>

  <view  class="icons {{ isScanError ? 'less-margin' : '' }}">
    <view class="icons-item">
      <image class="icon" src="/images/camera/icon-1.png"></image>
      <view class="text">光线充足</view>
    </view>
    <view class="icons-item">
      <image class="icon" src="/images/camera/icon-2.png"></image>
      <view class="text">正对镜头</view>
    </view>
    <view class="icons-item">
      <image class="icon" src="/images/camera/icon-3.png"></image>
      <view class="text">光线充足</view>
    </view>
  </view>

  <view class="error-btn" >
    <view class="btn right" bind:tap="resetScan" >重新扫脸</view>
     <view class="btn left" bind:tap="scanByFace" wx:if="{{ faceList.length > 0 }}">直接查找</view>
  </view>

  <scroll-view class="photos-box" scroll-x>
    <view class="photos">
      <view wx:for="{{ faceList }}" wx:key="id" class="photos-item" bind:tap="selectFace" data-index="{{ index }}" data-face="{{ item.faceUrl }}">
        <view class="photos-border {{ index == selectedFace ? 'selected' : '' }}"></view>
        <image class="photo" src="{{ item.faceUrl }}"></image>
      </view>
    </view>
  </scroll-view>

  <image class="bottom" src="/images/camera/bottom.png"></image>
</view>

  <view class="float-sidebar">
    <view class="sidebar-item" bindtap="backToHome">
      <image class="sidebar-icon" src="/images/tabs/tab-0-active.png" mode="aspectFit"></image>
      <text class="sidebar-text">首页</text>
    </view>
  </view>
