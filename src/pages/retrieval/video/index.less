.wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #EBFBFC;
  padding: 278rpx 48rpx 0;
  box-sizing: border-box;


  .tips {
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #FF4444;
    text-align: center;
    margin: 48rpx 0 38rpx;
    cursor: pointer;
    transition: opacity 0.3s;

    &:active {
      opacity: 0.8;
    }

    .icon {
      width: 28rpx;
      height: 28rpx;
      margin-right: 8rpx;
    }

    .jump-icon {
      width: 40rpx;
      height: 40rpx;
      margin-left: 16rpx;
    }
  }

  .camera-box {
    position: relative;
    height: 620rpx;
    width: 548rpx;
    margin: 0 auto;

    .camera-bg {
      position: relative;
      z-index: 3;
      width: 100%;
      height: 100%;
    }

    .camera-photo {
      position: absolute;
      z-index: 1;
      left: 54rpx;
      top: 0;
      width: 440rpx;
      height: 620rpx;
    }

    .blur {
      filter: blur(20px);
    }

    .camera-mask {
      position: absolute;
      z-index: 2;
      left: 54rpx;
      top: 0;
      width: 440rpx;
      height: 620rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: rgba(0, 0, 0, .5);
      border-radius: 400rpx 400rpx 400rpx 400rpx;
      font-weight: 800;
      font-size: 32rpx;
      color: #FFFFFF;

      .num {
        margin-top: 20rpx;
        font-size: 120rpx;
      }
    }

    .camera-scan {
      position: absolute;
      z-index: 2;
      left: 54rpx;
      top: 0;
      width: 440rpx;
      height: 144rpx;
      animation: scanFace 3s ease-in-out infinite;
    }

    @keyframes scanFace {
      0% {
        transform: translateY(0);
      }

      50% {
        transform: translateY(476rpx);
      }

      100% {
        transform: translateY(0);
      }
    }

    .camera {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      margin: auto;
      width: 440rpx;
      height: 620rpx;
      border-radius: 400rpx;
      box-sizing: border-box;

      &.error {
        border: 8rpx solid #FF4444;
      }
    }

  }

  .btns {
    margin-top: 156rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .icons {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 158rpx 0 80rpx;
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 800;
    font-size: 24rpx;
    color: #000000;

    &.less-margin {
      margin: 0 0 40rpx;
    }
  }

  .error-btn {
    display: flex;
    justify-content: center;
    margin: 40rpx 0;
  }

  .icon {
    width: 60rpx;
    height: 60rpx;
  }

  .photos-box {
    // margin-top: 80rpx;
  }

  .photos {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding: 0 16rpx;

    &-item {
      position: relative;
      flex: 1 0 180rpx;
      width: 180rpx;
      height: 180rpx;
      border-radius: 10rpx;
      overflow: hidden;
      margin-right: 24rpx;
    }

    &-border {
      width: 180rpx;
      height: 180rpx;

      &.selected {
        background: linear-gradient(360deg, rgba(0, 206, 255, 1), rgba(87, 250, 230, 1));
      }
    }

    .photo {
      position: absolute;
      left: 8rpx;
      top: 8rpx;
      width: 164rpx;
      height: 164rpx;
      overflow: hidden;
      border-radius: 10rpx;
    }
  }

  .bottom {
    position: absolute;
    z-index: -1;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 566rpx;
  }
}


.btn {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 250rpx;
  height: 88rpx;
  border-radius: 100rpx;
  border: 4rpx solid rgba(5, 32, 49, 0.2);

  &.right {
    background: #F5F5F5;
    border: 2rpx solid #2FD2F8;
  }

  &.left {

    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    border: none;
    margin-left: 40rpx;
  }
}



.video {

  padding: 40rpx 24rpx;
  text-align: center;

  &-position {
    display: flex;
    align-items: center;
    position: fixed;
    left: 32rpx;
    z-index: 20;
    font-weight: bold;
    font-size: 28rpx;
    color: #052031;
    z-index: 9999;
    top: 108rpx;

    &-icon {
      width: 28rpx;
      height: 28rpx;
      margin-left: 2rpx;
    }
  }

  &-title {
    font-weight: bold;
    font-size: 40rpx;
    color: #052031;
    padding-top: 136rpx;
  }

  &-bg {
    width: 514rpx;
    height: 514rpx;
    margin-top: 50rpx;
  }

  &-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 526rpx;
    height: 104rpx;
    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    border-radius: 100rpx 100rpx 100rpx 100rpx;
    margin: 66rpx auto 50rpx;
  }

  &-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 18rpx;
  }

  &-tips {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: #052031;

    .link {
      color: #1FB5D7;
    }
  }
}

.popup {
  padding: 48rpx 56rpx 16rpx;
  text-align: center;

  &-title {
    font-weight: bold;
    font-size: 32rpx;
    color: #052031;
  }

  &-desc {
    font-size: 24rpx;
    color: #052031;
    opacity: .5;
    margin: 16rpx 0 48rpx;
  }

  &-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-btn {
    width: 300rpx;
    height: 80rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #052031;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    border-radius: 100rpx 100rpx 100rpx 100rpx;

    &.normal {
      background: #F5F5F5;
      border: 2rpx solid #D6D6D6;
    }
  }
}


/* 侧边栏浮窗样式 */
.float-sidebar {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.sidebar-item {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50rpx;
  width: 80rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.sidebar-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s;
}

.sidebar-item:active::after {
  opacity: 1;
}

.sidebar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.sidebar-text {
  color: #ffffff;
  font-size: 20rpx;
  line-height: 1;
}

/* 点击效果 */
.sidebar-item:active {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 入场动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.float-sidebar {
  /* ... 原有样式 ... */
  animation: slideIn 0.3s ease-out;
}
