page {
  background: #070E12;
}

.gen {
  padding: 40rpx 32rpx;

  // 演示视频容器
  .demo-video-container {
    width: 100%;
    height: 400rpx;
    border-radius: 16rpx;
    overflow: hidden;
    background-color: #2E3133;
    display: flex;
    align-items: center;
    justify-content: center;

    // 当视频为竖屏时，调整容器高度，为底部按钮留出空间
    // 计算：page-padding(80rpx) + footer(120rpx) + bottom-buttons(188rpx) + bottom-distance(140rpx) + gap(60rpx) = ~588rpx + safe-area
    &.portrait-container {
      height: calc(100vh - 600rpx - env(safe-area-inset-bottom));
      max-height: 700rpx;
      min-height: 280rpx;
      margin-bottom: 60rpx; // 增加底部边距，确保与按钮区域有足够间隙

      // 小屏幕适配 - 为底部按钮留出更多空间
      @media screen and (max-height: 667px) {
        height: calc(100vh - 520rpx - env(safe-area-inset-bottom));
        min-height: 250rpx;
        margin-bottom: 40rpx;
      }

      // 大屏幕适配
      @media screen and (min-height: 812px) {
        height: calc(100vh - 660rpx - env(safe-area-inset-bottom));
        max-height: 900rpx;
        margin-bottom: 80rpx;
      }
    }
  }

  // 演示视频样式
  .demo-video {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;

    // 横屏视频样式
    &.landscape {
      object-fit: cover;
    }

    // 竖屏视频样式
    &.portrait {
      object-fit: contain;
      background-color: #000;
    }
  }

  // 视频信息提示
  .video-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16rpx;
    padding: 12rpx 16rpx;
    background-color: rgba(46, 49, 51, 0.8);
    border-radius: 8rpx;
    font-size: 24rpx;

    .video-orientation {
      color: #2FD2F8;
      font-weight: 500;
    }

    .video-resolution {
      color: #ffffff;
      opacity: 0.7;
    }
  }

  &-tip{
    font-size: 28rpx;
    color: #ffffff;
    margin-top: 20rpx;
  }

  // 底部视频选择区域
  .bottom-buttons {
    position: fixed;
    left: 32rpx;
    right: 32rpx;
    bottom: calc(env(safe-area-inset-bottom) + 140rpx);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 24rpx;
    padding: 24rpx;
    background-color: rgba(29, 29, 29, 0.95);
    border-radius: 20rpx;
    backdrop-filter: blur(20rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
    z-index: 10;

    // 小屏幕适配 - 减少底部距离
    @media screen and (max-height: 667px) {
      bottom: calc(env(safe-area-inset-bottom) + 100rpx);
      padding: 20rpx;
      gap: 20rpx;

      .button-item {
        width: 120rpx;
        height: 120rpx;
      }
    }

    // 大屏幕适配 - 增加底部距离
    @media screen and (min-height: 812px) {
      bottom: calc(env(safe-area-inset-bottom) + 180rpx);
      padding: 28rpx;
      gap: 28rpx;
    }

    .button-item {
      position: relative;
      width: 140rpx;
      height: 140rpx;

      // 已选择的视频/图片样式
      .selected-item {
        position: relative;
        width: 100%;
        height: 100%;

        &-box {
          width: 100%;
          height: 100%;
          border-radius: 16rpx;
          overflow: hidden;
          background-color: #2E3133;
        }

        .del {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          width: 34rpx;
          height: 34rpx;
          z-index: 10;
        }

        .img {
          width: 100%;
          height: 100%;
          border-radius: 16rpx;
        }
      }

      // 未选择的添加按钮样式
      .add-item {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .add-btn {
          width: 100%;
          height: 100%;
          border-radius: 16rpx;
          background-color: #2E3133;
          border: 2rpx solid rgba(47, 210, 248, 0.2);
          transition: all 0.3s ease;
        }

        .video-length-tip {
          position: absolute;
          bottom: 12rpx;
          left: 0;
          right: 0;
          font-size: 24rpx;
          color: #2FD2F8;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
        }

        // 按钮点击效果
        &:active .add-btn {
          transform: scale(0.95);
          border-color: #2FD2F8;
        }
      }
    }
  }
}


.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 32rpx calc(env(safe-area-inset-bottom) + 20rpx);
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  background-color: #1D1D1D;
  color: #ffffff;
  z-index: 100;

  // 小屏幕适配 - 减少内边距
  @media screen and (max-height: 667px) {
    padding: 16rpx 32rpx calc(env(safe-area-inset-bottom) + 16rpx);
    font-size: 26rpx;
  }

  // 大屏幕适配 - 增加内边距
  @media screen and (min-height: 812px) {
    padding: 24rpx 32rpx calc(env(safe-area-inset-bottom) + 24rpx);
    font-size: 30rpx;
  }

  &-text {
    display: flex;
    align-items: center;

    .select {
      color: #2FD2F8;
    }
  }

  &-btn {
    width: 200rpx;
    height: 80rpx;
    text-align: center;
    line-height: 80rpx;
    font-weight: bold;
    font-size: 32rpx;
    color: #052031;
    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    border-radius: 100rpx 100rpx 100rpx 100rpx;

    // 小屏幕适配 - 减小按钮尺寸
    @media screen and (max-height: 667px) {
      width: 180rpx;
      height: 70rpx;
      line-height: 70rpx;
      font-size: 28rpx;
    }

    // 大屏幕适配 - 增大按钮尺寸
    @media screen and (min-height: 812px) {
      width: 220rpx;
      height: 88rpx;
      line-height: 88rpx;
      font-size: 34rpx;
    }
  }
}


.popup {
  width: 100%;
  height: 414rpx;
  text-align: center;
  padding: 60rpx 64rpx;
  box-sizing: border-box;

  &-title {
    font-weight: 800;
    font-size: 36rpx;

    .empty {
      display: block;
      width: 332rpx;
      height: 266rpx;
      margin: 100rpx auto 0;
    }

    .desc {
      font-size: 26rpx;
      color: #052031;
      line-height: 52rpx;
      padding: 0 90rpx;
      text-align: center;
    }

    &-desc {
      font-weight: 500;
      font-size: 28rpx;
      color: #052031;
      margin: 24rpx 0 60rpx;
      opacity: .5;
      line-height: 40rpx;
    }
  }
  &-desc {
    font-weight: 500;
    font-size: 28rpx;
    color: #052031;
    margin: 24rpx 0 60rpx;
    line-height: 40rpx;

    // 进度条容器
    .progress-container {
      margin: 20rpx 0;
    }

    // 进度条样式
    .progress-bar {
      width: 100%;
      height: 8rpx;
      background-color: #E5E7EB;
      border-radius: 4rpx;
      overflow: hidden;
      margin-bottom: 16rpx;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
      border-radius: 4rpx;
      transition: width 0.3s ease;
    }

    // 加载文字样式
    .loading-text {
      font-size: 28rpx;
      color: #052031;
      text-align: center;
      opacity: 0.8;
    }
  }

  // 按钮容器样式
  .popup-buttons {
    display: flex;
    justify-content: space-between;
    gap: 20rpx;
    margin-top: 20rpx;

    .popup-btn {
      flex: 1;
      margin: 0;
    }
  }
}

.btn {
  width: 204rpx;
  height: 80rpx;
  margin: 0 auto;
  display: grid;
  font-size: 36rpx;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
  border-radius: 100rpx 100rpx 100rpx 100rpx;
}
