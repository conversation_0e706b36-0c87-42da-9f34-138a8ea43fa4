<NavBar nav-data="{{ navData }}" />
<view class="gen" style="margin-top: {{ paddingHeight }}px">
  <!-- 模板演示视频 -->
  <view class="demo-video-container {{ videoOrientation === 'portrait' ? 'portrait-container' : '' }}">
    <video
      wx:if="{{ demoVideo }}"
      class="demo-video {{ videoOrientation === 'portrait' ? 'portrait' : 'landscape' }}"
      src="{{ demoVideo }}"
      mode="widthFix"
      poster="{{ coverUrl }}"
      show-center-play-btn
      show-play-btn
      object-fit="cover"
      autoplay
      show-fullscreen-btn
      picture-in-picture-mode="push"
      bindloadedmetadata="onVideoLoadedMetadata"
      bindplay="onPlay"
      binderror="onError"
      bindended="onEnded"
    />
    <image
      wx:else
      class="demo-video"
      src="{{ coverUrl || 'https://k-cos.ai-market.top/wx/banner/custom_mix_flow.png' }}"
    />
  </view>

  <!-- 视频方向提示 -->
  <view wx:if="{{ false }}" class="video-info" >
    <text class="video-orientation">{{ videoOrientation === 'landscape' ? '横屏视频' : '竖屏视频' }}</text>
    <text class="video-resolution">{{ videoWidth }}x{{ videoHeight }}</text>
  </view>

  <!-- 底部视频选择区域 -->
  <view class="bottom-buttons">
    <view class="button-item" wx:for="{{ videoList }}" wx:key="id">
      <!-- 已选择的视频/图片 -->
      <view wx:if="{{ item.url }}" class="selected-item">
        <image
          class="del"
          data-index="{{ index }}"
          bind:tap="deleteItem"
          src="/images/video/del.png"
        />
        <view class="selected-item-box">
          <image
            class="img"
            mode="aspectFill"
            data-index="{{ index }}"
            bind:tap="previewItem"
            bind:load="onImageLoad"
            bind:error="onImageError"
            src="{{ item.url }}"
          />
        </view>
      </view>

      <!-- 未选择的添加按钮 -->
      <view wx:else class="add-item"   bind:tap="addVideo"  data-index="{{ index }}" data-type="0">
        <image
          class="add-btn"
          src="/images/video/add.png"
        />
        <text
          wx:if="{{ item.duration }}"
          class="video-length-tip"
        >
          {{ item.duration }}s
        </text>
      </view>
    </view>
  </view>

  <view class="footer">
    <view class="footer-text">
      <view class="select">已选{{ selectNum }}</view>
      <view>/共{{ total }}</view>
    </view>
    <view wx:if="{{ selectNum === total }}" class="footer-btn" bind:tap="generateVideo">
      去合成
    </view>
    <view wx:else class="footer-btn" bind:tap="addVideo" data-index="{{ firstCustomIndex }}" data-type="1">
      合成同款
    </view>

  </view>
</view>

<van-popup show="{{ show }}" round bind:close="onClose">
  <view class="popup" style="width: 674rpx;">
    <view class="popup-title">温馨提示</view>
    <view class="popup-desc" wx:if="{{ taskStatus == 2 }}">
      视频已经合成完成，点击预览按钮即可预览
    </view>
    <view class="popup-desc" wx:if="{{ taskStatus == -1 }}">
      视频合成失败，请重新合成
    </view>
    <view class="popup-desc" wx:if="{{ taskStatus == 0 || taskStatus == 1 }}">
      <!-- 进度条 -->
      <view class="progress-container">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{ progressWidth }}%"></view>
        </view>
      </view>
      <!-- 动态文字 -->
      <view class="loading-text">视频合成中，{{ loadingText }}</view>
    </view>
    <!-- 按钮容器 -->
    <view class="popup-buttons">
      <view class="popup-btn btn" bindtap="goBack">返回</view>
      <view class="popup-btn btn" wx:if="{{ taskStatus == 2 }}" bindtap="download">立即预览</view>
      <view class="popup-btn btn" wx:if="{{ taskStatus == 0 || taskStatus == 1 }}" bindtap="refresh">刷新</view>
    </view>
  </view>
</van-popup>
