import { uploadFileToCos } from "@/utils/cos_config";
import util from "@/utils/util";
import { createCustomMixTaskApi } from "@/api/mix";
import {
  getOneMixVideoTaskApi,
  queryOneSiteCommonTemplateApi,
} from "@/api/retrieval";

const app = getApp();

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "合成同款",
      color: "#ffffff",
    },
    videoList: [],
    selectNum: 0,
    total: 0,
    templateId: 0,
    siteId: 0,
    firstCustomIndex: 0,
    materialTemplateConfig: "",
    materialTimeConfig: "", // 视频时长配置字符串
    materialTimeList: [], // 解析后的时长数组
    taskId: 0,
    show: false,
    taskStatus: 0,
    uploadList: [],
    // 模板演示视频相关
    demoVideo: "",
    coverUrl: "",
    videoOrientation: "", // 视频方向：landscape(横屏) 或 portrait(竖屏)
    videoWidth: 0,
    videoHeight: 0,
    // 进度条和文字动画相关
    progressWidth: 0,
    loadingText: "请稍后",
    loadingTextTimer: null,
    progressTimer: null,
  },

  onLoad(options) {
    console.log("options", options);
    let templateId = options.templateId;
    let siteId = options.siteId;
    this.setData({
      templateId: templateId,
      siteId: siteId,
    });

    this.getSitTemplateList(templateId);
  },
  onUnload() {
    console.log("onUnload");
    this.clearInterval();
    this.clearLoadingAnimation();
  },
  clearInterval() {
    if (this.data.timeOutId) {
      clearInterval(this.data.timeOutId);
    }
  },

  // 清理加载动画定时器
  clearLoadingAnimation() {
    if (this.data.loadingTextTimer) {
      clearInterval(this.data.loadingTextTimer);
      this.setData({ loadingTextTimer: null });
    }
    if (this.data.progressTimer) {
      clearInterval(this.data.progressTimer);
      this.setData({ progressTimer: null });
    }
  },

  // 启动加载动画
  startLoadingAnimation() {
    this.clearLoadingAnimation();

    // 文字动画：请稍后 -> 请稍后. -> 请稍后.. -> 请稍后...
    const textStates = ["请稍后", "请稍后.", "请稍后..", "请稍后..."];
    let textIndex = 0;

    const loadingTextTimer = setInterval(() => {
      this.setData({
        loadingText: textStates[textIndex],
      });
      textIndex = (textIndex + 1) % textStates.length;
    }, 500); // 每500ms切换一次文字

    // 进度条动画：模拟进度增长
    let progress = 0;
    const progressTimer = setInterval(() => {
      // 模拟进度增长，但不会到100%，因为我们不知道确切的完成时间
      if (progress < 90) {
        progress += Math.random() * 2; // 随机增长0-2%
        if (progress > 90) progress = 90; // 最多到90%
        this.setData({
          progressWidth: progress,
        });
      }
    }, 1000); // 每1秒更新一次进度

    this.setData({
      loadingTextTimer,
      progressTimer,
      progressWidth: 10, // 初始进度10%
    });
  },

  getSitTemplateList(templateId) {
    queryOneSiteCommonTemplateApi({
      id: templateId,
    }).then((res) => {
      console.log("getSitTemplateList res=", res);

      if (!res.data) {
        wx.showToast({
          title: "模板配置错误",
          icon: "none",
        });
        this.setData({
          videoList: [],
        });
        return;
      }

      //  "materialTemplateConfig": "01010", 中有1表示用户素材
      let materialTemplateConfig = res.data.materialTemplateConfig;
      let materialTimeConfig = res.data.materialTimeConfig || "";
      console.log("demoVideo=", res.data.demoVideo);
      console.log("materialTimeConfig=", materialTimeConfig);

      // 解析时长配置
      let materialTimeList = [];
      if (materialTimeConfig) {
        materialTimeList = materialTimeConfig.split(",").map((time) => {
          const parsedTime = parseFloat(time.trim());
          return isNaN(parsedTime) ? 0 : parsedTime;
        });
      }
      console.log("materialTimeList=", materialTimeList);

      this.setData({
        materialTemplateConfig: materialTemplateConfig,
        materialTimeConfig: materialTimeConfig,
        materialTimeList: materialTimeList,
        demoVideo: res.data.demoVideo || "",
        coverUrl: res.data.coverUrl || "",
      });

      this.getSitTemplateMaterialList();
    });
  },

  /**
   * 获取站点模板素材
   * 根据 materialTemplateConfig 生成 videoList，不再调用接口
   */
  getSitTemplateMaterialList() {
    // 处理素材模板配置
    let materialTemplateConfig = this.data.materialTemplateConfig;
    let materialTimeList = this.data.materialTimeList;
    console.log("materialTemplateConfig=", materialTemplateConfig);
    console.log("materialTimeList=", materialTimeList);

    if (!materialTemplateConfig) {
      console.error("materialTemplateConfig 为空");
      wx.showToast({
        title: "模板配置错误",
        icon: "none",
      });
      return;
    }

    let videoList = [];
    let totalCustom = 0;
    // 遍历materialTemplateConfig，根据配置生成videoList
    let firstCustomIndex = -1;

    for (let i = 0; i < materialTemplateConfig.length; i++) {
      if (materialTemplateConfig[i] == "1") {
        // 用户素材，添加空白占位
        // 获取对应位置的时长
        let duration =
          materialTimeList && materialTimeList[i] ? materialTimeList[i] : null;

        videoList.push({
          id: i,
          type: 1,
          url: "",
          videoUrl: "",
          duration: duration, // 添加时长字段
        });
        totalCustom++;
        if (firstCustomIndex == -1) {
          firstCustomIndex = i;
        }
      }
    }

    console.log("videoList=", videoList);
    this.setData({
      videoList: videoList,
      total: totalCustom,
      firstCustomIndex: firstCustomIndex,
    });
    // 计算已选择的素材数量
    this.calcSelectNum();
  },

  calcSelectNum() {
    const { videoList } = this.data;
    const list = videoList.filter((v) => v.url != "");

    console.log("calcSelectNum videoList=", list);
    this.setData({
      selectNum: list.length,
    });
  },

  addVideo(e) {
    console.log("=== addVideo 方法开始执行 ===");
    console.log("事件对象 e:", e);
    console.log("e.currentTarget:", e.currentTarget);
    console.log("e.currentTarget.dataset:", e.currentTarget.dataset);

    const { index, type } = e.currentTarget.dataset;
    const { videoList, selectNum, total } = this.data;

    console.log("提取的参数 - index:", index, "type:", type);
    console.log("当前数据状态 - selectNum:", selectNum, "total:", total);
    console.log(
      "videoList长度:",
      videoList ? videoList.length : "videoList为空",
    );

    if (!videoList || videoList.length === 0) {
      console.error("videoList为空或未初始化");
      wx.showToast({
        title: "数据未加载完成，请稍后再试",
        icon: "none",
      });
      return;
    }

    if (index === undefined || index === null) {
      console.error("index参数无效:", index);
      return;
    }

    const video = videoList[index];
    console.log("目标视频对象:", video);

    if (!video) {
      console.error("未找到对应的视频对象，index:", index);
      return;
    }

    // 修复选择数量逻辑
    let canSelectNum;
    let isFullReselect = false;

    // "合成同款"按钮：选择所有剩余的视频
    if (type === "1") {
      canSelectNum = total - selectNum;
      isFullReselect = true;
      console.log("合成同款模式：需要选择", canSelectNum, "个视频");
    } else {
      // 单个格子按钮：可以选择多个视频填充空位
      // 如果已经有选择的视频，只能选择1个替换当前位置
      // 如果没有选择的视频，可以选择多个填充所有空位
      if (selectNum > 0) {
        canSelectNum = 1;
      } else {
        canSelectNum = total; // 可以一次选择所有需要的视频
      }
      isFullReselect = false;
      console.log(
        "单个选择模式：可以选择",
        canSelectNum,
        "个视频，目标位置index=",
        index,
      );
    }

    // 检查是否有有效的选择数量
    if (canSelectNum <= 0) {
      console.log("没有需要选择的视频");
      return;
    }

    console.log("开始调用wx.chooseMedia，参数:", {
      count: canSelectNum,
      mediaType: ["mix"],
      maxDuration: 60,
    });
    wx.chooseMedia({
      count: canSelectNum,
      mediaType: ["mix"], // mix表示可以同时选择图片和视频
      sourceType: ["album", "camera"], // album: 相册, camera: 拍照
      maxDuration: 60, // 增加到60秒，给用户更多选择空间
      camera: "back",
      success: (res) => {
        console.log("wx.chooseMedia success:", res);

        // 检查是否有文件选择失败
        if (res.failedCount > 0) {
          console.warn(`有 ${res.failedCount} 个文件选择失败`);
          let errorMsg = "部分文件选择失败";
          if (res.tempFiles.length === 0) {
            errorMsg = "视频选择失败，可能是文件过大或时长超过60秒";
          }
          wx.showToast({
            title: errorMsg,
            icon: "none",
            duration: 3000,
          });
        }

        // 检查是否有有效的文件
        if (!res.tempFiles || res.tempFiles.length === 0) {
          console.error("没有选择到有效的文件");
          return;
        }

        wx.showLoading({
          title: "上传中",
          mask: true,
        });
        const arr = [];

        // 记录需要上传的文件数量，用于跟踪上传完成状态
        const totalUploadCount = res.tempFiles.length;
        let completedUploadCount = 0;

        res.tempFiles.forEach((item, fileIndex) => {
          console.log(`处理文件${fileIndex}:`, item);
          console.log(`文件${fileIndex}详细信息:`, {
            tempFilePath: item.tempFilePath,
            thumbTempFilePath: item.thumbTempFilePath,
            size: item.size,
            duration: item.duration,
            height: item.height,
            width: item.width,
          });
          const _v = {};

          if (item.thumbTempFilePath) {
            // 视频文件：有缩略图
            _v.videoUrl = item.tempFilePath;
            _v.url = item.thumbTempFilePath;
            console.log(`文件${fileIndex}(视频)处理：`, {
              videoUrl: item.tempFilePath,
              thumbUrl: item.thumbTempFilePath,
            });
          } else {
            // 图片文件：没有缩略图，使用原文件作为封面
            _v.url = item.tempFilePath;
            _v.videoUrl = item.tempFilePath;
            console.log(`文件${fileIndex}(图片)处理：`, {
              url: item.tempFilePath,
            });
          }

          // 确保每个文件都有url
          if (!_v.url) {
            console.error(`文件${fileIndex}没有url!`, item);
            _v.url = item.tempFilePath; // 兜底处理
          }

          // 暂时注释掉异步上传，避免封面错乱
          // 上传将在界面更新后进行
          console.log(`文件${fileIndex}暂不上传，先显示封面`);

          // 详细记录URL信息
          console.log(`文件${fileIndex}最终URL信息:`, {
            url: _v.url,
            videoUrl: _v.videoUrl,
            originalThumbPath: item.thumbTempFilePath,
            originalTempPath: item.tempFilePath,
          });

          arr.push(_v);
          console.log(
            `文件${fileIndex}添加到arr:`,
            JSON.stringify(_v, null, 2),
          );
        });

        console.log("所有文件处理完成，arr=", arr);

        if (isFullReselect || (canSelectNum > 1 && selectNum === 0)) {
          // 多视频选择逻辑：填充所有空的用户素材位置
          let arrIndex = 0;
          console.log("多选模式：总共选择了", arr.length, "个文件");
          console.log(
            "当前videoList状态:",
            videoList.map((item, idx) => ({
              index: idx,
              type: item.type,
              hasUrl: !!item.url,
              url: item.url,
            })),
          );

          videoList.forEach((item, itemIndex) => {
            // 更精确的空位判断
            const isEmpty =
              item.url === "" || item.url === null || item.url === undefined;
            console.log(
              `检查位置${itemIndex}: type=${item.type}, url="${item.url}", isEmpty=${isEmpty}, arrIndex=${arrIndex}`,
            );

            if (isEmpty && arrIndex < arr.length) {
              const _v = arr[arrIndex];
              console.log(`准备分配文件${arrIndex}到位置${itemIndex}:`, _v);
              console.log(`分配前URL检查 - 源文件${arrIndex}:`, {
                url: _v.url,
                videoUrl: _v.videoUrl,
              });
              arrIndex++;

              // 创建新的对象避免引用问题
              const updatedItem = {
                ...item,
                url: _v.url,
                videoUrl: _v.videoUrl,
              };
              videoList[itemIndex] = updatedItem;

              console.log(`分配后URL检查 - 位置${itemIndex}:`, {
                url: updatedItem.url,
                videoUrl: updatedItem.videoUrl,
              });

              console.log(
                "多选更新 itemIndex=",
                itemIndex,
                "updatedItem=",
                updatedItem,
                "_v=",
                _v,
              );
            }
          });

          // 检查是否所有文件都被分配了
          if (arrIndex < arr.length) {
            console.warn(
              "警告：有",
              arr.length - arrIndex,
              "个文件没有被分配到位置",
            );
            console.warn("剩余文件:", arr.slice(arrIndex));
          }
          console.log(
            "分配完成，最终arrIndex=",
            arrIndex,
            "总文件数=",
            arr.length,
          );
        } else {
          // 单视频选择逻辑：只更新指定位置的视频项
          if (arr[0] && index >= 0 && index < videoList.length) {
            // 创建新的对象避免引用问题
            const updatedItem = {
              ...videoList[index],
              videoUrl: arr[0].videoUrl,
              url: arr[0].url,
            };
            videoList[index] = updatedItem;
            console.log(
              "单选更新 index=",
              index,
              "updatedItem=",
              updatedItem,
              "arr[0]=",
              arr[0],
            );
          }
        }

        // 添加调试信息
        console.log("准备更新videoList:", videoList);
        console.log(
          "最终videoList状态:",
          videoList.map((item, idx) => ({
            index: idx,
            type: item.type,
            hasUrl: !!item.url,
            url: item.url ? item.url.substring(0, 50) + "..." : "空",
          })),
        );

        this.setData(
          {
            videoList,
          },
          () => {
            this.calcSelectNum();
            // 立即隐藏loading，让用户看到封面，上传在后台进行
            wx.hideLoading();
            console.log(
              "videoList更新完成，当前数据:",
              this.data.videoList.map((item, idx) => ({
                index: idx,
                hasUrl: !!item.url,
                url: item.url ? "有封面" : "无封面",
              })),
            );

            // 界面更新完成后，开始后台上传视频文件
            console.log("开始后台上传视频文件");
            arr.forEach((fileItem, fileIndex) => {
              if (fileItem.videoUrl) {
                console.log(`开始上传文件${fileIndex}:`, fileItem.videoUrl);
                this.uploadVideo(fileItem.videoUrl)
                  .then(() => {
                    console.log(`文件${fileIndex}上传成功`);
                  })
                  .catch((error) => {
                    console.error(`文件${fileIndex}上传失败:`, error);
                  });
              }
            });
          },
        );
      },
      fail: (err) => {
        console.error("wx.chooseMedia失败:", err);
        wx.hideLoading();
        wx.showToast({
          title: "选择文件失败",
          icon: "none",
        });
      },
    });
  },

  async uploadVideo(src) {
    try {
      const file_suffix = src.substring(src.lastIndexOf(".") + 1);
      const file_name = util.geneUuid() + "." + file_suffix;
      const data = await uploadFileToCos(src, file_name, "video");

      let videoUrl = app.globalData.cosDomain + data.Key;
      console.log("url=，src", videoUrl, src);

      let uploadItem = {
        src: src,
        videoUrl: videoUrl,
      };
      let uploadList = this.data.uploadList;
      uploadList.push(uploadItem);
      this.setData({
        uploadList: uploadList,
      });

      // 返回成功状态
      return Promise.resolve();
    } catch (error) {
      console.log("error: ", error);
      util.showErrorToast("上传失败");
      // 返回失败状态
      return Promise.reject(error);
    }
  },

  deleteItem(e) {
    const { index } = e.currentTarget.dataset;
    const { videoList } = this.data;
    const video = videoList[index];

    // 清理所有相关的URL引用
    console.log(`删除位置${index}的视频，清理URL引用:`, {
      url: video.url,
      videoUrl: video.videoUrl,
    });

    video.url = "";
    video.videoUrl = "";

    this.setData(
      {
        [`videoList[${index}]`]: video,
      },
      () => {
        this.calcSelectNum();
      },
    );
  },

  previewItem(e) {
    const { index } = e.currentTarget.dataset;
    const { videoList } = this.data;
    const video = videoList[index];
    if (video.videoUrl) {
      wx.previewMedia({
        sources: [
          {
            url: video.videoUrl,
            type: "video",
          },
        ],
      });
    } else {
      wx.previewImage({
        urls: [video.url],
      });
    }
  },

  generateVideo() {
    console.log("generateVideo");
    const { videoList } = this.data;
    const list = videoList.filter((v) => v.url != "");
    const customlist = videoList;
    customlist.forEach((v) => {
      if (v.videoUrl == "") {
        wx.showToast({
          title: "请选择素材",
          icon: "none",
        });
        return;
      } else {
        let uploadItem = this.data.uploadList.find(
          (item) => item.src == v.videoUrl,
        );
        if (uploadItem) {
          v.videoUrl = uploadItem.videoUrl;
        }
      }
    });

    let customList = customlist.filter(
      (v) => v.videoUrl != "" && v.videoUrl != undefined && v.videoUrl != null,
    );
    if (customList.length == 0) {
      wx.showToast({
        title: "等视频上传完成. ",
        icon: "none",
      });
      return;
    }
    // 检查视频是否上传完成
    customList = customlist.filter((v) => v.videoUrl.indexOf("tmp") != -1);
    if (customList.length > 0) {
      wx.showToast({
        title: "等视频上传完成. ",
        icon: "none",
      });
      return;
    }

    console.log("generateVideo videoList=", list);
    wx.showLoading({
      title: "合成中,请稍后...",
      mask: true,
    });

    let data = {
      siteId: this.data.siteId,
      siteTemplateId: this.data.templateId,
      customMateriaList: list.map((v) => v.videoUrl),
    };
    console.log("generateVideo data=", data);
    createCustomMixTaskApi(data)
      .then((res) => {
        console.log("createCustomMixTaskApi res=", res);
        if (!util.requestSuccess(res.code)) {
          wx.hideLoading();
          wx.showModal({
            title: "提示",
            content: res.msg,
            showCancel: false,
            confirmText: "确定",
          });
          return;
        }

        this.setData({
          taskId: res.data,
          show: true,
        });

        // 启动加载动画
        this.startLoadingAnimation();

        let timeOutId = setInterval(() => {
          this.getOneMixVideoTask();
        }, 3000);
        this.setData({ timeOutId: timeOutId });
      })
      .catch((error) => {
        wx.hideLoading();
        util.showErrorToast("合成失败");
      });
  },
  getOneMixVideoTask() {
    let that = this;
    let id = this.data.taskId;
    getOneMixVideoTaskApi({ id: id }).then((res) => {
      console.log("res=", res);
      if (res.data.taskStatus == 2) {
        wx.hideLoading();
        this.clearInterval();
        this.clearLoadingAnimation();
        // 任务完成，进度条设为100%
        this.setData({
          progressWidth: 100,
        });
      }
      this.setData({
        taskStatus: res.data.taskStatus,
      });
      if (res.data.taskStatus == -1) {
        wx.hideLoading();
        util.showErrorToast("合成失败");
        this.clearInterval();
        this.clearLoadingAnimation();
      }
    });
  },
  download(e) {
    let id = this.data.taskId;
    this.setData({
      selectNum: 0,
      show: false,
    });
    wx.navigateTo({
      url: "/pages/retrieval/play/index?type=0&id=" + id,
    });
  },
  goBack(e) {
    wx.navigateBack({
      delta: 1,
    });
  },

  onClose() {
    this.setData({
      show: false,
    });
    // 关闭弹窗时清理动画
    this.clearLoadingAnimation();
  },
  // 视频相关事件处理
  onVideoLoadedMetadata(e) {
    console.log("视频元数据加载完成", e.detail);
    const { width, height } = e.detail;

    // 判断视频方向
    const orientation = width > height ? "landscape" : "portrait";

    console.log(`视频分辨率: ${width}x${height}, 方向: ${orientation}`);

    this.setData({
      videoWidth: width,
      videoHeight: height,
      videoOrientation: orientation,
    });
  },

  onPlay(e) {
    console.log("视频开始播放", e);
  },

  onError(e) {
    console.log("视频播放错误", e);
    wx.showToast({
      title: "视频加载失败",
      icon: "none",
    });
  },

  onEnded(e) {
    console.log("视频播放结束", e);
  },

  refresh() {
    wx.showLoading({
      title: "刷新中,请稍后...",
      mask: true,
    });
    this.getOneMixVideoTask();
  },

  // 图片加载成功事件
  onImageLoad(e) {
    const { index } = e.currentTarget.dataset;
    console.log(`图片加载成功 - 位置${index}:`, e.detail);
  },

  // 图片加载失败事件
  onImageError(e) {
    const { index } = e.currentTarget.dataset;
    console.error(`图片加载失败 - 位置${index}:`, e.detail);
    console.error(`失败的图片URL:`, this.data.videoList[index]?.url);
  },
});
