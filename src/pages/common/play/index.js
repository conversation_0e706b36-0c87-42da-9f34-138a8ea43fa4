var util = require("@/utils/util.js");

const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight + 10,
    statusHeight: app.globalData.statusHeight,
    navData: {
      title: "视频",
    },
    videoUrl: "",
    videoId: "",
    shareUrl: "",
    isDownloading: false,
  },

  onLoad: function (options) {
    // 接收传递过来的参数
    const { videoUrl, videoId } = options;

    // 解码视频链接（因为跳转时进行了编码）
    const decodedVideoUrl = videoUrl ? decodeURIComponent(videoUrl) : "";

    // 构建分享链接（修复原有问题）
    let shareUrl = `/pages/common/play/index?pCode=${app.globalData.user.userCode}`;
    if (videoId) {
      shareUrl += `&videoId=${videoId}`;
    }
    if (videoUrl) {
      shareUrl += `&videoUrl=${videoUrl}`;
    }

    this.setData({
      videoUrl: decodedVideoUrl,
      videoId: videoId,
      shareUrl: shareUrl,
    });

    // 如果没有视频链接，显示错误提示
    if (!decodedVideoUrl) {
      wx.showToast({
        title: "视频链接无效",
        icon: "none",
      });
    }
  },
  onShareAppMessage: function () {
    let shareUrl = this.data.shareUrl;
    return {
      title: "酷拍侠 - 记录最酷的瞬间",
      imageUrl: "https://k-cos.ai-market.top/wx/base/logo-launch.png",
      path: shareUrl,
    };
  },
  // 修改下载方法，添加节流
  downloadVideo: function () {
    if (this.data.isDownloading) {
      return;
    }

    this.setData({ isDownloading: true });

    let that = this;
    let link = that.data.videoUrl;
    util.downloadToAlbum(link);

    // 2秒后重置状态（下载可能需要更长时间）
    setTimeout(() => {
      this.setData({ isDownloading: false });
    }, 2000);
  },
});
