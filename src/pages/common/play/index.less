page {
  background-color: #EBFBFC;
}

.video {
  padding: 0;
  text-align: center;
   width: 100%;
   background-color: #000;


   position: fixed;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   z-index: 1;

   &-player {
    width: 100%;
    height: 82%;
  }

}

.bottom-actions {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx 50rpx;
  background-color: #000;
}

.action-btn {
  margin-top: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 100rpx;
  height: 76rpx;
  width: 300rpx;
}

.action-btn text {
  color: #fff;
  font-size: 14px;
  margin-left: 10rpx;
}

.action-btn-icon {
  width: 43rpx;
  height: 43rpx;
}