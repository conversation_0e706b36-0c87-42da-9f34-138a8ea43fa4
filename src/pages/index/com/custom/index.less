.card {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  &-item {
    width: 342rpx;
    border-radius: 30rpx;
    background-color: #ffffff;
    overflow: hidden;
    margin-bottom: 20rpx;
  }

  &-img {
    width: 100%;
    height: 342rpx;
  }

  &-title {
    font-weight: bold;
    font-size: 24rpx;
    color: #052031;
    padding: 10rpx 20rpx 0;
  }

  &-desc {
    font-weight: 500;
    font-size: 20rpx;
    color: #052031;
    opacity: .5;
    margin: 4rpx 0 10rpx;
    padding: 0 20rpx;
  }

  &-price {
    display: flex;
    font-weight: bold;
    font-size: 36rpx;
    color: #FF4F28;
    padding: 0 20rpx 20rpx;
    align-items: baseline;

    .unit {
      font-size: 24rpx;
    }
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 200rpx;

  .empty-text {
    color: #999;
    font-size: 28rpx;
  }
}
