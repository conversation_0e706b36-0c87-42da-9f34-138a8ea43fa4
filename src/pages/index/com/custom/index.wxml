<view class="card">
  <!-- 商品列表 -->
  <view class="card-item" wx:for="{{ list }}" wx:key="id" bind:tap="_gotoCustom" data-id="{{ item.id }}">
    <image class="card-img" src="{{ item.img || item.picUrl }}" />
    <view class="card-title">{{ item.name }}</view>
    <view class="card-desc">{{ item.desc || item.introduction }}</view>
    <view class="card-price" wx:if="{{ item.price }}">
      <view class="unit">￥</view>
      <view>{{ item.price }}</view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{ list.length === 0 }}" class="empty-container">
    <view class="empty-text">暂无商品</view>
  </view>
</view>
