Component({
  properties: {
    list: {
      type: Array,
      default: [],
    },
  },
  data: {},
  methods: {
    _playVideo: function (e) {
      let id = e.currentTarget.dataset.id;
      console.log("id", id);

      wx.navigateTo({
        //url: '/pages/retrieval/play-template/index?id=' + id,
        url:
          "/pages/retrieval/play-video/index?id=" +
          id +
          "&img=" +
          e.currentTarget.dataset.img,
      });
    },
  },
});
