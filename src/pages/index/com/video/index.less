.box {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  &-item {
    width: 342rpx;
    margin-bottom: 18rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    overflow: hidden;
  }

  &-img {
    width: 100%;
    height: 192rpx;
  }

  &-title {
    font-weight: bold;
    font-size: 24rpx;
    color: #052031;
    margin: 10rpx 0 8rpx;
    padding: 0 20rpx;
  }

  &-date {
    font-size: 16rpx;
    color: #052031;
    margin-bottom: 10rpx;
    padding: 0 20rpx;
  }

  &-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx 16rpx;

    &__left {
      display: flex;
      align-items: center;
      font-size: 20rpx;
      color: #052031;
    }

    &__right {
      display: flex;
      align-items: center;
      font-size: 16rpx;
      color: #052031;
    }

    &__avatar {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
      border-radius: 32rpx;
    }

    .icon {
      width: 16rpx;
      height: 16rpx;
    }

    .number {
      margin-left: 18rpx;
    }
  }
}