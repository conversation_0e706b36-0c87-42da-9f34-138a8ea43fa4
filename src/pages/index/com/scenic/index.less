.box {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 200rpx;
  border-radius: 30rpx;
  overflow: hidden;
  background-color: #ffffff;
  margin-bottom: 24rpx;

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40rpx 24rpx;
    flex: 1;
    font-weight: bold;
    font-size: 36rpx;
    color: #052031;
  }

  &-img {
    width: 200rpx;
    height: 200rpx;
    border-radius: 30rpx;
    overflow: hidden;
  }

  &-address {
    font-weight: 500;
    font-size: 28rpx;
    color: #052031;
    opacity: .5;
  }
}