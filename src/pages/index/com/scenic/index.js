Component({
  properties: {
    list: {
      type: Array,
      default: [],
    },
  },
  data: {},
  methods: {
    _searchVideo: function (e) {
      let id = e.currentTarget.dataset.id;
      let live = e.currentTarget.dataset.live;
      let code = e.currentTarget.dataset.code;
      let name = e.currentTarget.dataset.name;
      console.log("id - code - name", id, code, name);

      if (live) {
        wx.navigateTo({
          url: "/play-live/live-list/index?id=" + id + "&code=" + code,
        });
      } else {
        wx.navigateTo({
          url:
            "/pages/retrieval/video/index?id=" +
            id +
            "&code=" +
            code +
            "&name=" +
            name,
        });
      }
    },
  },
});
