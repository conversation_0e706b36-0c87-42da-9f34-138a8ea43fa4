Component({
  properties: {
    list: {
      type: Array,
      default: [],
    },
  },
  data: {
    hasMore: true,
    page: 1,
    pageSize: 10,
  },

  observers: {
    list: function (newList) {
      // 当父组件传入的list发生变化时，更新组件内部状态
      console.log("Template list updated:", newList);
    },
  },

  methods: {
    // 模板卡片点击事件
    onTemplateTap(e) {
      const templateId = e.currentTarget.dataset.id;
      console.log("templateId", templateId);
      wx.navigateTo({
        url: `/pages/retrieval/generate-video/index?templateId=${templateId}`,
      });
    },

    // 点赞事件
    onLikeTap(e) {
      const templateId = e.currentTarget.dataset.id;
      // 这里可以调用点赞API
      wx.showToast({
        title: "点赞功能开发中",
        icon: "none",
      });
    },

    // 分享事件
    onShareTap(e) {
      const templateId = e.currentTarget.dataset.id;
      wx.showToast({
        title: "分享功能开发中",
        icon: "none",
      });
    },
  },
});
