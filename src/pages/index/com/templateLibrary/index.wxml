<view class="template-container">
  <view class="template-grid">

      <block wx:for="{{list}}" wx:key="id">
      <view
        class="template-card"
        bindtap="onTemplateTap"
        data-id="{{ item.templateId }}"
      >
        <!-- 模板预览图 -->
        <view class="template-cover">
          <image class="cover-image" src="{{ item.templateCover }}" mode="widthFix"  />
        </view>

        <!-- 模板信息 -->
        <view class="template-info">
          <view class="template-title">{{ item.title }}</view>
          <view class="template-stats">
            <view class="stat-item">
              <image
                class="like-icon {{ item.isLiked ? 'liked' : '' }}"
                src="/images/icons/{{ item.isLiked ? 'heart-red' : 'heart-grey' }}.png"
                bindtap="onLikeTap"
                data-id="{{ item.id }}"
                catchtap="true"
              />
              <text class="stat-text">{{ item.likeCount }}</text>
            </view>
            <view class="stat-item">
              <image
                class="like-icon {{ item.isLiked ? 'liked' : '' }}"
                src="/images/icons/{{ item.isLiked ? 'like-red' : 'like' }}.png"
                bindtap="onLikeTap"
                data-id="{{ item.id }}"
                catchtap="true"
              />
              <text class="stat-text">{{ item.useCount }}</text>
            </view>
            <!-- <view class="share-btn" bindtap="onShareTap" data-id="{{ item.id }}" catchtap="true">
              <image class="share-icon" src="/images/icons/share.png" />
            </view> -->
          </view>
        </view>
      </view>
      </block>
    </view>
    <!-- 空状态 -->
    <view wx:if="{{ list.length === 0 }}" class="empty-state">
      <image class="empty-icon" src="/images/icons/empty.png" />
      <text class="empty-text">暂无相关模板</text>
    </view>
  </view>
