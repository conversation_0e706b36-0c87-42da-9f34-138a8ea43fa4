  // 模板列表样式
  .template-container {
    padding: 0 4rpx;
    width: 100%;
    box-sizing: border-box;


    .template-grid {
      // 瀑布流布局 - 使用 CSS columns 实现
      column-count: 2;
      column-gap: 24rpx;
      padding: 20rpx 0;

      // 确保在不同屏幕尺寸下都显示两列
      @media screen and (max-width: 750rpx) {
        column-gap: 20rpx;
      }

      .template-card {
        background: #FFFFFF;
        border-radius: 32rpx;
        margin-bottom: 24rpx;
        overflow: hidden;
        box-shadow: 0px 4rpx 20rpx 0px rgba(54, 127, 137, 0.1);
        break-inside: avoid; // 防止卡片被分割到不同列
        display: inline-block; // 确保瀑布流效果
        width: 100%; // 占满列宽
        .template-cover {
          position: relative;


          .cover-image {
            width:100%;
            height: auto;
          // aspect-ratio: 1;
            display: block;
            border-radius: 32rpx 32rpx 0 0;
          }
        }

        .template-info {
          padding: 20rpx 20rpx 20rpx 20rpx;

          .template-title {
            font-size: 24rpx;
            color: #052031;
            font-weight: 400;
            margin-bottom: 10rpx;
            line-height: 1.4;
          }

          .template-stats {
            display: flex;
            align-items: center;
            gap:20rpx;

            .stat-item {
              display: flex;
              align-items: center;
              gap: 8rpx;

              .like-icon {
                width: 24rpx;
                height: 24rpx;

                &.liked {
                  opacity: 1;
                }
              }

              .stat-text {
                font-size:20rpx;
                color: rgba(5, 32, 49, 0.5);
              }
            }

            .share-btn {
              margin-left: auto;
              padding: 8rpx;

              .share-icon {
                width: 24rpx;
                height: 24rpx;
                opacity: 0.5;
              }
            }
          }
        }
      }
    }

    // 加载状态
    .loading-container {
      display: flex;
      justify-content: center;
      padding: 40rpx 0;

      .loading-text {
        font-size: 28rpx;
        color: rgba(5, 32, 49, 0.5);
      }
    }

    // 没有更多数据
    .no-more {
      display: flex;
      justify-content: center;
      padding: 40rpx 0;
      font-size: 28rpx;
      color: rgba(5, 32, 49, 0.5);
    }

    // 空状态
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 120rpx 0;

      .empty-icon {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 24rpx;
        opacity: 0.3;
      }

      .empty-text {
        font-size: 28rpx;
        color: rgba(5, 32, 49, 0.5);
      }
    }
  }
