page {
  background-color: #EBFBFC;
}

.home {
  padding: 40rpx 24rpx 280rpx;

  .search {
    position: relative;
    z-index: 20;
    display: flex;
    align-items: center;
    width: 100%;
    height: 76rpx;
    padding: 14rpx 16rpx;
    background: #FFFFFF;
    box-sizing: border-box;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    margin-bottom: 24rpx;

    &-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
    }

    &-input {
      flex: 1;
    }
  }

  .banner {
    width: 100%;
    height: 400rpx;
    margin-bottom: 40rpx;

    &-img,
    &-swiper {
      width: 100%;
      height: 400rpx;
    }
  }

  .icons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;
    position: relative;

    &-item {
      text-align: center;
      font-size: 24rpx;
      color: #052031;
      position: relative;
    }

    &-img {
      width: 116rpx;
      height: 116rpx;
    }
    &-hot{
      width: 64rpx;
      height: 50rpx;
      position: absolute;
      top: -15rpx;
      right: -30rpx;
    }
  }

  .center {
    width: 100%;
    height: 194rpx;
    margin-bottom: 44rpx;
  }

  .menu {
    font-size: 32rpx;
    color: #052031;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    padding: 0 16rpx;
    margin-bottom: 26rpx;

    &-item {
      &.active {
        position: relative;
        font-weight: bold;
        font-size: 36rpx;
        color: #052031;

        &::before {
          position: absolute;
          z-index: -1;
          left: 0;
          bottom: 8rpx;
          content: '';
          width: 72rpx;
          height: 10rpx;
          background: linear-gradient(180deg, #00B2FF 0%, #2FD2F8 100%);
          border-radius: 20rpx 20rpx 20rpx 20rpx;
        }
      }
    }
  }
}