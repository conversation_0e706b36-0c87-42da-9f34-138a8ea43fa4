const app = getApp();
const util = require("@/utils/util.js");
import { loginByOpenidApi } from "@/api/user";
import { querySiteListApi } from "@/api/site";
import { getCommonConfigApi } from "@/api/common";
import { getBannerApi } from "@/api/banner";
import { querySiteCommonTemplate } from "@/api/retrieval";
const { productListApi } = require("@/api/product");
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "酷拍侠",
    },
    bannerList: [],
    tabs: [
      {
        label: "私人定制",
        icon: "/images/index/tabs-0.png",
        feature: "/pages/yinge/search/index",
        hot_icon: "",
      },
      {
        label: "云导游",
        icon: "/images/index/tabs-1.png",
        feature: "/pages/mywebview/index",
        hot_icon: "",
      },
      {
        label: "场馆画面",
        icon: "/images/index/tabs-4.png",
        feature: "/play-live/site-live/index",
        hot_icon: "/images/index/hot.png",
      },
      {
        label: "获取视频",
        icon: "/images/index/tabs-2.png",
        feature: "/pages/retrieval/video/index",
        hot_icon: "",
      },
      {
        label: "剪同款",
        icon: "/images/index/tabs-3.png",
        feature: "/pages/retrieval/template-home/index",
        hot_icon: "",
      },
    ],
    menus: [
      {
        id: 1,
        label: "景区",
      },
      {
        id: 4,
        label: "运动",
      },
      {
        id: 5,
        label: "娱乐",
      },
      {
        id: 2,
        label: "定制",
      },
      {
        id: 6,
        label: "模板",
      },
    ],
    videoList: [],
    scenicList: [],
    // 运动场馆
    sport: [],
    // 台球馆
    template: [],
    // 儿童乐园
    amusement: [],
    customList: [],
    activeId: 1,
  },

  handleMenu({
    currentTarget: {
      dataset: { id },
    },
  }) {
    this.setData({
      activeId: id,
    });

    // 如果切换到定制tab且未加载过数据，则懒加载
    if (id == 2 && this.data.customList.length === 0) {
      this.getCustomList();
    }

    // 如果切换到模板tab且未加载过数据，则懒加载
    if (id == 6 && this.data.template.length === 0) {
      this.getTemplateList();
    }
  },
  gotoBusiness: function () {
    wx.navigateTo({
      url: "/pages/business/index",
      //url: "/store/pages/store/index",
    });
  },
  gotoSearch: function () {
    wx.navigateTo({
      url: "/pages/search/index",
    });
  },
  onLoad: function (options) {
    let that = this;
    console.log("query", options);
    that.data.query = options;

    this.getOpenId();
    this.getSiteList();
    this.getCommonConfig();
    this.getTemplateList();
  },
  onShareAppMessage: function () {
    return {
      title: app.globalData.share_config.title,
      imageUrl: app.globalData.share_config.imageUrl,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  gotoFeature: function (e) {
    let feature = e.currentTarget.dataset.feature;
    if (!feature) {
      util.showToast("敬请期待");
      return;
    }
    wx.navigateTo({
      url: e.currentTarget.dataset.feature,
    });
  },
  gotoCustom: function (e) {
    console.log("e", e);
    let id = e.detail.id;
    console.log("idx", id);
    wx.navigateTo({
      url: `/pages/yinge/goods-detail/index?id=${id}`,
    });
  },
  getSiteList: function () {
    let that = this;
    querySiteListApi({
      hot: 1,
      pageNo: 1,
      pageSize: 100,
    }).then((res) => {
      console.log("res", res);
      res.data.list.forEach((item) => {
        item.live = false;
      });
      // 根据 categoryId 分别将数据赋值给 scenicList、sport、table、amusement
      that.setData({
        scenicList: res.data.list.filter((item) => item.categoryId == 3),
        sport: res.data.list.filter((item) => item.categoryId == 4),
        table: res.data.list.filter((item) => item.categoryId == 6),
        amusement: res.data.list.filter((item) => item.categoryId == 5),
      });
    });
  },

  // 获取定制商品列表
  getCustomList: function () {
    let that = this;

    productListApi({
      page: 1,
      pageSize: 20, // 可以根据需要调整页面大小
    })
      .then((res) => {
        console.log("getCustomList", res);

        if (res.data && res.data.list) {
          let list = res.data.list;
          // 处理商品数据
          list.forEach((item) => {
            // 价格转元，保留两位小数
            if (item.price) {
              item.price = (item.price / 100).toFixed(2);
            }
            // 确保有图片字段
            if (item.picUrl && !item.img) {
              item.img = item.picUrl;
            }
            // 确保有描述字段
            if (item.introduction && !item.desc) {
              item.desc = item.introduction;
            }
          });

          that.setData({
            customList: list,
          });
        } else {
          // 数据格式异常处理
          that.setData({
            customList: [],
          });
        }
      })
      .catch((error) => {
        console.error("获取定制商品列表失败", error);
        wx.showToast({
          title: "获取商品列表失败",
          icon: "none",
        });
      });
  },

  // 获取模板列表
  getTemplateList: function () {
    let that = this;

    querySiteCommonTemplate({
      pageNo: 1,
      pageSize: 10,
      materialCount: [0, 99],
      templateTime: [0, 99],
    })
      .then((res) => {
        console.log("getTemplateList", res);

        if (res.data && res.data.list) {
          let list = res.data.list;
          // 处理模板数据，确保字段匹配组件需要的格式
          list.forEach((item) => {
            // 确保有必要的字段
            if (!item.coverImage && item.coverUrl) {
              item.coverImage = item.coverUrl;
            }
            if (!item.likeCount) {
              item.likeCount = 0;
            }
            if (!item.useCount) {
              item.useCount = 0;
            }
            if (!item.isLiked) {
              item.isLiked = false;
            }
          });

          that.setData({
            template: list,
          });
        } else {
          // 数据格式异常处理
          that.setData({
            template: [],
          });
        }
      })
      .catch((error) => {
        console.error("获取模板列表失败", error);
        wx.showToast({
          title: "获取模板列表失败",
          icon: "none",
        });
      });
  },

  getCommonConfig: function () {
    var bannerList = [];
    {
      getBannerApi({
        scene: "kpx_index",
      }).then(({ data }) => {
        console.log("getBannerApi", data);
        bannerList = data;
        that.setData({
          bannerList: bannerList,
        });
      });
    }

    let that = this;
    getCommonConfigApi({
      scene: "index_video",
      platform: "kupaixia",
    }).then((res) => {
      let videoListJson = res.data.jsonData;
      let videoList = JSON.parse(videoListJson);
      that.setData({
        videoList: videoList.index_video,
      });
    });
  },
  getOpenId: function () {
    let that = this;
    let appId = app.globalData.appId;
    let scene = app.globalData.user.scene;
    console.log("appId", appId);
    wx.login({
      success: (res) => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        // 拿到code就调用后端的登录接口，微信服务器就会返回openid、session_key等信息
        // 注意：要拿openid除了code还有三个参数：appId、secret、grant_type
        //（appid和secret在微信开发者后台可以获取）由于我们后端已经对这块做了处理，只需要传code即可
        loginByOpenidApi({
          loginCode: res.code,
          state: util.geneUuid(),
          appId: appId,
          scene: scene,
          parentCode: that.data.query.pCode,
        }).then((data) => {
          console.log("data", data);

          if (data.data == null) {
            console.log("未注册");
            app.globalData.userInfo.userId = 0;
            app.globalData.userInfo.userCode = 0;
            return;
          }
          app.globalData.userInfo.userId = data.data.userId;
          app.globalData.userInfo.userCode = data.data.userId;
          app.globalData.userInfo.openId = data.data.openid;
          app.globalData.userInfo.avatar =
            "https://k-cos.ai-market.top/wx/base/default_avatar.png?v=0716";
          if (!app.globalData.userInfo.username) {
            app.globalData.userInfo.username = "微信用户";
          }
          that.data.ready = true;
          app.globalData.user = {
            token: data.data.accessToken,
            openId: app.globalData.userInfo.openId,
            userCode: app.globalData.userInfo.userCode,
          };
          console.log("token", data.data.accessToken);
          wx.setStorageSync("token", data.data.accessToken);
          wx.setStorageSync("user", app.globalData.userInfo);

          let feature = that.data.query.feature;
          console.log("feature", feature);
          if (feature != null && feature != undefined && feature.length > 0) {
            let feature_path = that.data.feature_path[feature].split("|");
            console.log("feature_path", that.data.feature_path[feature]);
            if (feature_path.length > 1) {
              wx.navigateTo({
                url: feature_path[0],
              });
            } else {
              wx.switchTab({
                url: feature_path[0],
              });
            }
          }
        });
      },
    });
  },

  gotoFunc(e) {
    var locate = e.currentTarget.dataset.locate;

    var open_type = e.currentTarget.dataset.type;
    console.log("locate", locate, open_type);
    var targetApp = e.currentTarget.dataset.appId;
    if (locate == "") {
      util.showToast("全力开发中，尽请期待");
      return;
    }
    if (open_type == "switchTab") {
      console.log("open_type" + open_type);
      wx.switchTab({
        url: locate,
      });
      return;
    }
    if (open_type == "navigateTo") {
      wx.navigateTo({
        url: locate,
      });
      return;
    }
    console.log("test", targetApp);
    if (open_type == "navigateToMiniProgram") {
      wx.navigateToMiniProgram({
        appId: targetApp,
        path: locate,
        success(res) {
          console.log("打开成功", res);
        },
      });
      return;
    }

    console.log("open_type" + open_type);
    wx.redirectTo({
      url: locate,
    });
  },
});
