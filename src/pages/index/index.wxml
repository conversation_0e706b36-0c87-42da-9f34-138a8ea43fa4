<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="home" style="margin-top: {{ paddingHeight }}px">
  <view class="search">
    <image class="search-icon" src="/images/index/search.png" bind:tap="gotoSearch" />
    <input class="search-input" placeholder="搜索定制产品、场馆名称等"  bindfocus="gotoSearch"/>
  </view>
  <view class="banner">
    <swiper
      indicator-dots
      autoplay
      indicator-active-color="#ffffff"
      indicator-color="rgba(255,255,255,.3)"
      circular
      class="banner-swiper"
    >
      <swiper-item wx:for="{{ bannerList }}" wx:key="id" bindtap="gotoFunc" data-locate="{{ item.link }}" data-type="{{item.openType}}" data-app="{{item.appId}}"  >
        <image class="banner-img" src="{{ item.imageUrl }}" />
      </swiper-item>
    </swiper>
  </view>
  <view class="icons">
    <view class="icons-item" wx:for="{{ tabs }}" wx:key="index" data-feature="{{ item.feature }}" bind:tap="gotoFeature">
      <image class="icons-img" src="{{ item.icon }}" />
      <view class="icons-label">{{ item.label }}</view>
          <image class="icons-hot" src="{{ item.hot_icon }}"  wx:if="{{item.hot_icon != ''}}" />
    </view>

  </view>
  <image class="center" src="https://k-cos.ai-market.top/wx/banner/center.png"  bind:tap="gotoBusiness" />
  <view class="menu">
    <view
      class="menu-item {{ item.id === activeId ? 'active' : '' }}"
      wx:for="{{ menus }}"
      wx:key="id"
      data-id="{{ item.id }}"
      bind:tap="handleMenu"
    >
      {{ item.label }}
    </view>
  </view>

  <com-video wx:if="{{ activeId === 1 }}" list="{{ videoList }}" />
  <com-scenic wx:elif="{{ activeId === 3 }}" list="{{ scenicList }}" />
  <com-templateLibrary wx:elif="{{ activeId === 6 }}" list="{{ template }}" />
  <com-scenic wx:elif="{{ activeId === 4 }}" list="{{ sport }}" />
  <com-scenic wx:elif="{{ activeId === 5 }}" list="{{ amusement }}" />
    <com-custom wx:elif="{{ activeId === 2 }}" list="{{ customList }}"/>
</view>

<TabBar activeIndex="{{ 0 }}" />
