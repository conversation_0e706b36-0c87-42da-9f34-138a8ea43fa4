page {
  background-color: #EBFBFC;
  padding-bottom: 268rpx;
}

.wrap {
  padding: 40rpx 24rpx;
  letter-spacing: 2rpx;
  font-size: 0;

  .content {
    position: relative;
  }

  .title {
    font-weight: 800;
    font-size: 32rpx;
    color: #FFFFFF;
    background: #052031;
    border-radius: 0rpx 0rpx 0rpx 40rpx;
    width: 304rpx;
    height: 62rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40rpx 0 16rpx;
  }

  .text {
    position: relative;
    font-size: 28rpx;
    color: #052031;
    line-height: 60rpx;
    text-indent: 1em;

    &::before {
      content: '';
      position: absolute;
      width: 10rpx;
      height: 10rpx;
      left: 8rpx;
      top: 26rpx;
      background-color: #052031;
      border-radius: 10rpx;
    }
  }

  &-header {
    width: 100%;
    height: 260rpx;
  }

  &-text {
    padding: 32rpx 40rpx 108rpx;
    font-size: 32rpx;
    color: #052031;
    line-height: 60rpx;
    background-color: #ffffff;
    border-radius: 0rpx 0rpx 32rpx 200rpx;
  }

  &-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 286rpx;
  }
}

.footer {

  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 24rpx 52rpx 20rpx 44rpx;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  bottom: 50rpx;
  width: 100%;
  height: 268rpx;
  margin: auto;


  &-left {
    font-size: 32rpx;
    color: #052031;
    line-height: 72rpx;
  }

  &-blod {
    font-weight: 500;
    font-size: 40rpx;
  }

  &-right {
    text-align: center;
    font-size: 20rpx;
  }

  &-qr {
    width: 164rpx;
    height: 164rpx;
  }
}