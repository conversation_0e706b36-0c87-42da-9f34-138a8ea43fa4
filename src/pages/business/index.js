const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "商务合作",
    },
    phone: "***********",
    email: "<EMAIL>",
    qr: "https://k-cos.ai-market.top/wx/business/qr.png"
  },

  onLoad() {
    
  },

  callPhone(e) {
    const phone = this.data.phone;
    wx.makePhoneCall({
      phoneNumber: phone
    });
  },
  copyEmail(e) {
    const email = this.data.email;
    wx.setClipboardData({
      data: email,
      success() {
        wx.showToast({
          title: '邮箱已复制，可粘贴到邮箱客户端',
          icon: 'none'
        });
      }
    });
  },
  previewImage(e) {
    const image = this.data.qr;
    wx.previewImage({
      urls: [image]
    });
  }
});
