const util = require("@/utils/util.js");
import { loginByphoneApi } from "@/api/user";
const app = getApp();
Page({
  data: {
    navData: {
      title: "登陆",
    },
    query: {
      pCode: "",
      feature: "",
      source: "",
    },
    ready: false,
    feature_path: {
      extraction: "/pages/extraction/index",
      watermark: "/pages/watermark/index",
      textGen: "/pages/text-generator/index/index",
      gpt: "/pages/menu/index",
      dd: "/pages/text-generator/stream-result/index|navigateTo",
      ocr: "/pages/ocr/index|navigateTo",
      dubbing: "/pages/dubbing/index",
    },
    check: false,
    bannerList: [],
    funs: [],
  },
  onShow: function (options) {
    // 页面出现在前台时执行
    let that = this;
    let is_reday = that.data.ready;
    console.log("login onShow", is_reday);
    if (is_reday) {
      wx.switchTab({
        url: "/pages/index/index",
      });
    }
  },
  onReady: function (options) {
    // 页面出现在前台时执行
    console.log("login onReady", options);
    let that = this;
  },
  getIndexData: function () {
    let that = this;
  },
  onLoad: function (options) {
    let that = this;
    console.log("query", options);
    that.data.query = options;
    this.getIndexData();
    this.getOpenId(1);
  },
  getOpenId: function (tag) {
    console.log("tag", tag);
  },
  handleToastCheck() {
    const { check } = this.data;
    if (!check) {
      wx.showToast({
        title: "请勾选并阅读协议",
        icon: "none",
        duration: 3000,
      });
      return false;
    }
  },
  handleCheck() {
    this.setData({
      check: !this.data.check,
    });
  },
  jumpPrivacy() {
    wx.navigateTo({
      url: "/pages/privacy/index",
    });
  },

  getPhoneNumber(e) {
    let that = this;
    console.log(e.detail.code); // 动态令牌
    console.log(e.detail.errMsg); // 回调信息（成功失败都会返回）
    console.log(e.detail.errno); // 错误码（失败时返回）

    wx.login({
      success: (res) => {
        loginByphoneApi({
          loginCode: res.code,
          phoneCode: e.detail.code,
          state: util.geneUuid(),
        }).then((data) => {
          console.log("data", data);

          app.globalData.userInfo.userId = data.data.userId;
          app.globalData.userInfo.userCode = data.data.userId;
          app.globalData.userInfo.openId = data.data.openid;
          app.globalData.userInfo.avatar =
            "https://k-cos.ai-market.top/wx/base/default_avatar.png?v=0716";
          if (!app.globalData.userInfo.username) {
            app.globalData.userInfo.username = "微信用户";
          }
          that.data.ready = true;
          app.globalData.user = {
            token: data.data.accessToken,
            openId: app.globalData.userInfo.openId,
            userCode: app.globalData.userInfo.userCode,
          };
          console.log("token", data.data.accessToken);
          wx.setStorageSync("token", data.data.accessToken);
          wx.setStorageSync("user", app.globalData.userInfo);
          wx.navigateBack();
        });
      },
    });
  },
});
