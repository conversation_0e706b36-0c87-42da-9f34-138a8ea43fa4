<PageHeader />
<NavBar nav-data="{{ navData }}"/>




  <view class="login">

    <image class="login_logo" src="https://k-cos.ai-market.top/wx/base/logo-launch.png" ></image>

    <view class="login_main">
      <view class="login_tips">您还未登录，请先登录</view>

      <!-- -->
      <view class="login_box" catchtap="handleToastCheck">
       <view class="login_btn">授权手机</view>
        <block wx:if="{{check}}">
          <button  class="login_btn_display" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">授权手机
          </button>
        </block>
      </view>
    </view>


    <view class="login_ft">
      <view bindtap="handleCheck" class="login_label">
        <image wx:if="{{check}}" class="login_check" src="/images/video/checked.png" ></image>
        <image wx:else class="login_check" src="/images/video/unchecked.png" ></image>

        <view class="login_value">已阅读并同意
          <text
              id="login_to_agreement"
              data-report_name="登录_会员注册服务协议"
              data-report_type="2"
              catchtap="jumpPrivacy"
              class="login_text">《会员注册服务协议》
          </text>及<text
              id="login_to_privacy"
              data-report_name="登录_隐私条款"
              data-report_type="2"
              catchtap="jumpPrivacy"
              class="login_text">《隐私条款》
          </text>
        </view>
      </view>

    </view>
  </view>

