page {
  background-color: #FFFFFF;
}

.index {
  padding: 20rpx 30rpx;

  .logo{
    position: absolute;
    width: 320rpx;
    height: 106rpx;
    bottom: 240rpx;
    left: 0;
    right: 0;
    margin: auto;
  }


}

.nav_main {
  display: flex;
  align-items: center;
}

.icon_left {
  width: 10rpx;
  height: 17rpx;
}

.title {
  margin-left: 14rpx;

  font-size: 17rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 24rpx;
}


.login {
  // padding-bottom: calc(30px + env(safe-area-inset-bottom));
  min-height: 100rpx;
  background-color: #fff;

  box-sizing: border-box;
  overflow: hidden;


  &_logo {
    margin: 220rpx auto 0;
    width: 250rpx;
    height: 250rpx;
    display: block;
  }

  &_main {
    margin-top: 105rpx;
    padding: 0 38rpx;
    text-align: center;
  }

  &_tips {
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #666666;
    line-height: 40rpx;
  }


  &_box {
    position: relative;
  }

  &_btn {
    margin-top: 30rpx;
    padding: 25rpx 0;
    background-color: #000;
    color: #fff;

    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    line-height: 32rpx;
  }


  &_ft {
    margin-top: 156rpx;
    padding: 0 38rpx;
  }

  &_label {
    display: flex;
    align-items: center;
  }

  &_check {
    width: 40rpx;
    height: 40rpx;

  }

  &_value {
    margin-left: 6rpx;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 18rpx;
    display: flex;
  }

  &_text {
    color: #000;
  }
}


.login_btn_display {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  opacity: 0;
  z-index: 1;
  font-size: 0;
}

