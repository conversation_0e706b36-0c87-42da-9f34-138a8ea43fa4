import {
  pageAdminSiteDevicesList,
  updateDevice,
  flowsData,
  transactionData,
} from "@/api/merchant";

const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    // 导航数据
    navData: {
      title: "",
    },
    // 当前场馆ID
    siteId: 0,
    // 当前场馆名称
    siteName: "",
    // 当前场馆code
    siteCode: "",
    // 设备列表
    deviceList: [],
    // 设备列表 数据分析 TAB切换索引
    currentTab: 0,
    // 数据分析中的 副标题Tab索引
    activeSubtitle: 0,
    // 数值1
    value1: 0,
    // 数值2
    value2: 0,
    // 当前时间
    currentTime: "",
    visible: false,
    showLineChart: false,
    value: [
      new Date(new Date().setDate(new Date().getDate() - 5)).getTime(),
      new Date().getTime(),
    ],
    valueStr: ["", ""],
    minDate: new Date(
      new Date().setFullYear(new Date().getFullYear() - 1),
    ).getTime(),
    maxDate: new Date().getTime(),
    datasets: [
      {
        data: [200, 900, 356, 278, 158],
        color: "#15da73",
      },
    ],
    labels: ["2月", "3月", "4月", "5月", "6月"],
  },
  onLoad: function (options) {
    // 摄像头列表加载
    this.devices_init(options);
    // 添加初始化时间
    this.updateCurrentTime();
    // 初始化 valueStr
    this.updateValueStr();
    // 初始化数据
    this.updateData();
  },
  onUnload: function () {},
  // 将时间戳格式化为 YYYY/MM/DD 格式
  formatDate: function (timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
  },
  // 更新 valueStr
  updateValueStr: function () {
    const { value } = this.data;
    this.setData({
      valueStr: [this.formatDate(value[0]), this.formatDate(value[1])],
    });
  },
  handleCalendar: function () {
    this.setData({ visible: true });
  },
  handleConfirm: function (e) {
    const { value } = e.detail;
    this.setData({ value }, () => {
      // value 更新后再更新 valueStr
      this.updateValueStr();
      this.updateData();
    });
  },
  // 更新当前时间
  updateCurrentTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");
    const hour = now.getHours().toString().padStart(2, "0");
    const minute = now.getMinutes().toString().padStart(2, "0");
    this.setData({
      currentTime: `${year}/${month}/${day} ${hour}:${minute}`,
    });
  },
  // 摄像头列表加载
  devices_init: function (options) {
    // 从页面参数中获取 siteId
    const siteId = options.siteId;
    const siteName = decodeURIComponent(options.siteName || "");
    const siteCode = decodeURIComponent(options.siteCode || "");
    this.setData({
      siteId: siteId,
      siteCode: siteCode,
      siteName: siteName,
      "navData.title": siteName, // 将场馆名称设置为导航标题
    });
    // 使用 siteId 加载相关数据
    this.loadSiteDetails(siteId);
  },
  // 设备列表数据加载
  loadSiteDetails: function (siteId) {
    pageAdminSiteDevicesList({ pageNo: 1, pageSize: 100, siteId: siteId }).then(
      (res) => {
        if (res.code === 0) {
          this.setData({
            deviceList: res.data.list || [],
          });
        } else {
          wx.showToast({
            title: "获取场馆设备列表失败",
            icon: "none",
          });
        }
      },
    );
  },
  // 开启关闭摄像头
  turnOnOffTheCamera: function (e) {
    const item = e.currentTarget.dataset.item;
    updateDevice({ id: item.devId, clientShow: item.clientShow === 1 ? 0 : 1 })
      .then((res) => {
        if (res.code === 0) {
          // 显示成功提示
          wx.showToast({
            title: item.clientShow === 1 ? "已关闭直播" : "已开启直播",
            icon: "success",
          });
          // 刷新数据列表
          this.loadSiteDetails(this.data.siteId);
        } else {
          wx.showToast({
            title: "更新设备状态失败",
            icon: "none",
          });
        }
      })
      .catch((err) => {
        wx.showToast({
          title: "更新设备状态异常",
          icon: "none",
        });
      });
  },
  // 处理swiper滑动
  swiperChange: function (e) {
    this.setData({
      visible: false, // 关闭日期选择器
      currentTab: e.detail.current,
    });
    // if (e.detail.current === 1) {
    //     // 先隐藏图表，然后重新显示以触发重绘
    //     this.setData({
    //         showLineChart: false
    //     });
    //
    //     setTimeout(() => {
    //         this.setData({
    //             showLineChart: true
    //         });
    //     }, 100);
    // }
  },
  // 处理底部标签点击
  switchTab: function (e) {
    const index = e.currentTarget.dataset.index;
    // 如果点击的标签已经是激活状态，则不执行操作
    if (parseInt(index) === parseInt(this.data.currentTab)) {
      return;
    }
    this.setData({
      currentTab: index,
    });
  },
  // 更新数据 - 主方法
  updateData: function () {
    this.setData({ showLineChart: false });
    const parameter = {
      siteCode: this.data.siteCode,
      startDate: this.data.valueStr[0],
      endDate: this.data.valueStr[1],
    };
    // 0为流量数据，1为交易数据
    const apiRequest =
      this.data.activeSubtitle === 0
        ? flowsData(parameter)
        : transactionData(parameter);
    apiRequest.then((res) => {
      const { code, data } = res;
      const { total, today, dayList } = data;

      // 处理图表数据
      const chartResult = this.processChartData(dayList);

      // 处理数值显示
      const [value1, value2] = this.processValues(total, today);

      // 更新界面
      this.setData({
        value1,
        value2,
        datasets: [
          {
            data: chartResult.data,
            color: "#15da73",
          },
        ],
        labels: chartResult.labels,
        showLineChart: true,
      });
    });
  },

  // 处理图表数据
  processChartData: function (dayList) {
    // 计算开始日期和结束日期
    const startDate = new Date(this.data.valueStr[0]);
    const endDate = new Date(this.data.valueStr[1]);

    // 计算日期差异（天数）
    const diffTime = Math.abs(endDate - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // 判断是按天还是按月显示
    const displayByMonth = diffDays > 5;

    // 根据展示类型处理数据
    if (!displayByMonth) {
      return this.formatDailyChartData(dayList, startDate, endDate, diffDays);
    } else {
      return this.formatMonthlyChartData(dayList, startDate, endDate);
    }
  },

  // 处理按天显示的图表数据
  formatDailyChartData: function (dayList, startDate, endDate, diffDays) {
    const chartData = [];
    const chartLabels = [];

    // 按天显示，确保至少显示5天数据
    const daysToShow = Math.max(diffDays + 1, 5);

    // 根据结束日期计算调整后的开始日期
    let adjustedStartDate = new Date(endDate);
    adjustedStartDate.setDate(adjustedStartDate.getDate() - (daysToShow - 1));

    // 星期几的中文表示
    const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];

    // 为每一天创建数据点
    for (let i = 0; i < daysToShow; i++) {
      const date = new Date(adjustedStartDate);
      date.setDate(date.getDate() + i);

      // 获取星期几
      const weekDay = weekDays[date.getDay()];

      // 格式化日期为"周X MM/DD"格式
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const dateLabel = `${weekDay}${month}/${day}`;
      chartLabels.push(dateLabel);

      // 判断是否在有效数据范围内
      const inRange = date >= startDate && date <= endDate;

      if (inRange && dayList) {
        const currentTimestamp = date.setHours(0, 0, 0, 0);
        const dataPoint = dayList.find((item) => {
          const itemDate = new Date(item.date);
          return itemDate.setHours(0, 0, 0, 0) === currentTimestamp;
        });
        chartData.push(dataPoint ? dataPoint.amount : 0);
      } else {
        // 范围外的日期设为0
        chartData.push(0);
      }
    }

    return { data: chartData, labels: chartLabels };
  },

  // 处理按月显示的图表数据
  formatMonthlyChartData: function (dayList, startDate, endDate) {
    const chartData = [];
    const chartLabels = [];

    // 找出开始和结束的年月
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth();
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth();

    // 计算月份差
    const monthDiff = (endYear - startYear) * 12 + (endMonth - startMonth) + 1;
    const monthsToShow = Math.max(monthDiff, 12);

    // 确定调整后的开始年月（确保总共12个月）
    let adjustedStartYear = endYear;
    let adjustedStartMonth = endMonth - (monthsToShow - 1);

    // 处理月份为负的情况
    if (adjustedStartMonth < 0) {
      adjustedStartYear -= Math.ceil(Math.abs(adjustedStartMonth) / 12);
      adjustedStartMonth = ((adjustedStartMonth % 12) + 12) % 12;
    }

    // 为每个月创建数据点
    for (let i = 0; i < monthsToShow; i++) {
      const currentYear =
        adjustedStartYear + Math.floor((adjustedStartMonth + i) / 12);
      const currentMonth = (adjustedStartMonth + i) % 12;

      // 创建当前月的第一天和最后一天
      const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
      const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);

      // 格式化月份标签
      const monthLabel = `${(currentMonth + 1).toString().padStart(2, "0")}月`;
      chartLabels.push(monthLabel);

      // 判断是否在有效数据范围内
      const inRange = !(
        lastDayOfMonth < startDate || firstDayOfMonth > endDate
      );

      if (inRange && dayList) {
        // 汇总该月所有天的数据
        let monthTotal = 0;
        dayList.forEach((item) => {
          const itemDate = new Date(item.date);
          if (
            itemDate.getFullYear() === currentYear &&
            itemDate.getMonth() === currentMonth
          ) {
            monthTotal += item.amount;
          }
        });
        chartData.push(monthTotal);
      } else {
        // 范围外的月份设为0
        chartData.push(0);
      }
    }

    return { data: chartData, labels: chartLabels };
  },

  // 处理展示的数值
  processValues: function (total, today) {
    let value1 = total ? total : 0;
    let value2 = today ? today : 0;

    // 如果是交易数据，则除以100并保留两位小数
    if (this.data.activeSubtitle === 1) {
      value1 = parseFloat((value1 / 100).toFixed(2));
      value2 = parseFloat((value2 / 100).toFixed(2));
    }

    return [value1, value2];
  },
  // 切换副标题
  switchSubtitle: function (e) {
    const index = parseInt(e.currentTarget.dataset.index);
    // 如果点击的标签已经是激活状态，则不执行操作
    if (index === this.data.activeSubtitle) {
      return;
    }
    this.setData({
      activeSubtitle: index,
    });
    this.updateData();
    // 可以在这里添加切换对应内容的逻辑
  },
});
