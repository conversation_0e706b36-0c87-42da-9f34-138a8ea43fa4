<PageHeader/>
<NavBar navData="{{ navData }}"/>
<!-- 内容区域 - 使用swiper组件实现左右切换 -->
<swiper class="content-swiper" current="{{currentTab}}" bindchange="swiperChange">
    <swiper-item>
        <!-- 第一个tab的内容 -->
        <view class="tab-content">
            <view class="item" wx:for="{{deviceList}}" wx:key="id">
                <view class="left {{item.clientShow === 0 ? 'left-offline' : ''}}">
                    <image src="/images/store/base-venue-cover.png"></image>
                    <view class="status {{item.clientShow === 1 ? '' : 'status-offline'}}">
                        {{item.clientShow === 1 ? '直播中' : '休息中'}}
                    </view>
                </view>
                <view class="right">
                    <view class="name">
                        <text>{{item.deviceName}}</text>
                        <view></view>
                    </view>
                    <view class="button">
                        <view></view>
                        <text class="{{item.clientShow === 1 ? '' : 'button-online'}}" bindtap="turnOnOffTheCamera"
                              data-item="{{item}}">{{item.clientShow === 1 ? '关闭' : '开启'}}</text>
                    </view>
                </view>
            </view>
        </view>
    </swiper-item>

    <swiper-item>
        <!-- 第二个tab的内容 -->
        <view class="tab-content" style="padding: 0">
            <view class="subtitle">
                <view class="subtitle-item {{activeSubtitle === 0 ? 'active' : ''}}" bindtap="switchSubtitle"
                      data-index="0">
                    <text>流量</text>
                    <view class="underline" wx:if="{{activeSubtitle === 0}}"></view>
                </view>
                <view class="subtitle-item {{activeSubtitle === 1 ? 'active' : ''}}" bindtap="switchSubtitle"
                      data-index="1">
                    <text>交易</text>
                    <view class="underline" wx:if="{{activeSubtitle === 1}}"></view>
                </view>
            </view>
            <!-- 在subtitle下方添加日期筛选组件 -->
            <view class="date-filter">
                <t-calendar
                        class="custom-calendar"
                        visible="{{visible}}"
                        value="{{value}}"
                        minDate="{{minDate}}"
                        maxDate="{{maxDate}}"
                        type="range"
                        bind:confirm="handleConfirm"
                />
                <view class="wrapper" bind:tap="handleCalendar">
                    <view>{{valueStr[0]}}</view>
                    <view>-</view>
                    <view>{{valueStr[1]}}</view>
                </view>
            </view>
            <view class="splitBackground"></view>
            <view class="secondContent">
                <!-- 根据activeSubtitle动态显示标题 -->
                <view class="title">
                    <block wx:if="{{activeSubtitle === 0}}">流量数据</block>
                    <block wx:else>交易数据</block>
                </view>
                <!--                <view class="time">数据时间 {{currentTime}}</view>-->
                <view class="numCount">
                    <view class="numCountLeft">
                        <!-- 根据activeSubtitle动态显示左侧指标 -->
                        <view class="numCountTop">
                            <block wx:if="{{activeSubtitle === 0}}">累计用户数</block>
                            <block wx:else>累计交易量</block>
                        </view>
                        <!-- 可以根据需要显示不同的数据值 -->
                        <view class="numCountBottom">
                            <block wx:if="{{activeSubtitle === 0}}">{{value1}}</block>
                            <block wx:else>{{value1}}元</block>
                        </view>
                    </view>
                    <view class="numCountRight">
                        <!-- 根据activeSubtitle动态显示右侧指标 -->
                        <view class="numCountTop">
                            <block wx:if="{{activeSubtitle === 0}}">今日访问人数</block>
                            <block wx:else>今日交易量</block>
                        </view>
                        <!-- 可以根据需要显示不同的数据值 -->
                        <view class="numCountBottom">
                            <block wx:if="{{activeSubtitle === 0}}">{{value2}}</block>
                            <block wx:else>{{value2}}元</block>
                        </view>
                    </view>
                </view>
            </view>
            <view class="splitBackground"></view>
            <view class="lineGraph">
                <view class="lineGraphTitle">交易量</view>
                <view class="bottom-wrapper">
                    <view>{{valueStr[0]}}</view>
                    <view>-</view>
                    <view>{{valueStr[1]}}</view>
                </view>
                <view class="line-chart-box" wx:if="{{showLineChart}}">
                    <!-- 页面 WXML -->
                    <line-chart
                            id="myChart"
                            datasets="{{datasets}}"
                            labels="{{labels}}"
                    />
                </view>

            </view>
        </view>
    </swiper-item>
</swiper>

<!-- 吸底导航栏 -->
<view class="bottom-tabbar">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
        <t-icon name="video-camera-3" size="43rpx" data-name="video-camera-3"/>
        <text>设备列表</text>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
        <t-icon name="chart-line-data-1" size="43rpx" data-name="chart-line-data-1"/>
        <text>数据分析</text>
    </view>
</view>
