page {
  height: 100%;
  width: 100%;
  overflow: hidden; // 添加这一行，禁止页面滚动
}

.item {
  width: 100%;
  height: 200rpx;
  background: #FFFFFF;
  box-shadow: 0 8rpx 20rpx 0 rgba(5, 32, 49, 0.05);
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  padding: 36rpx 28rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.left {
  position: relative;
  width: 144rpx;
  height: 144rpx;
  box-sizing: border-box;
  padding: 5rpx;
  border: 5rpx solid #FF4F28;
  border-radius: 50%;
}

.left image {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 50%;

}

.left .status {
  text-align: center;
  line-height: 48rpx;
  position: absolute;
  bottom: -4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 48rpx;
  background: #FF4F28;
  border-radius: 160rpx 160rpx 160rpx 160rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 26rpx;
  color: #FFFFFF;
  font-style: normal;
  text-transform: none;
}

.right {
  width: 464rpx;
  display: flex;
  justify-content: space-between;
}

.right .name {
  display: flex;
  align-items: flex-start;
}

.name text {
  width: 138rpx;
  height: 50rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 36rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.right .button {
  display: flex;
  align-items: flex-end;
}

.button text {
  width: 146rpx;
  height: 64rpx;
  line-height: 64rpx;
  background: #FF4F28;
  border-radius: 100rpx 100rpx 100rpx 100rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.status-offline {
  background-color: #979797 !important;
}

.button-online {
  background-color: #00CD88 !important;
}

.left-offline {
  border: 5rpx solid #979797 !important;
}

.content-swiper {
  height: calc(100vh - 196rpx - 200rpx); /* 减去顶部导航和底部tabbar的高度 */
  margin-top: 200rpx;
}

.tab-content {
  background: #fff;
  height: 100%;
  width: 100%;
  overflow: hidden;
  overflow-y: auto; // 启用垂直滚动
  padding: 0 24rpx;
  box-sizing: border-box;
  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }

  // 兼容性处理
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.placeholder {
  padding: 30rpx;
  text-align: center;
  color: #999;
}

/* 吸底导航栏样式 */
.bottom-tabbar {
  display: flex;
  justify-content: space-between;
  align-items: start;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 196rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999;
}

.tab-item {
  margin-top: 30rpx;
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #052031;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.tab-item text{
  margin-top: 12rpx;
}

.tab-item.active {
  color: #07c160; /* 微信绿色 */
}

.tab-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
  /* 这里可以添加图标的背景图片 */
}

.subtitle {
  padding: 0 24rpx;
}

.subtitle-item {
  text-align: center;
  display: inline-block;
  position: relative;
  width: 104rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #052031;
  font-style: normal;
  text-transform: none;
  margin-right: 44rpx;
}

.subtitle-item.active text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 800;
  font-size: 36rpx;
  color: #052031;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.underline {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 104rpx;
  height: 4rpx;
  background: #00CD88;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}

.splitBackground {
  width: 100%;
  height: 12rpx;
  background: #F7F7F7;
}

.date-filter {
  background-color: #fff;
  padding: 0 24rpx;
  margin-top: 36rpx;
  margin-bottom: 40rpx;
}


.secondContent {
  width: 100%;
  padding: 40rpx 24rpx;
}

.secondContent .title {
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 28rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12rpx;
}

.secondContent .time {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 40rpx;
}


.secondContent .numCount {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.secondContent .numCount .numCountLeft {
  width: 50%;
}

.secondContent .numCount .numCountRight {
  width: 50%;
}

.numCountTop {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 16rpx;
}

.numCountBottom {
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 40rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.charts-box {
  width: 100%;
  height: 300px;
}


.wrapper {
  //background-color: var(--td-bg-color-container);
  width: 300rpx;
  background: #F5F5F5;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  display: flex;
  justify-content: space-between;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #052031;
  font-style: normal;
  text-transform: none;
  padding: 14rpx 16rpx;
}

.lineGraph {
  padding: 40rpx 24rpx;
}

.lineGraphTitle {
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 28rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12rpx;
}

.bottom-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 310rpx;
  line-height: 34rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #052031;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.custom-calendar {
  --td-brand-color: #00CD88;
}


.line-chart-box {
  position: relative;
  width: 100%;
  height: 400rpx;
}





