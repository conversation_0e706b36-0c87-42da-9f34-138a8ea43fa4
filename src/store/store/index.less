page {
  height: 100%;
  width: 100%;
  overflow: hidden; // 添加这一行，禁止页面滚动
}

.container {
  background: #fff;
  height: auto;
  overflow: hidden;
  width: 100%;
  margin-top: 200rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
}

text {
  display: block;
}

.item-box {
  height: 70vh;
  overflow-y: auto; // 启用垂直滚动
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;

  /* 隐藏滚动条 - Webkit 浏览器 (Chrome, Safari) */
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    background: transparent;
  }

  /* 隐藏滚动条 - Firefox */
  scrollbar-width: none;

  /* IE/Edge */
  -ms-overflow-style: none;
}

.container .venue {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 112rpx;
}

.venue .right {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 574rpx;
  position: relative;
}


.desc .title {
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 34rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 8rpx;
}

.desc .userCount {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.venue .baseCover {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  overflow: hidden;
}

.venue .arrow-right {
  width: 48rpx;
  height: 48rpx;
}

.divider {
  position: absolute;
  right: 0;
  bottom: -40rpx;
  width: 100%;
  height: 1rpx;
  background: rgba(5, 32, 49, 0.1);
}

.item {
  margin-bottom: 80rpx;
}

.text1 {
  width: 224rpx;
  height: 40rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 16rpx;
}

.text2 {
  width: 240rpx;
  height: 56rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 40rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 60rpx;
}

.text3 {
  width: 112rpx;
  height: 40rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #052031;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 24rpx;
}
