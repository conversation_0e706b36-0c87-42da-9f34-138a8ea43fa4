<PageHeader/>
<NavBar navData="{{ navData }}"/>
<view class="container">
  <text class="text1">酷拍侠商户工作台</text>
  <text class="text2">选择场馆查看</text>
  <text class="text3">场馆名称</text>
  <!-- 场馆 -->
  <view class="item-box">
    <view wx:if="{{loading}}" class="loading">加载中...</view>
    <view wx:elif="{{siteList.length === 0}}" class="empty">暂无场馆数据</view>
    <block wx:else>
      <view class="item" wx:for="{{siteList}}" wx:key="id" data-site-id="{{item.siteId}}"
            data-site-name="{{item.siteName}}" data-site-code="{{item.siteCode}}" bindtap="onSiteItemClick">
        <view class="venue">
          <image class="baseCover" src="{{item.headImgUrl || '/images/store/base-venue-cover.png'}}"></image>
          <view class="right">
            <view class="divider"></view>
            <view class="desc">
              <text class="title">{{item.siteName}}</text>
              <text class="userCount">累计用户{{item.userCount || '0'}}</text>
            </view>
            <image class="arrow-right" src="/images/store/arrow-right.png"></image>
          </view>
        </view>
      </view>
    </block>
  </view>
</view>
