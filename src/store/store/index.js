import { pageAdminSiteList } from "@/api/merchant";

const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "",
    },
    siteList: [], // 存储场馆列表数据
    loading: true,
  },

  onLoad: function () {
    this.fetchSiteList();
  },
  // 获取场馆列表
  fetchSiteList: function () {
    pageAdminSiteList({ pageNo: 1, pageSize: 100 })
      .then((res) => {
        if (res.code === 0) {
          this.setData({
            siteList: res.data.list,
            // siteList: [],
          });
        } else {
          wx.showToast({
            title: "获取场馆列表失败",
            icon: "none",
          });
        }
        this.setData({
          loading: false,
        });
      })
      .catch((err) => {
        console.log("获取场馆列表失败", err);
        wx.showToast({
          title: "获取场馆列表异常",
          icon: "none",
        });
        this.setData({
          loading: false,
        });
      });
  },
  // 场馆项点击事件
  onSiteItemClick: function (e) {
    const siteId = e.currentTarget.dataset.siteId;
    const siteName = e.currentTarget.dataset.siteName;
    const siteCode = e.currentTarget.dataset.siteCode;
    console.log("siteCode", siteCode);
    // 可以跳转到详情页，同时携带场馆ID和名称
    wx.navigateTo({
      url: `/store/detail/index?siteId=${siteId}&siteName=${encodeURIComponent(siteName)}&siteCode=${encodeURIComponent(siteCode)}`,
    });
  },
});
