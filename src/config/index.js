import dev from "./env-dev";
import prod from "./env-prod";

const ENV = {
  development: dev,
  production: prod,
};

export const isPro = "%ENV%" === "production";

export const version = "%VERSION%";

// 成功状态码
export const SUCCESS = 200;

// 登录失效
export const EXPIRE_LOGIN = 100802102;

// 超过限量
export const EXCEED_LIMIT = 100801105;

/**
 * 提取文案 存储key
 * @type {string}
 */
export const EXTRACT_WORD_STORAGE_KEY = "extract_word_key";

export default {
  ...ENV["%ENV%"],
  SUCCESS,
  EXCEED_LIMIT,
  EXPIRE_LOGIN,
  version,
  EXTRACT_WORD_STORAGE_KEY,
  isPro,
};

module.exports = {
  ...ENV["%ENV%"],
  SUCCESS,
  EXCEED_LIMIT,
  EXPIRE_LOGIN,
  version,
  EXTRACT_WORD_STORAGE_KEY,
  isPro,
};
