<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="result" style="margin-top: {{ paddingHeight }}px">

  <block>
    <view class="result-item" wx:for="{{ list }}" wx:key="id" bind:tap="videoClick" data-id="{{ item.deviceSerial }}">
      <view class="result-item__position">
        <image class="result-icon" src="/images/video/position.png" />
        <view>{{ item.deviceName }}</view>
      </view>

      <view class="result-card video" >

        <video
          class="video-player"
          id="myVideo"
          src="{{ item.liveUrl }}"
          controls
          show-center-play-btn
          show-play-btn
          show-fullscreen-btn
          picture-in-picture-mode="push"
          bindplay="onPlay"
          binderror="onError"
          bindended="onEnded"
        />

      </view>
    </view>
  </block>

</view>
<TabBar activeIndex="{{ -1 }}" />



