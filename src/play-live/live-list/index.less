page {
  background-color: #EBFBFC;
}

.result {
  padding: 40rpx 24rpx;
  padding-bottom: 280rpx;

  font-size: 32rpx;
  color: #052031;

  .empty {
    display: block;
    width: 332rpx;
    height: 266rpx;
    margin: 200rpx auto 0;
  }

  .footer {
    position: fixed;
    bottom: 300rpx;
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
    font-size: 26rpx;
    color: #052031;

    .btn {
      margin-top: 20rpx;
    }
  }

  .btn-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 8rpx;
  }

  .title {
    text-align: center;
    margin: 20rpx 0 28rpx;
    font-weight: bold;
    font-size: 40rpx;
    color: #052031;
  }

  .desc {
    font-size: 26rpx;
    color: #052031;
    line-height: 52rpx;
    padding: 0 90rpx;
  }

  &-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 4rpx;
  }

  &-card {
    position: relative;
    width: 702rpx;
    height: 396rpx;
    border-radius: 32rpx 32rpx 32rpx 32rpx;
    overflow: hidden;

    &__bg {
      width: 100%;
      height: 100%;
    }

    &__similar {
      position: absolute;
      right: 24rpx;
      top: 24rpx;
      text-align: center;

      .similar-text {
        background: #052031;
        border-radius: 40rpx 40rpx 40rpx 40rpx;
        opacity: 0.5;
        padding: 4rpx 14rpx;
        font-size: 24rpx;
        color: #ffffff;
      }

      .result-avatar {
        width: 96rpx;
        height: 96rpx;
        border: 4rpx solid #fff;
        border-radius: 96rpx;
      }
    }

    &__footer {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 70rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #052031;
      background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
      border-radius: 0rpx 0rpx 32rpx 32rpx;
      opacity: 0.9;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &-item {
    margin-bottom: 40rpx;

    &__position {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
    }
  }
}

.popup {
  width: 100%;
  height: 414rpx;
  text-align: center;
  padding: 60rpx 64rpx;
  box-sizing: border-box;

  &-title {
    font-weight: 800;
    font-size: 36rpx;
    color: #052031;
  }

  &-desc {
    font-weight: 500;
    font-size: 28rpx;
    color: #052031;
    margin: 24rpx 0 60rpx;
    opacity: .5;
    line-height: 40rpx;
  }
}

.btn {
  width: 204rpx;
  height: 80rpx;
  margin: 0 auto;
  display: grid;
  font-size: 36rpx;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
  border-radius: 100rpx 100rpx 100rpx 100rpx;
}

.search-btn {
  width: 464rpx;
  height: 80rpx;
  margin: 0 auto;
  display: flex;
  font-size: 36rpx;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
  border-radius: 100rpx 100rpx 100rpx 100rpx;
}





.video {


  &-player {
    width: 100%;
    height: 100%;
  }

}
