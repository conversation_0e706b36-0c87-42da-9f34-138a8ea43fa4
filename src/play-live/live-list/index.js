var util = require("@/utils/util.js");
import { getOneMixVideoTaskApi } from "@/api/retrieval";
import { querySitDevListApi } from "@/api/site";
const app = getApp();
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "场馆画面",
    },
    list: [],
    state: [
      {
        id: 100,
        label: "视频检索中",
        color: "#000000",
      },
      {
        id: 0,
        label: "等待合成",
        color: "#000000",
      },
      {
        id: 1,
        label: "合成中",
        color: "#000000",
      },
      {
        id: 2,
        label: "点击观看",
        color: "#000000",
      },
      {
        id: -1,
        label: "合成失败",
        color: "#FF4500",
      },
    ],
    process_state_list: [100, 0, 1],
    isEmpty: false,
    show: false,
    siteCode: 0,
  },
  onLoad(options) {
    console.log("options=", options);
    let siteCode = options.code;
    this.setData({ siteCode: siteCode });
    this.getSitDevList();
  },

  onClose() {
    this.setData({ show: false });
  },

  getSitDevList() {
    querySitDevListApi({
      siteCode: this.data.siteCode,
      pageNo: 1,
      pageSize: 100,
    }).then((res) => {
      console.log("res=", res);

      let list = [];
      res.data.list.forEach((item) => {
        list.push(item);
      });
      this.setData({
        list: list,
      });
    });
  },
  videoClick(e) {
    let id = e.currentTarget.dataset.id;
    console.log("videoClick");
    wx.navigateTo({
      url: "/play-live/living/index?id=" + id,
    });
  },
  onPlay(e) {
    console.log("e=", e);
  },
});
