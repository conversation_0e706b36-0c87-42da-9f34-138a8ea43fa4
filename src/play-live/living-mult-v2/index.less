.container {
  position: fixed; /* 改为fixed，防止页面滚动 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #181818;
  width: 100%;
  height: 100vh; /* 改为100vh，占满整个屏幕 */
  max-height: 100vh; /* 限制最大高度 */
  box-sizing: border-box;
  color: white;
  overflow: hidden; /* 禁止滚动 */
  font-size: 28rpx;
  text-align: center;
  touch-action: none;
  /* 禁止陀螺仪影响和页面拖拽 */
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  /* 强制竖屏方向 */
  orientation: portrait;
}

/* 时间显示 */
.time-display {
  width: 100%;
  text-align: center;
  margin-bottom: 40rpx;
}

.control-buttons-left {
  display: flex;
  justify-content: center;
  align-items: center;
}

.current-time {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  font-family: 'Courier New', monospace;
}

/* 录制时间显示 */
.record-time-display {
  width: 100%;
  background-color: rgba(255, 107, 107, 0.1);
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(255, 107, 107, 0.3);
}

.record-time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.record-time-item:last-child {
  margin-bottom: 0;
}

.record-time-label {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.record-time-value {
  font-size: 32rpx;
  color: #ff6b6b;
  font-family: 'Courier New', monospace;
  font-weight: bold;
}


/* 控制按钮区域 */
.control-buttons {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #141414;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}

.control-buttons-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);

}

/* 录制按钮组 */
.record-buttons {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.record-buttons.recording-active {
  padding: 10rpx 20rpx;
  background-color: rgba(255, 107, 107, 0.1);
  border-radius: 15rpx;
  border: 2rpx solid rgba(255, 107, 107, 0.3);
}

.control-btn.start-record {
  background-color: #ff6b6b;
  color: white;
}

.control-btn.cancel-record {
  background-color: #8e8e93;
  color: white;
  font-size: 24rpx;
  min-width: 140rpx;
}

.control-btn.stop-record {
  background-color: #ff4757;
  color: white;
  animation: recording-pulse 1.5s infinite;
}

@keyframes recording-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 滑块轨道容器 - 重构后的新结构 */
.slider-track-container {
  position: relative;
  width: 100%;
  height: 200rpx;
  padding: 20rpx 0;
  box-sizing: border-box;
  z-index: 1;
  /* 不设置overflow，让手柄可以正常显示 */
}

/* 基准线 - 固定在中央 */
.baseline-line {
  position: absolute;
  left: 50%;
  top: 50rpx;
  transform: translate(-50%);
  z-index: 600;
  width: 5rpx;
  height: 100%;
  background-color: red;
  pointer-events: none;
}

/* 时间刻度显示层 */
.time-scales-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 180rpx;
  z-index: 300;
  pointer-events: none;
}

/* 录制区域显示层 */
.record-area-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 180rpx;
  z-index: 400;
  pointer-events: none;
}

/* 滑块交互层 */
.slider-layer {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  transform: translateY(-50%);
  z-index: 500;
  height: 60rpx;
}

/* 原生滑块样式 */
.time-slider {
  width: 100%;
  height: 60rpx;
}

/* 录制区域背景 - 适配新结构 */
.record-area {
  box-sizing: border-box;
  position: absolute;
  bottom: -12rpx;
  height: 105rpx;
  background: linear-gradient(90deg, rgba(76, 175, 80, 0.3) 0%, rgba(139, 195, 74, 0.25) 50%, rgba(76, 175, 80, 0.3) 100%);
  border: 2rpx solid rgba(76, 175, 80, 0.6);
  box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.3);
  min-width: 60rpx;
  box-sizing: border-box;
  /* 确保没有过渡动画，提高拖拽跟手性 */
  transition: none !important;
  animation: none !important;
  /* 强制禁用所有变换动画 */
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
}

/* 录制区域显示层需要允许手柄交互 */
.record-area-layer .record-drag-handle {
  pointer-events: auto;
}

/* 录制区域进入动画 */
.record-area-enter {
  animation: recordAreaFadeIn 0.3s ease-out;
}

/* 录制区域淡入动画 */
@keyframes recordAreaFadeIn {
  0% {
    opacity: 0;
    transform: scaleX(0.8);
    background-color: rgba(255, 107, 107, 0.1);
  }
  50% {
    opacity: 0.7;
    transform: scaleX(0.95);
    background-color: rgba(255, 107, 107, 0.3);
  }
  100% {
    opacity: 1;
    transform: scaleX(1);
    background-color: rgba(255, 107, 107, 0.25);
  }
}

/* 录制区域拖拽手柄 - 箭头样式 */
.record-drag-handle {
  position: absolute;
  top: calc(100% + 60rpx); /* 往下移动30rpx，增加与录制区域的距离 */
  width: 70rpx;
  height: 90rpx;
  z-index: 550;
  pointer-events: auto;
}

/* 左侧拖拽手柄 - 保持在录制区域边界内 */
.record-drag-handle-left {
  left: 0;
  transform: translateX(-50%);
}

/* 右侧拖拽手柄 - 保持在录制区域边界内 */
.record-drag-handle-right {
  right: 0;
  transform: translateX(50%);
}

/* 手柄连接线 */
.record-drag-handle::before {
  content: '';
  position: absolute;
  top: -60rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 2rpx;
  height: 60rpx;
  background: #4caf50;
  z-index: 549;
}

/* 手柄标签页 - 长方形箭头样式 */
.drag-handle-tab {
  position: relative;
  width: 100%;
  height: 100%;
  background: #4caf50;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;

  /* 渐变背景 */
  background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);

  /* 禁用拖拽时的动画效果，提高跟手性 */
  transition: none !important;
  animation: none !important;
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;

  /* 悬停效果 - 仅在非拖拽状态下生效 */

  &:active:not(.dragging) {
    background: linear-gradient(135deg, #5cb85c 0%, #43a047 100%);
    transform: scale(0.95);
  }
}

/* 左箭头图标 */
.record-drag-handle-left .drag-handle-tab::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-right: 16rpx solid rgba(255, 255, 255, 0.9);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 右箭头图标 */
.record-drag-handle-right .drag-handle-tab::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-left: 16rpx solid rgba(255, 255, 255, 0.9);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}


/* 录制区域标签 */
.record-area-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(76, 175, 80, 0.9);
  font-size: 20rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
  pointer-events: none;
  z-index: 500;
  white-space: nowrap;
}

/* 确认对话框遮罩 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 对话框容器 */
.dialog-container {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

/* 对话框标题 */
.dialog-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

/* 对话框内容 */
.dialog-content {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 录制信息 */
.record-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 10rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
}

.record-info-text {
  color: rgba(76, 175, 80, 0.9);
  font-weight: bold;
  font-size: 26rpx;
}

/* 对话框按钮组 */
.dialog-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

/* 对话框按钮 */
.dialog-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 取消按钮 */
.dialog-btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.dialog-btn-cancel:hover {
  background: #e0e0e0;
}

/* 确定按钮 */
.dialog-btn-confirm {
  background: #ff4757;
  color: white;
}

.dialog-btn-confirm:hover {
  background: #ff3742;
}

/* 刻度项 */
.scale-item {
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transform: translateX(-50%);
  /* 优化GPU加速和平滑过渡 */
  transition: transform 0.08s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* 刻度线 */
.scale-line {
  background-color: white;
  width: 4rpx;
}

/* 普通刻度样式 */
.scale-normal .scale-line {
  height: 60rpx;
  background-color: white;
  margin-top: auto;
  width: 2rpx;
}

/* 高刻度样式 */
.scale-high .scale-line {
  height: 100rpx;
  background-color: white;
  margin-top: auto;
  width: 5rpx;
  transform: translateY(15rpx);
}

/* 未来时间刻度样式 - 灰色显示 */
.scale-future .scale-line {
  background-color: #666666 !important; /* 使用灰色覆盖原有颜色 */
  opacity: 0.6; /* 增加透明度效果 */
}

.header-box {
  background: #141414;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
}

.header-box.show {
  max-height: 200rpx; /* 设置一个足够大的值 */
  padding: 20rpx 20rpx;
}

.save-slice-btn {
  font-weight: bold;
  border: none;
  padding: 15rpx 35rpx;
  font-size: 28rpx;
  border-radius: 20rpx;
  background: #595959;
  color: #8a8a8a;
}

.save-slice-btn.activate {
  background: #ff0d44;
  color: white;
}


/* 时间标签 */
.time-label {
  position: absolute;
  top: 0;
  font-size: 24rpx;
  border-radius: 4rpx;
  white-space: nowrap;
  color: #fff;
}

/* 未来时间标签样式 - 灰色显示 */
.time-label-future {
  color: #666666 !important; /* 使用灰色覆盖原有颜色 */
  opacity: 0.6; /* 增加透明度效果 */
}

.video-box {
  position: relative;
  width: 100%;
  height: 30vh;

  // ==================== 静音按钮 ====================
  .mute-toggle {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 100;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 30rpx;
    padding: 12rpx 20rpx;
    backdrop-filter: blur(10rpx);
    transition: all 0.3s ease;

    &__icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
      opacity: 0.8;
      transition: opacity 0.3s ease;
    }

    &__text {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }

    // 激活状态（静音时）
    &--active {
      background: rgba(255, 107, 107, 0.8);

      .mute-toggle__icon {
        opacity: 1;
      }

      .mute-toggle__text {
        color: #ffffff;
      }
    }

    // 悬停效果
    &:active {
      transform: scale(0.95);
      background: rgba(0, 0, 0, 0.8);

      &.mute-toggle--active {
        background: rgba(255, 107, 107, 1);
      }
    }
  }

  /* 刷新按钮样式 */

  .refresh-toggle {
    position: absolute;
    bottom: 30rpx;
    right: 30rpx;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
  }
}

/* 录制状态下的视频框样式 - 减少高度变化 */
.video-box.recording {
  height: 35vh;
}

.video-box-player {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

#myVideo {
  width: 100%;
  display: block; /* 移除默认的inline样式 */
  object-fit: contain; /* 保持宽高比，完整显示视频 */
  max-height: 100%;
}

.camera-box {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  box-sizing: border-box;
  padding: 20rpx 20rpx;
  display: flex;
  flex-wrap: nowrap;
}

.slice-video-box {
  height: 40vh;
  width: 100vw;
  position: relative;
  box-sizing: border-box;

  // 加载状态样式
  &--loading {
    .video-item-wrapper {
      opacity: 0.3;
      transition: opacity 0.3s ease;
    }
  }
}

.video-list-scroll {
  height: 100%;
  width: 100%;
  padding: 0 24rpx;
  box-sizing: border-box;
}

// 视频列表加载提示
.video-list-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;

  .loading-text {
    color: #666;
    font-size: 28rpx;
    text-align: center;
    padding: 20rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.video-item-wrapper {
  position: relative;
  overflow: hidden;
  margin-bottom: 24rpx;

  &--show-delete {
    .video-item {
      transform: translateX(-120rpx);
    }

    .video-item__delete-btn {
      transform: translateX(0);
    }
  }
}

.video-item {
  text-align: start;
  width: 100%;
  display: flex;
  height: 130rpx;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4rpx 20rpx 0 rgba(54, 127, 137, 0.1);
  border-radius: 32rpx;
  transition: transform 0.3s ease-out;
  position: relative;
  z-index: 2;
  overflow: hidden;

  &--new {
    animation: slideInFromTop 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  &--status-success {
    animation: successPulse 1.5s ease-out;
  }

  &--status-failed {
    animation: failedShake 1.5s ease-out;
  }

  &--status-processing {
    animation: updatePulse 1.5s ease-out;
  }

  &--status-thumbnail {
    .video-item__image {
      animation: thumbnailUpdate 0.8s ease-in-out;
    }
  }

  // ==================== 切片成功状态遮罩层 ====================
  &__success-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2ff8a8 0%, #27e09c 100%);
    border-radius: 32rpx;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    backdrop-filter: blur(2rpx);
    animation: overlayFadeIn 0.3s ease-out, overlayFadeOut 0.6s ease-in 0.9s;
  }

  &__success-content {
    background: transparent;
    border-radius: 20rpx;
    padding: 0;
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: none;
  }

  &__success-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #fff;
    text-align: center;
    letter-spacing: 2rpx;
    text-shadow: 0 2rpx 8rpx rgba(39, 224, 156, 0.18);
  }

  &__thumbnail {
    width: 266rpx;
    height: 100%;
    border-radius: 20rpx;
    overflow: hidden;
    flex-shrink: 0;
    background: rgba(255, 255, 255, 0.05); /* 占位符背景 */
    position: relative;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease; /* 添加淡入效果 */

    /* 图片加载完成后显示 */

    &.loaded {
      opacity: 1;
    }
  }

  &__content {
    flex: 1;
    padding: 24rpx 20rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: flex-start;
  }

  &__title {
    font-weight: 550;
    font-size: 32rpx;
    color: #FFFFFF;
    flex: 1;
    margin-right: 20rpx;
  }

  &__duration {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  &__duration-icon {
    width: 44rpx;
    height: 44rpx;
    margin-right: 10rpx;
  }

  &__duration-text {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 26rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    background: linear-gradient(270deg, #00CEFF 0%, #57FAE6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  &__time-range {
    width: 100%;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #FFFFFF;
  }

  &__delete-btn {
    height: 130rpx;
    border-radius: 32rpx;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 120rpx;
    background-color: #ff4444;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 28rpx;
    transform: translateX(120rpx);
    transition: transform 0.3s ease;
    z-index: 1;
  }

  // ==================== 失败状态遮罩层 ====================
  &__failure-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 71, 87, 0.9), rgba(255, 56, 56, 0.95));
    border-radius: 32rpx;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    backdrop-filter: blur(2rpx);
    animation: overlayFadeIn 0.3s ease-out;
  }

  &__retry-btn {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 12rpx 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    animation: retryButtonPulse 2s infinite ease-in-out;
  }

  &__retry-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #ff4757;
    text-align: center;
  }

  // ==================== 录制中状态遮罩层 ====================
  &__processing-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 159, 67, 0.9), rgba(255, 107, 107, 0.85));
    border-radius: 32rpx;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    backdrop-filter: blur(2rpx);
    animation: overlayFadeIn 0.3s ease-out;
  }

  &__processing-content {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: processingPulse 1.5s infinite ease-in-out;
  }

  &__processing-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
    animation: loadingDots 1.8s infinite ease-in-out;

    &::after {
      content: '';
      animation: loadingDotsAfter 1.8s infinite ease-in-out;
    }
  }

}

.clip-button-box {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.button-box {
  margin: 30rpx auto;
}

.footer-box {
  width: 100%;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: -10vh;
  transition: bottom 0.3s linear;
  z-index: 1000;
}

.footer-box-show {
  bottom: 2vh;
}

.footer-icon-item {
  text-align: center;
  line-height: 50rpx;
  width: 100%;
  padding: 20rpx 0;
  transition: all 0.3s ease;
}

.not-online-icon {
  color: #595959;
}


/* 剪辑/保存按钮样式 */
.cut-icon {
  position: relative;

  &.cut-icon--save-mode {
    color: #35ff3b;

    /* 添加保存模式的背景高亮 */

    &::before {
      content: '';
      position: absolute;
      top: 10rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 80rpx;
      height: 80rpx;
      background: rgba(20, 142, 23, 0.1);
      border-radius: 50%;
      z-index: -1;
    }

    /* 保存按钮的图标动画 */

    .t-icon {
      animation: saveButtonPulse 2s infinite;
    }
  }
}

/* 保存按钮脉冲动画 */
@keyframes saveButtonPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.header-box-left {
  width: 140rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}


/* 撤回按钮样式 */
.rollback-btn {
  transition: all 0.3s ease;
  color: #595959;
  opacity: 0.6;
}


/* ==================== 新手教程样式 ==================== */

/* 教程遮罩层 */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
}

/* 半透明遮罩背景 */
.tutorial-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  pointer-events: auto;
}

/* 高亮区域基础样式 */
.tutorial-highlight {
  position: absolute;
  background-color: transparent;
  border: 3rpx solid #4CAF50; /* 减少边框宽度，更精确 */
  border-radius: 8rpx; /* 减少圆角，更贴合实际元素 */
  box-shadow: 0 0 0 4000rpx rgba(0, 0, 0, 0.6);
  pointer-events: auto;
  animation: tutorial-pulse 2s infinite;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.tutorial-highlight:active {
  border-color: #45a049;
  box-shadow: 0 0 0 4000rpx rgba(0, 0, 0, 0.7), 0 0 30rpx rgba(76, 175, 80, 0.8);
}

/* 高亮区域脉冲动画 - 避免transform冲突 */
@keyframes tutorial-pulse {
  0%, 100% {
    border-color: #4CAF50;
    box-shadow: 0 0 0 4000rpx rgba(0, 0, 0, 0.6), 0 0 15rpx rgba(76, 175, 80, 0.5);
    border-width: 3rpx;
  }
  50% {
    border-color: #66BB6A;
    box-shadow: 0 0 0 4000rpx rgba(0, 0, 0, 0.6), 0 0 25rpx rgba(76, 175, 80, 0.8);
    border-width: 4rpx; /* 减少脉冲时的边框宽度变化 */
  }
}

/* 录制区域专用脉冲动画 */
@keyframes tutorial-pulse-record {
  0%, 100% {
    border-color: #FF6B6B;
    box-shadow: 0 0 0 4000rpx rgba(0, 0, 0, 0.6), 0 0 20rpx rgba(255, 107, 107, 0.5);
    border-width: 4rpx;
  }
  50% {
    border-color: #FF8A80;
    box-shadow: 0 0 0 4000rpx rgba(0, 0, 0, 0.6), 0 0 30rpx rgba(255, 107, 107, 0.8);
    border-width: 6rpx;
  }
}

/* 不同目标区域的高亮位置 */
.tutorial-highlight-control-buttons {
  /* 时间轴区域 - 包含控制按钮和手势操作区域 */
  top: 25vh;
  left: 0;
  right: 0;
  height: 55vh;
}

.tutorial-highlight-footer-icon-item {
  /* 左下角剪辑按钮 - 精确对应footer-box中的第一个footer-icon-item */
  bottom: 2vh;
  left: 0;
  width: 16.66%; /* 100% / 6个按钮 = 16.66% */
  height: calc(100rpx + 40rpx); /* line-height(50rpx) + padding(20rpx * 2) + icon高度 */
}

.tutorial-highlight-clip-button-and-record-area {
  /* 第二步：同时高亮剪辑按钮区域 */
  bottom: 2vh;
  left: 0;
  width: 16.66%; /* 100% / 6个按钮 */
  height: 180rpx;
}


.tutorial-highlight-gesture-operation-box {
  /* 第二步：时间轨道容器区域 - 精确对应gesture-operation-box */
  top: calc(35vh + 120rpx); /* header-box(120rpx) + video-box(35vh) */
  left: 0;
  right: 0;
  height: calc(38vh + 40rpx); /* gesture-operation-box高度 + padding */
}

.tutorial-highlight-save-slice-btn {
  /* 第三步：右上角保存切片按钮 - 精确对应header-box中的保存按钮 */
  top: 30rpx; /* header-box内部padding */
  right: 20rpx; /* header-box-right的padding */
  width: 160rpx; /* 保存按钮的实际宽度 */
  height: 60rpx; /* 保存按钮的实际高度 */
}

.tutorial-highlight-save-button {
  /* 兼容旧的保存按钮样式 */
  top: 0;
  right: 20rpx;
  width: 200rpx;
  height: 120rpx;
}

/* 教程提示框容器 - 负责定位 */
.tutorial-popup-container {
  position: absolute;
  pointer-events: auto;
  z-index: 10001; /* 确保弹框在高亮区域之上 */
}

/* 不同位置的提示框容器 */
.tutorial-popup-container-bottom {
  left: 50%;
  top: 30vh;
  transform: translateX(-50%);
}

.tutorial-popup-container-top {
  left: 50%;
  bottom: 30vh;
  transform: translateX(-50%);
}

.tutorial-popup-container-top-center {
  left: 50%;
  top: 15vh; /* 显示在页面更上方，确保不挡住轨道高亮区域 */
  transform: translateX(-50%);
}

/* 教程提示框 - 负责样式和动画 */
.tutorial-popup {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
  animation: tutorial-popup-show 0.3s ease-out;
}

/* 提示框显示动画 - 避免transform冲突 */
@keyframes tutorial-popup-show {
  0% {
    opacity: 0;
    box-shadow: 0 5rpx 20rpx rgba(0, 0, 0, 0.1);
  }
  100% {
    opacity: 1;
    box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
  }
}

/* 教程头部 */
.tutorial-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.tutorial-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.tutorial-step {
  font-size: 28rpx;
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
}

/* 教程内容 */
.tutorial-content {
  font-size: 30rpx;
  line-height: 1.6;
  color: #666;
  margin-bottom: 40rpx;
}

/* 教程按钮组 */
.tutorial-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.tutorial-btn {
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  transition: all 0.2s ease;
}

.tutorial-btn-skip {
  background: #f5f5f5;
  color: #999;
}

.tutorial-btn-skip:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.tutorial-btn-next {
  background: #4CAF50;
  color: white;
}

.tutorial-btn-next:active {
  background: #45a049;
  transform: scale(0.95);
}

.camera-item {
  flex-shrink: 0;
  padding: 16rpx 40rpx;
  color: #ffffff;
  margin-right: 16rpx;
  background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
  opacity: .5;
  border: 1rpx solid transparent;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &--active {
    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    border: 1rpx solid #FFFFFF;
    font-weight: bold;
    color: #052031;
    opacity: 1;
  }

  &__name {
    white-space: nowrap;
  }
}

.pre-clip-edit {
  width: 400rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  border: none;
  outline: none;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  background: linear-gradient(135deg, #2FD2F8, #2FF8A8);
  box-shadow: 0 8rpx 20rpx rgba(47, 210, 248, 0.3);
  animation: breathe 2s infinite ease-in-out;
  margin: 30rpx auto;
}

// ==================== 动画定义 ====================
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 20rpx rgba(47, 210, 248, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 30rpx rgba(47, 248, 168, 0.5);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 20rpx rgba(47, 210, 248, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 30rpx rgba(47, 248, 168, 0.5);
  }
}

@keyframes recordingPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

@keyframes recordingDot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  60% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes updatePulse {
  0% {
    background: rgba(47, 210, 248, 0.1);
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx 0 rgba(47, 210, 248, 0.2);
  }
  25% {
    background: rgba(47, 210, 248, 0.3);
    transform: scale(1.02);
    box-shadow: 0 8rpx 30rpx 0 rgba(47, 210, 248, 0.4);
  }
  50% {
    background: rgba(47, 210, 248, 0.4);
    transform: scale(1.03);
    box-shadow: 0 12rpx 40rpx 0 rgba(47, 210, 248, 0.5);
  }
  75% {
    background: rgba(47, 210, 248, 0.3);
    transform: scale(1.02);
    box-shadow: 0 8rpx 30rpx 0 rgba(47, 210, 248, 0.4);
  }
  100% {
    background: rgba(47, 210, 248, 0.1);
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx 0 rgba(47, 210, 248, 0.2);
  }
}

@keyframes successPulse {
  0%, 100% {
    background: rgba(47, 248, 168, 0.1);
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx 0 rgba(47, 248, 168, 0.2);
  }
  50% {
    background: rgba(47, 248, 168, 0.4);
    transform: scale(1.05);
    box-shadow: 0 16rpx 50rpx 0 rgba(47, 248, 168, 0.5);
  }
}

@keyframes failedShake {
  0%, 100% {
    background: rgba(248, 47, 47, 0.1);
    transform: translateX(0) scale(1);
  }
  10% {
    background: rgba(248, 47, 47, 0.3);
    transform: translateX(-10rpx) scale(1.02);
  }
  25% {
    background: rgba(248, 47, 47, 0.4);
    transform: translateX(10rpx) scale(1.02);
  }
  40% {
    transform: translateX(-8rpx) scale(1.01);
  }
  55% {
    transform: translateX(8rpx) scale(1.01);
  }
  70% {
    transform: translateX(-5rpx) scale(1);
  }
  85% {
    transform: translateX(5rpx) scale(1);
  }
}

@keyframes thumbnailUpdate {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1);
  }
  25% {
    opacity: 0.4;
    transform: scale(0.95);
    filter: brightness(1.2);
  }
  50% {
    opacity: 0.2;
    transform: scale(0.9);
    filter: brightness(1.4);
  }
  75% {
    opacity: 0.6;
    transform: scale(0.95);
    filter: brightness(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1);
  }
}

@keyframes overlayFadeIn {
  0% {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(2rpx);
  }
}

@keyframes overlayFadeOut {
  0% {
    opacity: 1;
    backdrop-filter: blur(2rpx);
  }
  100% {
    opacity: 0;
    backdrop-filter: blur(0);
  }
}

@keyframes retryButtonPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.3);
  }
}


// ==================== 录制中动画定义 ====================
@keyframes processingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes loadingDots {
  0%, 20% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  80%, 100% {
    opacity: 1;
  }
}

@keyframes loadingDotsAfter {
  0% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
  100% {
    content: '';
  }
}

// ==================== 切片列表动画定义 ====================
@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  60% {
    transform: translateY(10%);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes successPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx 0 rgba(54, 127, 137, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8rpx 30rpx 0 rgba(76, 175, 80, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx 0 rgba(54, 127, 137, 0.1);
  }
}

@keyframes failedShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5rpx);
  }
}

@keyframes updatePulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes thumbnailLoading {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes thumbnailUpdate {
  0% {
    opacity: 0.3;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.control-buttons-right {
  display: flex;
  justify-content: center;
  align-items: center;
}

.devName {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  font-size: 20rpx;
}

.backBtn {
  font-size: 20rpx;
}
