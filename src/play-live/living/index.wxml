<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="video" style="margin-top: {{ paddingHeight }}px">
  

 <!--
  <ezplayer 
    id="ezplayer"
    accessToken="at.5eeroiay27f8otxb7js5w75086djwmib-60uh7ixprn-1y1xtxa-5jtx4tnbz"
    url="{{ videoUrl }}"
     muted="{{ isMute }}"
    plugins="talk,voice,capture"
    recPlayTime=""
    width="100%"
    height="300rpx"
    watermark="酷拍侠"
    themeData="{'poster':'','controls':{'fullScreenBtn':false,'hdBtn':true}}"
    bind:handleError="handleError"
  />
    -->

  <video 
    class="video-player"
    id="myVideo"
    poster="{{ img }}"
    src="{{ videoUrl }}"
    custom-cache="{{false}}"
    controls
    autoplay
    width="100%"
    height="300rpx"
    muted="{{ isMute }}"
    playsinline="true"
    show-center-play-btn
    show-play-btn
    show-fullscreen-btn
    picture-in-picture-mode="push"
    bindplay="onPlay"
    binderror="onError"
    bindended="onEnded"
  />






    <view class="bottom-actions">
      <button class="action-btn share-btn" bindtap="shareVideo" open-type="share" >
        <image src="/images/video/video_share.png" class="action-btn-icon" />
        <text>分享</text>
      </button>
      <button class="action-btn download-btn" style="width: 230rpx;" bindtap="liveSlice" wx:if="{{ !spliting }}">
        <image src="/images/video/video_download.png" class="action-btn-icon" />
        <text>直播切片</text>
      </button>

      
      <button class="action-btn download-btn" bindtap="stopSlice" wx:if="{{ false }}">
        <image src="/images/video/video_download.png" class="action-btn-icon" />
        <text>停止</text>
      </button>

       <button class="action-btn download-btn" bindtap="showSliceList" wx:if="{{ spliting }}">
        <image src="/images/video/video_download.png" class="action-btn-icon" />
        <text>查看</text>
      </button>


    </view>

</view>



  <view class="float-sidebar">

     <view class="sidebar-item" bindtap="loveVideo">
      <image class="sidebar-icon" src="/images/live/love.png" mode="aspectFit"></image>
    </view>
    <text class="sidebar-text" style="color: #333; font-size: 24rpx; margin-bottom: 10rpx;">{{ loveCount }}</text>

   <view class="sidebar-item" bindtap="muteVideo" wx:if="{{ !isMute }}">
      <image class="sidebar-icon" src="/images/live/sound.png" mode="aspectFit"></image>
    </view>

    <view class="sidebar-item" bindtap="muteVideo" wx:if="{{ isMute }}">
      <image class="sidebar-icon" src="/images/live/muted.png" mode="aspectFit"></image>
    </view>

    <view class="sidebar-item" bindtap="refreshVideo" >
      <image class="sidebar-icon" src="/images/live/refresh.png" mode="aspectFit"></image>
    </view>
  </view>