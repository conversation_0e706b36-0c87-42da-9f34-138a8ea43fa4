const app = getApp();
import { querySitDevListApi } from "@/api/site";
import {
  queryLiveSplitListApi,
  createLiveTaskApi,
  completeLiveTaskApi,
  queryLiveTaskListApi,
} from "@/api/live";
Page({
  data: {
    paddingHeight: app.globalData.paddingHeight + 10,
    statusHeight: app.globalData.statusHeight,
    navData: {
      title: "场馆画面",
    },
    isChecked: false,
    show: false,
    // 标清视频
    videoUrl: "",
    // 超清视频
    superVideoUrl: "",
    videoList: [],
    id: "",
    shareUrl: "",
    isMute: false,
    loveCount: 12567,
    deviceSerial: "",
    liveSplitTaskId: 0,
    spliting: false,
    isEmpty: false,
  },

  onLoad: function (options) {
    console.log("options", options);
    let id = options.id;
    let shareUrl =
      "/play-live/living/index?pCode=" +
      app.globalData.user.userCode +
      "&id=" +
      id;
    this.setData({
      shareUrl: shareUrl,
      id: id,
    });
    this.getSitDevList(id);
  },

  onShareAppMessage: function () {
    let shareUrl = this.data.shareUrl;
    return {
      title: app.globalData.share_config.title,
      imageUrl: "https://k-cos.ai-market.top/wx/base/logo-launch.png",
      path: shareUrl,
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: shareUrl,
    };
  },

  getSitDevList(id) {
    querySitDevListApi({
      deviceSerial: id,
      pageNo: 1,
      pageSize: 100,
    }).then((res) => {
      console.log("res=", res);
      this.setData({
        videoUrl: res.data.list[0].liveUrl,
        superVideoUrl: res.data.list[0].superLiveUrl,
        deviceSerial: res.data.list[0].deviceSerial,
      });
      this.getLiveTaskList();
    });
  },
  getLiveTaskList() {
    queryLiveTaskListApi({
      pageSize: 1,
      deviceSerial: this.data.deviceSerial,
      userId: app.globalData.user.userCode,
    }).then((res) => {
      console.log("res=", res);
      if (res.data.list.length > 0) {
        let taskStatus = res.data.list[0].taskStatus;

        //判断任务的创建时间是否已经超过60分钟
        let createTime = res.data.list[0].createTime;
        let now = new Date().getTime();
        let diff = now - createTime;

        console.log("diff=", diff / 1000 / 60);

        // 任务状态小于3， 任务状态 0 默认值  1 等待录制 2 开始录制 3 录制完成 4 录制失败
        if (taskStatus < 3 && diff < 60 * 60 * 1000) {
          this.setData({
            liveSplitTaskId: res.data.list[0].id,
            spliting: true,
          });
        }
      }
    });
  },

  muteVideo() {
    console.log("muteVideo", this.data.isMute);

    this.setData({
      isMute: !this.data.isMute,
    });
  },
  refreshVideo() {
    console.log("refreshVideo");
    wx.showLoading({
      title: "刷新中",
    });
    this.getSitDevList(this.data.id);
  },
  loveVideo() {
    console.log("loveVideo");
    this.setData({
      loveCount: this.data.loveCount + 1,
    });
  },
  liveSlice() {
    console.log("liveSlice", this.data.superVideoUrl);
    wx.showLoading({
      title: "正在创建任务",
    });
    createLiveTaskApi({
      deviceSerial: this.data.deviceSerial,
      liveUrl: this.data.superVideoUrl,
    }).then((res) => {
      console.log("res=", res);
      this.setData({
        liveSplitTaskId: res.data,
        spliting: true,
      });
      wx.showModal({
        title: "提示",
        content: "任务创建成功，请稍后查看切片",
        showCancel: true,
        success: ({ confirm, cancel }) => {},
      });
    });
  },
  stopSlice() {
    console.log("stopSlice");
  },
  showSliceList() {
    console.log("showSliceList", this.data.liveSplitTaskId);
    wx.navigateTo({
      url:
        "/pages/ucenter/live-split/index?taskId=" + this.data.liveSplitTaskId,
    });
  },
  handleError(e) {
    console.log("error", e.detail.errMsg);
  },
});
