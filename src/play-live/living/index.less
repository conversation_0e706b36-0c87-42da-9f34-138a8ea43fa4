page {
  background-color: #EBFBFC;
}

.video {
  text-align: center;
  width: 100%;
  background-color: #000;

   &-player {
    width: 100%;
    height: 300rpx;
  }

}

.bottom-actions {
  display: flex;
  justify-content: space-between;
  padding: 0 20rpx 50rpx;
  background-color: #000;
}

.action-btn {
  margin-bottom: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 100rpx;
  height: 76rpx;
  width: 180rpx;
}

.action-btn text {
  color: #fff;
  font-size: 14px;
  margin-left: 10rpx;
}

.action-btn-icon {
  width: 43rpx;
  height: 43rpx;
}


/* 侧边栏浮窗样式 */
.float-sidebar {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  animation: slideIn 0.3s ease-out;
}

.sidebar-item {
  position: relative; 
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50rpx;
  width: 80rpx;
  height: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.sidebar-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s;
}

.sidebar-item:active::after {
  opacity: 1;
}

.sidebar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.sidebar-text {
  color: #ffffff;
  font-size: 20rpx;
  line-height: 1;
}

/* 点击效果 */
.sidebar-item:active {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 入场动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
