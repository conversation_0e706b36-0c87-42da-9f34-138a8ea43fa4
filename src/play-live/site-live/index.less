page {
  background-color: #EBFBFC;
}

.wrap {
  padding: 40rpx 24rpx 280rpx;

  .search {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    background: #FFFFFF;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(54, 127, 137, 0.1);
    border-radius: 100rpx 100rpx 100rpx 100rpx;
    padding: 0 24rpx 0 12rpx;
    box-sizing: border-box;
    margin-bottom: 40rpx;

    .right {
      display: flex;
      align-items: center;
      flex: 1;
    }

    &-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
    }

    &-input {

      flex: 1;
    }

    &-btn {
      width: 64rpx;
      height: 64rpx;
      margin-left: 20rpx;
    }
  }

  .history {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 24rpx;
    color: #052031;

    &-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 8rpx;
    }
  }

  .menu {
    position: relative;
    font-size: 32rpx;
    color: #052031;
    display: flex;
    align-items: baseline;
    padding: 0 16rpx;
    margin-bottom: 26rpx;

    &-item {
      margin-right: 48rpx;

      &.active {
        position: relative;
        font-weight: bold;
        font-size: 36rpx;
        color: #052031;

        &::before {
          position: absolute;
          z-index: -1;
          left: 0;
          bottom: 8rpx;
          content: '';
          width: 100%;
          height: 10rpx;
          background: linear-gradient(180deg, #00B2FF 0%, #2FD2F8 100%);
          border-radius: 20rpx 20rpx 20rpx 20rpx;
        }
      }
    }
  }

  .card {
    padding: 20rpx 28rpx;
    border-radius: 32rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(54, 127, 137, 0.1);

    &-left {
      position: relative;

      .thumbnail {
        width: 144rpx;
        height: 144rpx;
        border-radius: 144rpx;
        overflow: hidden;
        border: 4rpx solid #ffffff;
      }

      &__text {
        position: absolute;
        bottom: 0;
        font-weight: 500;
        font-size: 28rpx;
        color: #FFFFFF;
        padding: 4rpx 29rpx;
        border-radius: 30rpx;
        background-color: #FF4F28;
      }

      &__img {
        width: 150rpx;
        height: 150rpx;
        border-radius: 160rpx;
        overflow: hidden;
        border: 4rpx solid #FF4F28;
      }
    }

    &-right {
      padding: 20rpx 10rpx 20rpx 40rpx;

      .line {
        margin: 0 20rpx;
      }

      .title {
        font-weight: bold;
        font-size: 36rpx;
        color: #052031;
      }

      .desc {
        margin-top: 30rpx;
        display: flex;
        font-weight: 500;
        font-size: 28rpx;
        color: rgba(5, 32, 49, .5);
      }
    }
  }
}