const app = getApp();
const util = require("@/utils/util.js");
import { querySiteListApi } from "@/api/site";
import { queryWatchLiveRecordApi, deleteWatchLiveRecordApi } from "@/api/live";

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "场馆画面",
    },
    menus: [
      {
        id: 1,
        label: "热门场馆",
      },
      {
        id: 2,
        label: "历史记录",
      },
    ],
    cameraList: [],
    historyList: [],
    activeId: 1,
    searchValue: "",
    historySiteCodeList: [],
  },

  onLoad(options) {
    console.log("options: ", options);
    let that = this;
    console.log("query", options);
    that.data.query = options;
    that.getSiteList();
    that.getHistoryList();
  },

  onShareAppMessage: function () {
    return {
      title: app.globalData.share_config.title,
      imageUrl: app.globalData.share_config.imageUrl,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: "/pages/index/index?pCode=" + app.globalData.user.userCode,
    };
  },

  handleMenu({
    currentTarget: {
      dataset: { id },
    },
  }) {
    this.setData({
      activeId: id,
    });
  },

  delHistory(e) {
    let that = this;
    wx.showModal({
      title: "提示",
      content: "确定要删除该记录吗？",
      success: (res) => {
        if (res.confirm) {
          let that = this;
          let id = e.currentTarget.dataset.id;
          deleteWatchLiveRecordApi({
            userId: app.globalData.userInfo.userId,
          }).then((res) => {
            console.log("res", res);
            wx.showToast({
              title: "删除成功",
              icon: "success",
            });

            that.setData({
              historyList: [],
              historySiteCodeList: [],
            });
          });
        }
      },
    });
  },

  search() {
    console.log("search...", this.data.searchValue);
  },

  searchInput({ detail: { value } }) {
    this.setData({
      searchValue: value,
    });
    this.getSiteList();
  },

  getSiteList: function () {
    let that = this;
    querySiteListApi({
      siteName: that.data.searchValue,
      live: 1,
      pageNo: 1,
      pageSize: 100,
    }).then((res) => {
      console.log("res", res);
      // 遍历res.data.list，将categoryId为3的站点添加到scenicList中
      res.data.list.forEach((item) => {
        item.live = true;
      });

      // 根据 categoryId 分别将数据赋值给 scenicList、sport、table、amusement
      that.setData({
        cameraList: res.data.list,
      });
    });
  },
  getHistoryList: function () {
    let that = this;
    queryWatchLiveRecordApi({
      userId: app.globalData.userInfo.userId,
      pageNo: 1,
      pageSize: 10,
    }).then((res) => {
      console.log("queryWatchLiveRecordApi res", res);

      // 遍历res.data.list，将siteCode添加到historySiteCodeList中 去重
      res.data.list.forEach((item) => {
        if (!that.data.historySiteCodeList.includes(item.siteCode)) {
          that.data.historySiteCodeList.push(item.siteCode);
        }
      });

      if (that.data.historySiteCodeList.length == 0) {
        return;
      }
      // 根据historySiteCodeList查询站点信息
      querySiteListApi({
        siteCodes: that.data.historySiteCodeList,
        pageNo: 1,
        pageSize: 10,
      }).then((res) => {
        console.log("res", res);
        that.setData({
          historyList: res.data.list,
        });
      });
    });
  },
  gotoFeature: function (e) {
    let id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: "/play-live/living-mult-v2/index?id=" + id,
    });
  },
});
