<PageHeader />
<NavBar nav-data="{{ navData }}" />
<view class="wrap" style="margin-top: {{ paddingHeight }}px">
  <view class="search">
    <view class="right">
      <image
        class="search-icon"
        src="/images/search/icon1.png"
        bind:tap="search"
      />
      <input
        class="search-input"
        placeholder="请输入场馆名称"
        value="{{ searchValue }}"
        bind:input="searchInput"
      />
    </view>
    <image
      class="search-btn"
      src="/images/search/icon2.png"
      bind:tap="search"
    />
  </view>

  <view class="menu">
    <view
      class="menu-item {{ item.id === activeId ? 'active' : '' }}"
      wx:for="{{ menus }}"
      wx:key="id"
      data-id="{{ item.id }}"
      bind:tap="handleMenu"
    >
      {{ item.label }}
    </view>

    <view wx:if="{{ activeId === 2 }}" class="history" bind:tap="delHistory">
      <image class="history-icon" src="/images/delete.png" />
      <text>清除历史</text>
    </view>
  </view>

  <view class="list">
    <view
      wx:for="{{ activeId === 1 ? cameraList : historyList }}"
      wx:key="id"
      data-id="{{ item.id }}"
      bind:tap="gotoFeature"
      class="card"
    >
      <view class="card-left">
        <view class="card-left__img">
          <image class="thumbnail" src="{{ item.headImgUrl }}" />
        </view>
        <view wx:if="{{ item.devCount > 0 }}"  class="card-left__text">{{ item.devCount  }}个画面</view>
        <view wx:if="{{ item.devCount==0 }}" class="card-left__text" style="background: #999;">休息中</view>
      </view>
      <view class="card-right">
        <view class="title">{{ item.siteName }}</view>
        <view class="desc">
          <view>{{ item.address }}</view>
        </view>
      </view>
    </view>
  </view>
</view>
