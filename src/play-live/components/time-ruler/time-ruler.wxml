<view class="time-ruler-container">
    <view class="current-time-box">
        <!-- 指示器上方的时间显示 -->
        <view class="time-str">{{currentTimeString}}</view>
        <!-- 固定在中间的时间指示器 -->
        <view class="line"></view>
        <view class="square"></view>
    </view>


    <scroll-view
            class="time-ruler"
            scroll-x="true"
            scroll-left="{{scrollLeft}}"
            bindscroll="handleScroll"
            onscrollend="onTouchEnd"
            upper-threshold="100"
            lower-threshold="100"
            show-scrollbar="{{false}}"
            enhanced="true"
    >
      <!-- 可录制区域背景 -->
      <view class="recordable-background"
            style="width: {{recordableWidth}}px; background: {{recordableGradient}};"></view>

      <!-- 录制区域显示 -->
      <view wx:if="{{showRecordingArea}}"
            class="recording-area"
            style="left: {{recordingStartPosition}}px; width: {{recordingWidth}}px;">
        <view class="recordingAreaText">录制区域</view>
      </view>

      <view class="ruler-content" style="width: {{contentWidth}}px;">
            <view class="ruler-item {{item.isHour ? 'hour-item' : ''}}"
                  wx:for="{{rulerItems}}"
                  wx:key="id"
                  style="left: {{item.position}}px;"
            >
                <view class="ruler-line"></view>
                <view class="ruler-label" wx:if="{{item.showLabel}}">{{item.label}}</view>
            </view>
        </view>
    </scroll-view>

</view>
        <!--<button class="play-button" bindtap="togglePlayPause">-->
        <!--    {{isPlaying ? '暂停' : '播放'}}-->
        <!--</button>-->
        <!--<button wx:if="{{showBackToLatestBtn}}" bindtap="resetToRealTime" class="back-to-latest-btn">-->
        <!--    回到最新-->
        <!--</button>-->
