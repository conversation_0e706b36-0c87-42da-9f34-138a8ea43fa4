.time-ruler-container {
  width: 100%;
  height: 150rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 20rpx;
  color: #FFFFFF;
  font-style: normal;
  text-transform: none;
}


.time-ruler {
  position: relative;
  height: 120rpx;
  width: 100%;
}

.ruler-content {
  position: relative;
  height: 100%;
}

.ruler-item {
  position: absolute;
  bottom: 0;
}

.ruler-line {
  height: 26rpx;
  border: 1rpx solid #FFFFFF;
  position: absolute;
  bottom: 13rpx;
}

.hour-item .ruler-line {
  bottom: 0;
  width: 0;
  height: 52rpx;
  border: 1rpx solid #FFFFFF;
}

.ruler-label {
  position: absolute;
  bottom: 60rpx;
  transform: translateX(-50%);
}

.current-time-box {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  z-index: 10;
  bottom: 0;
  pointer-events: none; /* 添加这行，使该元素不接收鼠标/触摸事件 */
  overflow: hidden;
}

/* 时间指示器样式 */
.current-time-box .line {
  height: 100%;
  margin: 0 auto;
  z-index: 1;
  width: 0;
  border-radius: 0 0 0 0;
  border: 4rpx solid #FF4F28;
}

/* 时间指示器样式 */
.current-time-box .time-str {
  padding: 6rpx 16rpx;
  font-size: 24rpx;
  background: #FF4F28;
  border-radius: 100rpx 100rpx 100rpx 100rpx;
}

.current-time-box .square {
  background-color: #FF4F28;
  position: absolute;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  bottom: -12rpx;
  z-index: 2;
  width: 20rpx;
  height: 20rpx;
  border-radius: 0; /* 移除圆角，变成正方形 */
}


/* 添加小三角形连接指示器 */
.current-time-display:after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #4a90e2; /* 与背景色相同 */
}

.return-button {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  background-color: #007AFF;
  color: white;
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
}

/* 修改为类选择器 */
//.time-ruler-container ::-webkit-scrollbar {
//  width: 0;
//  height: 0;
//  color: transparent;
//  display: none;
//}

/* 对滚动容器添加样式 */
.ruler-container {
  -webkit-overflow-scrolling: touch;
}

.play-button {
  /* 基础按钮样式 */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 60rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
  margin: 20rpx auto;
  transition: all 0.3s ease;
}

/* 播放状态 - 绿色 */
.play-button {
  background-color: #4CAF50;
  border: 2rpx solid #43a047;
}


/* 点击效果 */
.play-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}


.time-ruler-container {
  position: relative;
}

.recordable-background {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 80%;
  z-index: 1;
  pointer-events: none;
}

.time-ruler-content {
  position: relative;
  z-index: 2;
}

.recording-area {
  position: absolute;
  bottom: 0;
  height: 80%;
  background: #00CD88;
  z-index: 2;
  overflow: hidden;
  pointer-events: none;
  border-radius: 2rpx;
  text-align: center;
}

@keyframes recording-pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

.recordingAreaText {
  font-size: 12px;
  font-weight: bold;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  animation: recording-pulse 2s infinite;
  white-space: nowrap;
}
