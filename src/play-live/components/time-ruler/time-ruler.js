// 常量定义
const ITEM_WIDTH = 25; // 时间刻度项的宽度
const INITIAL_ITEMS_COUNT = 200; // 初始加载的时间刻度数量
const LOAD_MORE_THRESHOLD = 20; // 触发加载更多的阈值
const LOAD_MORE_COUNT = 40; // 每次加载更多的数量
const SCROLL_TIMEOUT = 200; // 滚动停止检测的延迟时间
const MINUTES_PER_SCALE = 10; // 每个刻度代表的分钟数

Component({
  properties: {
    // 初始时间属性
    initialTime: {
      type: String,
      value: "",
    },
  },

  data: {
    baseTime: null, // 基准时间
    currentTimeString: "", // 当前显示的时间字符串
    rulerItems: [], // 时间轴刻度项数组
    currentTimePosition: 0, // 当前时间指示器位置
    contentWidth: 0, // 内容总宽度
    scrollLeft: 0, // 滚动位置
    isScrolling: false, // 是否正在滚动
    isPlaying: true, // 是否正在播放
    isFollowingRealTime: true, // 是否跟随实时时间
    scrollCenterTime: null, // 滚动中心对应的时间
    recordableWidth: 0, // 可录制区域宽度
    recordableGradient: "", // 可录制区域渐变样式
    recordingStartPosition: 0, // 录制开始位置
    recordingEndPosition: 0, // 录制结束位置
    recordingWidth: 0, // 录制区域宽度
    showRecordingArea: false, // 是否显示录制区域
  },

  lifetimes: {
    /**
     * 组件挂载时初始化
     */
    attached() {
      this._state = {
        timeUpdateTimer: null, // 时间更新定时器
        scrollTimeoutID: null, // 滚动超时ID
        isLoadingMore: false, // 是否正在加载更多
        isScrolling: false, // 内部滚动状态
        scrollStopTime: null, // 滚动停止时间
        pausedTime: null, // 暂停时的时间
        leftItemsCount: 0, // 左侧加载的项目数量
        rightItemsCount: 0, // 右侧加载的项目数量
        viewportWidth: 0, // 视口宽度
        isInitializing: true, // 是否正在初始化
        centerIndex: 0, // 中心索引
        centerOffset: 0, // 中心偏移
        isAutoScrolling: false, // 是否自动滚动
        playStartTime: null, // 播放开始时间
        playStartCenterTime: null, // 播放开始时的中心时间
      };
      this.initRuler();
    },

    /**
     * 组件销毁时清理资源
     */
    detached() {
      this._clearTimers();
      this._clearTimers();
    },
  },

  methods: {
    /**
     * 设置录制区域
     * @param {Date} startTime 录制开始时间
     * @param {Date} endTime 录制结束时间
     */
    setRecordingArea(startTime, endTime) {
      if (!startTime || !this.data.baseTime) {
        this.hideRecordingArea();
        return;
      }

      // 计算录制开始位置
      const startDiffMinutes = (startTime - this.data.baseTime) / (60 * 1000);
      const startDiffScales = startDiffMinutes / MINUTES_PER_SCALE;
      const startPosition =
        this._state.centerIndex * ITEM_WIDTH + startDiffScales * ITEM_WIDTH;

      let endPosition = startPosition;
      let recordingWidth = 0;

      if (endTime) {
        // 计算录制结束位置
        const endDiffMinutes = (endTime - this.data.baseTime) / (60 * 1000);
        const endDiffScales = endDiffMinutes / MINUTES_PER_SCALE;
        endPosition =
          this._state.centerIndex * ITEM_WIDTH + endDiffScales * ITEM_WIDTH;
        recordingWidth = Math.abs(endPosition - startPosition);
      }

      this.setData({
        recordingStartPosition: Math.min(startPosition, endPosition),
        recordingEndPosition: Math.max(startPosition, endPosition),
        recordingWidth: recordingWidth,
        showRecordingArea: true,
      });
    },

    /**
     * 隐藏录制区域
     */
    hideRecordingArea() {
      this.setData({
        showRecordingArea: false,
        recordingWidth: 0,
      });
    },

    /**
     * 更新录制区域（录制过程中调用）
     * @param {Date} startTime 录制开始时间
     * @param {Date} currentTime 当前时间（可能是拖拽位置或实时时间）
     */
    updateRecordingArea(startTime, currentTime) {
      if (!startTime || !currentTime) return;
      this.setRecordingArea(startTime, currentTime);
    },

    // ==================== 对外暴露的公共方法 ====================

    /**
     * 开始播放
     * 从当前时间开始继续播放
     */
    play() {
      if (!this.data.isPlaying) {
        this._resumePlay();
        this.triggerEvent("playStateChange", { isPlaying: true });
      }
    },

    /**
     * 暂停播放
     * 停止时间更新，保存当前状态
     */
    pause() {
      if (this.data.isPlaying) {
        this._pausePlay();
        this.triggerEvent("playStateChange", { isPlaying: false });
      }
    },

    /**
     * 重置到实时状态
     * 跳转到当前实时时间并开始播放
     */
    resetToRealTime() {
      this._resetToRealTime();
      this.triggerEvent("resetToRealTime", { time: new Date() });
    },

    /**
     * 获取当前时间
     * @returns {Date} 当前显示的时间
     */
    getCurrentTime() {
      return this.data.scrollCenterTime || new Date();
    },

    /**
     * 设置指定时间
     * @param {Date|string} time 要设置的时间
     */
    setTime(time) {
      const targetTime = new Date(time);
      this._jumpToTime(targetTime);
      this.triggerEvent("timeChange", { time: targetTime });
    },

    // ==================== 内部核心方法 ====================
    /**
     * 更新可录制区域背景
     * 根据当前实时时间计算可录制区域的宽度和样式
     */
    _updateRecordableBackground() {
      if (!this.data.baseTime) {
        this.setData({
          recordableWidth: 0,
          recordableGradient: "",
        });
        return;
      }

      // 使用当前实时时间作为可录制区域的边界
      const now = new Date();

      // 计算当前时间相对于baseTime的位置
      const diffMinutes = (now - this.data.baseTime) / (60 * 1000);
      const diffScales = diffMinutes / MINUTES_PER_SCALE;

      // 考虑左侧加载的偏移量，计算实际的当前时间位置
      const currentTimePosition =
        this._state.centerIndex * ITEM_WIDTH + diffScales * ITEM_WIDTH;

      // 可录制区域从时间轴的最左端开始，到当前实时时间位置结束
      // 当currentTimePosition为负数时（当前时间在baseTime之前），可录制区域为0
      const recordableWidth = Math.max(0, currentTimePosition);

      // 设置渐变背景：从透明到半透明绿色
      const recordableGradient = `linear-gradient(to right,
    rgba(76, 175, 80, 0.1) 0%,
    rgba(76, 175, 80, 0.2) 70%,
    rgba(76, 175, 80, 0.3) 100%)`;

      this.setData({
        recordableWidth,
        recordableGradient,
      });
    },

    /**
     * 初始化时间轴
     * 设置基准时间、视口宽度、生成初始刻度项
     */
    initRuler() {
      const now = this.properties.initialTime
        ? new Date(this.properties.initialTime)
        : new Date();
      const baseTime = new Date(now);
      // 将基准时间对齐到10分钟刻度
      baseTime.setMinutes(
        Math.floor(now.getMinutes() / MINUTES_PER_SCALE) * MINUTES_PER_SCALE,
      );
      baseTime.setSeconds(0);
      baseTime.setMilliseconds(0);

      const windowInfo = wx.getWindowInfo();
      this._state.viewportWidth = windowInfo.windowWidth * 2;
      this._state.centerIndex = Math.floor(INITIAL_ITEMS_COUNT / 2);

      // 计算当前时间相对于基准时间的偏移
      const diffMinutes = (now - baseTime) / (60 * 1000);
      this._state.centerOffset = diffMinutes / MINUTES_PER_SCALE;
      this._state.leftItemsCount = this._state.centerIndex;
      this._state.rightItemsCount =
        INITIAL_ITEMS_COUNT - this._state.centerIndex - 1;

      const contentWidth = INITIAL_ITEMS_COUNT * ITEM_WIDTH;

      this.setData(
        {
          baseTime: baseTime,
          contentWidth: contentWidth,
          currentTimeString: this._formatTime(now),
          isFollowingRealTime: true,
        },
        () => {
          this._generateInitialItems();

          // 计算当前时间的显示位置
          const viewportCenter = this._state.viewportWidth / 4;
          const centerTimePosition = this._state.centerIndex * ITEM_WIDTH;
          const currentTimeOffset = this._state.centerOffset * ITEM_WIDTH;
          const currentPosition = centerTimePosition + currentTimeOffset;

          this.setData(
            {
              currentTimePosition: currentPosition,
            },
            () => {
              const scrollLeft = Math.max(0, currentPosition - viewportCenter);
              this._state.isAutoScrolling = true;
              this.setData({ scrollLeft }, () => {
                this._state.isInitializing = false;
                this._startTimeUpdate();
                // 立即更新一次可录制区域
                this._updateRecordableBackground();
                // 启动可录制区域的定期更新
                this._startRecordableAreaUpdate();
              });
            },
          );
        },
      );
    },
    /**
     * 开始可录制区域定期更新
     * 每秒更新一次可录制区域，与时间更新同步
     */
    _startRecordableAreaUpdate() {
      // 在现有的时间更新定时器中同时更新可录制区域
      // 无需单独的定时器，修改 _updateTimeDisplay 方法即可
    },

    /**
     * 停止可录制区域更新
     */
    _stopRecordableAreaUpdate() {
      // 可录制区域更新会随着时间更新定时器一起停止
    },
    /**
     * 生成初始的时间刻度项
     * 创建200个时间刻度项，以中心索引为基准
     */
    _generateInitialItems() {
      const rulerItems = [];
      const centerIndex = this._state.centerIndex;

      for (let i = 0; i < INITIAL_ITEMS_COUNT; i++) {
        const offset = i - centerIndex;
        rulerItems.push(this._createTimeItem(offset));
      }

      this.setData({ rulerItems });
    },

    /**
     * 创建单个时间刻度项
     * @param {number} offset 相对于中心的偏移量
     * @returns {Object} 时间刻度项对象
     */
    _createTimeItem(offset) {
      const timeOffset = offset * MINUTES_PER_SCALE;
      const itemTime = new Date(this.data.baseTime);
      itemTime.setMinutes(itemTime.getMinutes() + timeOffset);

      const hour = itemTime.getHours();
      const minute = itemTime.getMinutes();
      const isHour = minute === 0; // 整点刻度
      const isHalfHour = minute === 30; // 半点刻度

      return {
        id: `time-${offset}`,
        position: (this._state.centerIndex + offset) * ITEM_WIDTH,
        isHour,
        isHalfHour,
        showLabel: isHour,
        label: isHour
          ? `${String(hour).padStart(2, "0")}:00`
          : `${String(hour).padStart(2, "0")}:30`,
        time: itemTime,
        offset,
      };
    },

    /**
     * 恢复播放
     * 从暂停状态或滚动状态恢复播放
     */
    _resumePlay() {
      const currentDisplayTime = this._parseTimeString(
        this.data.currentTimeString,
      );

      this.setData({
        isPlaying: true,
        isFollowingRealTime: false,
        scrollCenterTime: currentDisplayTime,
      });

      this._state.scrollStopTime = new Date();
      this._state.pausedTime = null;
      this._startTimeUpdate();
    },

    /**
     * 暂停播放
     * 保存当前播放状态，停止时间更新
     */
    _pausePlay() {
      const now = new Date();
      let pausedTime;

      // 计算暂停时的精确时间
      if (this.data.isFollowingRealTime) {
        pausedTime = now;
      } else {
        let baseTime = this._state.pausedTime
          ? now
          : this._state.scrollStopTime || now;
        let centerTime = this.data.scrollCenterTime || now;
        const timePassed = now.getTime() - baseTime.getTime();
        pausedTime = new Date(centerTime.getTime() + timePassed);
      }

      this.setData({
        isPlaying: false,
        isFollowingRealTime: false, // 添加这行，确保不再跟随实时时间
        scrollCenterTime: pausedTime,
      });

      this._state.pausedTime = pausedTime;
      this._stopTimeUpdate();

      // 添加这两行事件触发，让父组件知道需要显示回到最新时间按钮
      this.triggerEvent("playStateChange", { isPlaying: false });
      this.triggerEvent("scrollEnd", {
        time: pausedTime,
        needBackToLatest: true,
      });
    },

    /**
     * 重置到实时时间
     * 跳转到当前实时时间并开始跟随
     */
    _resetToRealTime() {
      this._state.isScrolling = false;

      this.setData({
        isFollowingRealTime: true,
        isPlaying: true,
      });

      const now = new Date();
      this._updateIndicatorPosition(now);

      // 滚动到实时位置
      const scrollLeft = Math.max(
        0,
        this.data.currentTimePosition - this._state.viewportWidth / 4,
      );
      this._state.isAutoScrolling = true;
      this.setData({ scrollLeft });

      this._startTimeUpdate();

      // 添加这两行，确保父页面状态同步
      this.triggerEvent("playStateChange", { isPlaying: true });
      this.triggerEvent("resetToRealTime", { time: now });
    },

    /**
     * 跳转到指定时间
     * @param {Date} targetTime 目标时间
     */
    _jumpToTime(targetTime) {
      const diffMinutes = (targetTime - this.data.baseTime) / (60 * 1000);
      const diffScales = diffMinutes / MINUTES_PER_SCALE;
      const targetPosition =
        this._state.centerIndex * ITEM_WIDTH + diffScales * ITEM_WIDTH;

      this.setData({
        currentTimePosition: targetPosition,
        scrollCenterTime: targetTime,
        isFollowingRealTime: false,
        isPlaying: false,
      });

      const scrollLeft = Math.max(
        0,
        targetPosition - this._state.viewportWidth / 4,
      );
      this._state.isAutoScrolling = true;
      this.setData({ scrollLeft });
    },

    /**
     * 处理滚动事件
     * @param {Object} e 滚动事件对象
     */
    handleScroll(e) {
      const scrollLeft = e.detail.scrollLeft;

      // 忽略自动滚动
      if (this._state.isAutoScrolling) {
        this._state.isAutoScrolling = false;
        return;
      }

      // 开始手动滚动
      if (!this._state.isScrolling) {
        this._state.isScrolling = true;
        this.setData({
          isFollowingRealTime: false,
          isScrolling: true,
        });

        // 添加这行：触发拖拽开始事件
        this.triggerEvent("dragStart");

        this.triggerEvent("scrollStart", { scrollLeft });
      }

      // 清除之前的滚动停止检测
      if (this._state.scrollTimeoutID) {
        clearTimeout(this._state.scrollTimeoutID);
      }

      // 更新显示时间
      const centerTime = this._calculateScrollCenterTime(scrollLeft);
      const newTimeString = this._formatTime(centerTime);

      if (newTimeString !== this.data.currentTimeString) {
        this.setData({
          currentTimeString: newTimeString,
          scrollCenterTime: centerTime,
        });
        this.triggerEvent("timeChange", { time: centerTime });
      }

      // 设置滚动停止检测
      this._state.scrollTimeoutID = setTimeout(() => {
        this._handleScrollStop(scrollLeft);
      }, SCROLL_TIMEOUT);

      // 如果正在录制，触发录制区域更新事件
      if (this.data.showRecordingArea) {
        const centerTime = this._calculateScrollCenterTime(scrollLeft);
        this.triggerEvent("recordingAreaUpdate", {
          centerTime: centerTime,
          scrollLeft: scrollLeft,
        });
      }
      // 检查是否需要加载更多内容
      this._checkAndLoadMore(scrollLeft);
    },

    /**
     * 处理滚动停止事件
     * @param {number} scrollLeft 滚动位置
     */
    _handleScrollStop(scrollLeft) {
      const centerTime = this._calculateScrollCenterTime(scrollLeft);
      const now = new Date();
      this._state.scrollStopTime = new Date();
      this._state.isScrolling = false;

      this.setData({
        isScrolling: false,
        scrollCenterTime: centerTime,
      });

      // 初始化阶段特殊处理
      if (this._state.isInitializing) {
        this.setData({
          isFollowingRealTime: true,
          isPlaying: true,
        });
        return;
      }

      // 如果滚动到实时时间或之后，自动重置到实时
      if (centerTime >= now) {
        this._resetToRealTime();
        // 触发scrollEnd事件，不需要显示回到最新按钮
        this.triggerEvent("scrollEnd", {
          scrollCenterTime: centerTime,
          needBackToLatest: false,
        });
        return;
      }

      // 停在历史时间，暂停播放
      this.setData({
        isFollowingRealTime: false,
        isPlaying: false,
      });

      this._state.pausedTime = centerTime;
      this._stopTimeUpdate();

      // 触发播放状态变化事件和滚动结束事件
      this.triggerEvent("playStateChange", { isPlaying: false });
      this.triggerEvent("scrollEnd", {
        scrollCenterTime: centerTime,
        needBackToLatest: true,
      });
    },

    // ==================== 时间更新相关方法 ====================

    /**
     * 开始时间更新定时器
     * 每秒更新一次显示时间
     */
    _startTimeUpdate() {
      this._stopTimeUpdate();
      this._updateTimeDisplay();
      this._state.timeUpdateTimer = setInterval(() => {
        this._updateTimeDisplay();
      }, 1000);
    },

    /**
     * 停止时间更新定时器
     */
    _stopTimeUpdate() {
      if (this._state.timeUpdateTimer) {
        clearInterval(this._state.timeUpdateTimer);
        this._state.timeUpdateTimer = null;
      }
    },

    /**
     * 清理所有定时器
     */
    _clearTimers() {
      this._stopTimeUpdate();
      if (this._state.scrollTimeoutID) {
        clearTimeout(this._state.scrollTimeoutID);
        this._state.scrollTimeoutID = null;
      }
    },

    /**
     * 更新时间显示
     * 计算当前应该显示的时间并更新指示器位置
     */
    _updateTimeDisplay() {
      if (this._state.isScrolling) return;

      const now = new Date();
      let displayTime;

      // 根据当前状态计算显示时间
      if (this.data.isFollowingRealTime) {
        displayTime = now;
      } else {
        let baseTime =
          this._state.playStartTime || this._state.scrollStopTime || now;
        let centerTime =
          this._state.playStartCenterTime || this.data.scrollCenterTime || now;
        const timePassed = now.getTime() - baseTime.getTime();
        displayTime = new Date(centerTime.getTime() + timePassed);
      }

      this._updateIndicatorPosition(displayTime);
      this.setData({
        currentTimeString: this._formatTime(displayTime),
      });
      // 每次时间更新时都更新可录制区域背景
      this._updateRecordableBackground();
    },

    /**
     * 更新时间指示器位置
     * @param {Date} time 要显示的时间
     */
    _updateIndicatorPosition(time) {
      const diffMinutes = (time - this.data.baseTime) / (60 * 1000);
      const diffScales = diffMinutes / MINUTES_PER_SCALE;
      const currentPosition =
        this._state.centerIndex * ITEM_WIDTH + diffScales * ITEM_WIDTH;

      this.setData({ currentTimePosition: currentPosition });

      // 实时模式下自动滚动跟随
      if (this.data.isFollowingRealTime && !this._state.isScrolling) {
        const newScrollLeft = Math.max(
          0,
          currentPosition - this._state.viewportWidth / 4,
        );
        if (Math.abs(newScrollLeft - this.data.scrollLeft) > 1) {
          this._state.isAutoScrolling = true;
          this.setData({ scrollLeft: newScrollLeft });
        }
      }
    },

    /**
     * 根据滚动位置计算中心时间
     * @param {number} scrollLeft 滚动位置
     * @returns {Date} 计算出的中心时间
     */
    _calculateScrollCenterTime(scrollLeft) {
      if (!this.data.baseTime) return new Date();

      const centerPosition = scrollLeft + this._state.viewportWidth / 4;
      const rpxPerMinute = ITEM_WIDTH / MINUTES_PER_SCALE;
      const minutesFromBase =
        (centerPosition - this._state.centerIndex * ITEM_WIDTH) / rpxPerMinute;

      return new Date(
        this.data.baseTime.getTime() + minutesFromBase * 60 * 1000,
      );
    },

    // ==================== 工具方法 ====================

    /**
     * 格式化时间为字符串
     * @param {Date} date 要格式化的时间
     * @returns {string} 格式化后的时间字符串 (YYYY-MM-DD HH:mm:ss)
     */
    _formatTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${hours}:${minutes}:${seconds}`;
    },

    /**
     * 解析时间字符串为Date对象
     * @param {string} timeString 时间字符串 (HH:mm 或 YYYY-MM-DD HH:mm:ss)
     * @returns {Date} 解析后的Date对象
     */
    _parseTimeString(timeString) {
      if (!timeString) return new Date();
      // 检查是否包含日期部分
      if (timeString.includes(" ")) {
        // 完整的日期时间字符串格式：YYYY-MM-DD HH:mm:ss
        const [datePart, timePart] = timeString.split(" ");
        const [year, month, day] = datePart.split("-").map(Number);
        const [hours, minutes, seconds = 0] = timePart.split(":").map(Number);
        return new Date(year, month - 1, day, hours, minutes, seconds);
      } else {
        // 只有时间部分：HH:mm
        const [hours, minutes] = timeString.split(":").map(Number);
        const today = new Date();
        return new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate(),
          hours,
          minutes,
          0,
        );
      }
    },

    // ==================== 动态加载相关方法 ====================

    /**
     * 检查并加载更多内容
     * @param {number} scrollLeft 当前滚动位置
     */
    _checkAndLoadMore(scrollLeft) {
      const rightEdge = scrollLeft + this._state.viewportWidth;

      // 接近左边界时加载更多左侧内容
      if (scrollLeft < ITEM_WIDTH * LOAD_MORE_THRESHOLD) {
        this._loadMoreLeft();
      }

      // 接近右边界时加载更多右侧内容
      if (
        rightEdge >
        this.data.contentWidth - ITEM_WIDTH * LOAD_MORE_THRESHOLD
      ) {
        this._loadMoreRight();
      }
    },

    /**
     * 向左侧加载更多时间刻度
     * 在左边添加40个新的时间刻度项
     */
    _loadMoreLeft() {
      if (this._state.isLoadingMore) return;
      this._state.isLoadingMore = true;

      const newItems = [];
      const currentOffset = -this._state.leftItemsCount - 1;

      // 创建新的刻度项
      for (let i = 0; i < LOAD_MORE_COUNT; i++) {
        const offset = currentOffset - i;
        newItems.unshift(this._createTimeItem(offset));
      }

      this._state.leftItemsCount += LOAD_MORE_COUNT;
      this._state.centerIndex += LOAD_MORE_COUNT;

      // 更新所有项目的位置
      const updatedItems = [...newItems, ...this.data.rulerItems].map(
        (item) => ({
          ...item,
          position: item.position + LOAD_MORE_COUNT * ITEM_WIDTH,
        }),
      );

      this.setData(
        {
          rulerItems: updatedItems,
          contentWidth: this.data.contentWidth + LOAD_MORE_COUNT * ITEM_WIDTH,
          scrollLeft: this.data.scrollLeft + LOAD_MORE_COUNT * ITEM_WIDTH,
          currentTimePosition:
            this.data.currentTimePosition + LOAD_MORE_COUNT * ITEM_WIDTH,
        },
        () => {
          this._state.isLoadingMore = false;
          // 左侧加载后更新可录制区域背景
          this._updateRecordableBackground();
        },
      );
    },

    /**
     * 向右侧加载更多时间刻度
     * 在右边添加40个新的时间刻度项
     */
    _loadMoreRight() {
      if (this._state.isLoadingMore) return;
      this._state.isLoadingMore = true;

      const newItems = [];
      const currentOffset = this._state.rightItemsCount + 1;

      // 创建新的刻度项
      for (let i = 0; i < LOAD_MORE_COUNT; i++) {
        const offset = currentOffset + i;
        newItems.push(this._createTimeItem(offset));
      }

      this._state.rightItemsCount += LOAD_MORE_COUNT;

      this.setData(
        {
          rulerItems: [...this.data.rulerItems, ...newItems],
          contentWidth: this.data.contentWidth + LOAD_MORE_COUNT * ITEM_WIDTH,
        },
        () => {
          this._state.isLoadingMore = false;
          // 右侧加载后更新可录制区域背景
          this._updateRecordableBackground();
        },
      );
    },
  },
});
