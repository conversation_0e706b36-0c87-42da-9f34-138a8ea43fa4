const app = getApp();
import {
  querySiteDetailApi,
  querySitDevLivingListApi,
  liveReplay,
  getDeviceStatus,
} from "@/api/site";
import {
  addWatchLiveRecordApi,
  slicingTaskPage,
  submitSlicingTask,
  deleteSlicingTask,
  acquiringRights,
} from "@/api/live";

Page({
  data: {
    // ==================== 页面布局相关 ====================
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "",
      color: "#ffffff",
    },

    // ==================== 摄像头相关 ====================
    cameraList: [], // 摄像头列表
    activeId: null, // 当前选中的摄像头ID

    // ==================== 视频播放相关 ====================
    videoUrl: "", // 视频播放地址
    isMuted: false, // 是否静音
    isPlaying: true, // 是否正在播放
    isDragging: false, // 是否正在拖拽时间轴
    showVideo: true, // 是否显示视频组件

    // ==================== 站点和用户相关 ====================
    siteDetail: {}, // 站点详情
    id: null, // 站点ID
    shareUrl: "", // 分享链接

    // ==================== 播放统计相关 ====================
    playStartTime: null, // 播放开始时间
    lastCameraId: null, // 上一个播放的摄像头ID

    // ==================== 定时器统一管理 ====================
    timers: {
      status: null, // 状态轮询定时器
      recording: null, // 录制监控定时器
    },

    // ==================== 录制相关 ====================
    isRecording: false, // 是否正在录制
    recordTime1: null, // 录制开始时间
    rightsTimer: null, // 权益倒计时定时器

    // ==================== 录像列表相关 ====================
    videoList: [], // 录像列表

    // ==================== 滑动删除相关 - 合并触摸状态 ====================
    swipeDeleteItemId: null, // 当前显示删除按钮的录像ID
    touchState: {
      startTime: null, // 触摸开始时间
      startX: null, // 触摸开始X坐标
      startY: null, // 触摸开始Y坐标
      moved: false, // 是否已移动
    },

    // ==================== 权益相关 ====================
    userRights: {
      total: 0, // 总权益时长（秒）
      remain: 0, // 剩余权益时长（秒）
      remainFormatted: null, // 格式化后的剩余时长显示
    },
    _isLoadingVideoList: false, // 防重复加载标志
  },

  /**
   * 页面加载时的初始化逻辑
   * @param {Object} options 页面参数
   */
  async onLoad(options) {
    wx.showLoading({ title: "加载中...", mask: true });
    try {
      await this._initPageData(options);
      await this._initFirstCamera();
      // 延迟启动轮询，避免与初始化冲突
      setTimeout(() => {
        this.startDevicePolling(this.data.siteDetail.siteCode);
      }, 1000);
    } catch (error) {
      console.error("页面初始化失败:", error);
      wx.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 页面卸载时的清理工作
   */
  onUnload() {
    this._cleanupOnExit();
    this._clearAllTimers();
  },

  // ==================== 优化后的定时器管理 ====================

  /**
   * 清除所有定时器
   */
  _clearAllTimers() {
    Object.keys(this.data.timers).forEach((key) => {
      if (this.data.timers[key]) {
        clearInterval(this.data.timers[key]);
      }
    });

    // 清除权益倒计时定时器
    if (this.data.rightsTimer) {
      clearInterval(this.data.rightsTimer);
    }

    this.setData({
      timers: {
        status: null,
        recording: null,
      },
      rightsTimer: null,
    });
  },

  /**
   * 设置指定名称的定时器
   * @param {string} name 定时器名称
   * @param {number} timer 定时器ID
   */
  _setTimer(name, timer) {
    const timers = { ...this.data.timers };
    if (timers[name]) {
      clearInterval(timers[name]);
    }
    timers[name] = timer;
    this.setData({ timers });
  },

  /**
   * 清除指定名称的定时器
   * @param {string} name 定时器名称
   */
  _clearTimer(name) {
    if (this.data.timers[name]) {
      clearInterval(this.data.timers[name]);
      const timers = { ...this.data.timers };
      timers[name] = null;
      this.setData({ timers });
    }
  },

  // ==================== 权益查询方法 ====================

  /**
   * 检查用户切片权益
   * @returns {Object|null} 权益信息
   */
  async checkUserRights() {
    try {
      const { code, data, msg } = await acquiringRights({ 1: "1" });

      if (code === 0) {
        const remainSeconds = data.remain || 0;
        this.setData({
          userRights: {
            total: data.total || 0,
            remain: remainSeconds,
            remainFormatted: this._formatSecondsToTime(remainSeconds),
          },
        });
        return data;
      } else {
        console.error("查询权益失败:", msg);
        return null;
      }
    } catch (error) {
      console.error("权益查询接口调用失败:", error);
      return null;
    }
  },

  /**
   * 格式化秒数为时间显示格式 HH:MM:SS
   * @param {number} seconds 秒数
   * @returns {string} 格式化后的时间字符串
   */
  _formatSecondsToTime(seconds) {
    if (!seconds || seconds <= 0) return "00:00:00";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60) % 60;
    const remainingSeconds = seconds % 60;
    const pad = (n) => n.toString().padStart(2, "0");
    return `${pad(hours)}:${pad(minutes)}:${pad(remainingSeconds)}`;
  },

  /**
   * 初始化第一个摄像头
   */
  async _initFirstCamera() {
    const firstCamera = this.data.cameraList[0];
    if (firstCamera) {
      try {
        await this.handleTap({
          currentTarget: {
            dataset: { id: firstCamera.id },
          },
        });
      } catch (error) {
        console.error("初始化首个摄像头失败:", error);
      }
    }
  },

  // ==================== 录像列表相关方法 ====================

  /**
   * 获取录像列表（防重复调用版本）
   * @returns {Array} 录像列表
   */
  async getVideoList() {
    // 防止重复调用
    if (this._isLoadingVideoList) {
      console.log("录像列表正在加载中，跳过重复请求");
      return this.data.videoList || [];
    }

    if (!this.data.activeId) {
      console.warn("未选择设备，无法获取录像列表");
      this.setData({ videoList: [] });
      return [];
    }

    this._isLoadingVideoList = true;

    try {
      // 非录制状态下才检查权益，避免频繁请求
      if (!this.data.isRecording) {
        await this.checkUserRights();
      }

      const res = await slicingTaskPage({
        devId: this.data.activeId,
        current: 1,
        size: 1000,
      });

      const videoList = this._handleVideoListResponseOptimized(res);
      this._performDifferentialUpdate(videoList);
      return videoList;
    } catch (error) {
      console.error("获取录像列表失败:", error);
      return this._handleVideoListError(error);
    } finally {
      this._isLoadingVideoList = false;
    }
  },

  /**
   * 处理录像列表响应数据（优化版）
   * @param {Object} res API响应数据
   * @returns {Array} 处理后的录像列表
   */
  _handleVideoListResponseOptimized(res) {
    if (res.code === 0 && res.data && res.data.list) {
      const newRawList = res.data.list;
      const newProcessedList = this._processVideoListData(newRawList);
      const newSortedList = this._sortVideoList(newProcessedList);

      this._performDifferentialUpdate(newSortedList);
      return newSortedList;
    } else {
      console.warn("获取录像列表失败:", res.msg);
      this.setData({ videoList: [] });
      return [];
    }
  },

  /**
   * 重试切片任务
   * @param {Object} e 事件对象
   */
  async retrySlicingTask(e) {
    const { id, startTime, endTime } = e.currentTarget.dataset;

    if (!id || !startTime || !endTime) {
      wx.showToast({
        title: "参数错误，无法重试",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    // 重试前校验权益
    const rights = await this.checkUserRights();
    if (!rights || rights.remain <= 0) {
      this.showNoRightsModal(rights);
      return;
    }

    wx.vibrateShort({ type: "light" });

    wx.showModal({
      title: "重试确认",
      content: "确定要重新提交切片任务吗？",
      confirmText: "重试",
      cancelText: "取消",
      confirmColor: "#ff6b6b",
      success: async (res) => {
        if (res.confirm) {
          await this._executeRetrySlicing(id, startTime, endTime);
        }
      },
    });
  },

  /**
   * 执行重试切片任务
   * @param {string} taskId 任务ID
   * @param {number} startTime 开始时间
   * @param {number} endTime 结束时间
   */
  async _executeRetrySlicing(taskId, startTime, endTime) {
    wx.showLoading({
      title: "重新提交中...",
      mask: true,
    });

    try {
      const { code, data, msg } = await submitSlicingTask({
        id: this.data.activeId,
        startTime: startTime,
        endTime: endTime,
      });
      if (code !== 0) {
        return;
      }
      wx.showToast({
        title: "重试发起录制成功",
        icon: "success",
        duration: 2000,
      });
      // 删除原失败任务并刷新列表
      await deleteSlicingTask({ id: taskId });
      await this.getVideoList();
    } catch (error) {
      console.error("重试切片任务失败:", error);
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 执行差异更新录像列表（优化动画）
   * @param {Array} newList 新的录像列表
   */
  _performDifferentialUpdate(newList) {
    const currentList = this.data.videoList;

    // 如果当前列表为空，直接设置新列表，不触发动画
    if (!currentList || currentList.length === 0) {
      const initialList = newList.map((item) => ({
        ...this._transformVideoItem(item),
        isNew: false, // 初始加载不显示新增动画
      }));

      this.setData({ videoList: initialList });
      return;
    }

    // 检查是否真的有变化
    if (this._isListIdentical(currentList, newList)) {
      // console.log('录像列表无变化，跳过更新');
      return;
    }

    // 分析差异并应用更新
    const currentMap = new Map(currentList.map((item) => [item.id, item]));
    const newMap = new Map(newList.map((item) => [item.id, item]));
    const updates = this._analyzeDifferences(currentMap, newMap);
    this._applyUpdates(updates, newList);
  },

  /**
   * 检查列表是否完全相同
   * @param {Array} currentList 当前列表
   * @param {Array} newList 新列表
   * @returns {boolean} 是否相同
   */
  _isListIdentical(currentList, newList) {
    if (currentList.length !== newList.length) {
      return false;
    }

    const currentMap = new Map(currentList.map((item) => [item.id, item]));

    for (const newItem of newList) {
      const currentItem = currentMap.get(newItem.id);
      if (!currentItem) {
        return false;
      }

      // 检查关键字段是否有变化
      const keyFields = [
        "sliceStatue",
        "coverKey",
        "objectKey",
        "startTime",
        "endTime",
      ];
      for (const field of keyFields) {
        if (currentItem[field] !== newItem[field]) {
          return false;
        }
      }
    }

    return true;
  },

  /**
   * 分析录像列表差异
   * @param {Map} currentMap 当前列表映射
   * @param {Map} newMap 新列表映射
   * @returns {Object} 差异分析结果
   */
  _analyzeDifferences(currentMap, newMap) {
    const updates = {
      toAdd: [], // 新增项
      toUpdate: [], // 更新项
      toRemove: [], // 删除项
      unchanged: [], // 未变化项
    };

    // 分析新增和更新
    for (const [id, newItem] of newMap) {
      const currentItem = currentMap.get(id);

      if (!currentItem) {
        updates.toAdd.push({ ...newItem, isNew: true, isUpdating: false });
      } else {
        const hasChanges = this._checkItemChanges(currentItem, newItem);

        if (hasChanges.hasChange) {
          updates.toUpdate.push({
            ...newItem,
            isUpdated: true,
            isUpdating: true,
            statusChangeType: hasChanges.changeType,
            previousStatus: currentItem.sliceStatue,
          });
        } else {
          updates.unchanged.push({
            ...currentItem,
            isUpdated: false,
            isUpdating: false,
          });
        }
      }
    }

    // 分析删除
    for (const [id, currentItem] of currentMap) {
      if (!newMap.has(id)) {
        updates.toRemove.push(id);
      }
    }

    return updates;
  },

  /**
   * 检查录像项的变化
   * @param {Object} currentItem 当前项
   * @param {Object} newItem 新项
   * @returns {Object} 变化检查结果
   */
  _checkItemChanges(currentItem, newItem) {
    const result = {
      hasChange: false,
      changeType: null,
    };

    const keyFields = ["sliceStatue", "coverKey", "objectKey"];

    for (const field of keyFields) {
      if (currentItem[field] !== newItem[field]) {
        result.hasChange = true;

        if (field === "sliceStatue") {
          result.changeType = this._getStatusChangeType(
            currentItem.sliceStatue,
            newItem.sliceStatue,
          );
        } else if (field === "coverKey") {
          result.changeType = "thumbnail";
          newItem.thumbnail = this._processThumbnailUrl(newItem.coverKey);
        }
        break;
      }
    }

    // 特殊处理：切片成功状态
    if (newItem.sliceStatue === 20 && currentItem.sliceStatue !== 20) {
      result.hasChange = true;
      result.changeType = "success";
      newItem.thumbnail = this._processThumbnailUrl(newItem.coverKey);
    }

    return result;
  },

  /**
   * 获取状态变化类型
   * @param {number} oldStatus 旧状态
   * @param {number} newStatus 新状态
   * @returns {string|null} 状态变化类型
   */
  _getStatusChangeType(oldStatus, newStatus) {
    if (newStatus === 20 && oldStatus !== 20) {
      return "success";
    } else if (newStatus === 40 && oldStatus !== 40) {
      return "failed";
    }
    return null;
  },

  /**
   * 应用更新到录像列表（减少动画闪烁）
   * @param {Object} updates 更新数据
   * @param {Array} newList 新列表
   */
  _applyUpdates(updates, newList) {
    const { toAdd, toUpdate, toRemove, unchanged } = updates;

    if (toAdd.length === 0 && toUpdate.length === 0 && toRemove.length === 0) {
      return;
    }

    console.log("录像列表差异:", {
      新增: toAdd.length,
      更新: toUpdate.length,
      删除: toRemove.length,
      未变化: unchanged.length,
    });

    const finalList = this._buildFinalList(updates, newList);

    // 使用 wx.nextTick 确保更新在下一个事件循环中执行
    wx.nextTick(() => {
      this.setData({ videoList: finalList }, () => {
        // 只有真正有状态变化时才移除更新标志
        if (toUpdate.some((item) => item.statusChangeType)) {
          this._removeUpdateFlags();
        }
      });
    });
  },

  /**
   * 构建最终的录像列表
   * @param {Object} updates 更新数据
   * @param {Array} newList 新列表
   * @returns {Array} 最终列表
   */
  _buildFinalList(updates, newList) {
    const { toAdd, toUpdate, toRemove, unchanged } = updates;

    const updateMap = new Map(toUpdate.map((item) => [item.id, item]));
    const addMap = new Map(toAdd.map((item) => [item.id, item]));
    const unchangedMap = new Map(unchanged.map((item) => [item.id, item]));
    const removeSet = new Set(toRemove);

    return newList
      .map((newItem) => {
        const id = newItem.id;

        if (removeSet.has(id)) {
          return null;
        } else if (updateMap.has(id)) {
          return updateMap.get(id);
        } else if (addMap.has(id)) {
          return addMap.get(id);
        } else if (unchangedMap.has(id)) {
          return unchangedMap.get(id);
        } else {
          return { ...this._transformVideoItem(newItem), isUpdating: false };
        }
      })
      .filter((item) => item !== null);
  },

  /**
   * 移除更新标志
   */
  _removeUpdateFlags() {
    setTimeout(() => {
      const updatedList = this.data.videoList.map((item) => ({
        ...item,
        isNew: false,
        isUpdated: false,
        isUpdating: false,
        statusChangeType: undefined,
        previousStatus: undefined,
      }));

      this.setData({ videoList: updatedList });
    }, 1500);
  },

  /**
   * 转换录像项数据
   * @param {Object} item 原始录像项
   * @returns {Object} 转换后的录像项
   */
  _transformVideoItem(item) {
    const processedItem = {
      ...item,
      name: `录像${item.id}`,
      thumbnail: this._processThumbnailUrl(item.coverKey),
      isNew: false,
      isUpdated: false,
      isUpdating: false,
    };

    processedItem.startTimeDisplay = this.formatSecondsToDateTime(
      item.startTime,
    );
    processedItem.endTimeDisplay = this.formatSecondsToDateTime(item.endTime);
    processedItem.duration = this.calculateDurationDisplay(
      item.startTime,
      item.endTime,
    );

    return processedItem;
  },

  /**
   * 处理缩略图URL
   * @param {string} coverKey 封面键值
   * @returns {string} 处理后的缩略图URL
   */
  _processThumbnailUrl(coverKey) {
    if (!coverKey) {
      return "/images/live/slice.png";
    }

    const separator = coverKey.includes("?") ? "&" : "?";
    return `${coverKey}${separator}t=${Date.now()}`;
  },

  /**
   * 缩略图加载失败处理
   * @param {Object} e 事件对象
   */
  onThumbnailError(e) {
    const { id, index } = e.currentTarget.dataset;
    const videoList = this.data.videoList;
    if (videoList[index]) {
      const updatePath = `videoList[${index}].thumbnail`;
      this.setData({
        [updatePath]: "/images/live/slice.png",
      });
    }
  },

  /**
   * 处理录像列表错误（改进版）
   * @param {Error} error 错误对象
   * @returns {Array} 当前录像列表
   */
  _handleVideoListError(error) {
    console.error("获取录像列表请求失败:", error);
    // 保留当前列表，而不是直接清空
    return this.data.videoList || [];
  },

  /**
   * 处理录像列表原始数据
   * @param {Array} rawList 原始数据列表
   * @returns {Array} 处理后的列表
   */
  _processVideoListData(rawList) {
    return rawList
      .map((item) => this._transformVideoItem(item))
      .filter((item) => item.sliceStatue !== undefined);
  },

  /**
   * 对录像列表进行排序
   * @param {Array} videoList 录像列表
   * @returns {Array} 排序后的列表
   */
  _sortVideoList(videoList) {
    return videoList.sort((a, b) => b.id - a.id);
  },

  /**
   * 格式化秒数为日期时间显示
   * @param {number} seconds 秒数时间戳
   * @returns {string} 格式化后的日期时间
   */
  formatSecondsToDateTime(seconds) {
    if (!seconds || seconds <= 0) {
      return "--";
    }

    const date = new Date(seconds * 1000);
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const secs = String(date.getSeconds()).padStart(2, "0");

    return `${month}-${day} ${hours}:${minutes}:${secs}`;
  },

  /**
   * 计算时长显示
   * @param {number} startTime 开始时间
   * @param {number} endTime 结束时间
   * @returns {string} 时长显示字符串
   */
  calculateDurationDisplay(startTime, endTime) {
    if (!startTime || !endTime || endTime <= startTime) {
      return "未知时长";
    }
    const durationSeconds = endTime - startTime;
    const minutes = Math.floor(durationSeconds / 60);
    const seconds = durationSeconds % 60;
    return `${minutes}'${seconds.toString().padStart(2, "0")}"`;
  },

  /**
   * 初始化分享功能
   * @param {string} id 站点ID
   */
  shareInit(id) {
    const shareUrl = `/play-live/living-mult/index?pCode=${app.globalData.user.userCode}&id=${id}`;
    this.setData({ shareUrl, id });
  },

  /**
   * 初始化页面数据
   * @param {Object} options 页面参数
   */
  async _initPageData(options) {
    const { id } = options;
    this.shareInit(id);

    try {
      // 获取站点详情
      const siteDetailRes = await querySiteDetailApi({ id });
      const { siteName, siteCode } = siteDetailRes.data;

      // 获取设备列表
      const deviceListRes = await querySitDevLivingListApi({
        siteCode: siteCode,
        pageNo: 1,
        pageSize: 100,
      });

      const { list } = deviceListRes.data;
      if (!list || list.length === 0) {
        throw new Error("暂无可用设备");
      }

      this.setData({
        siteDetail: siteDetailRes.data,
        cameraList: list,
        navData: {
          title: siteName,
          color: "#ffffff",
        },
      });

      // 启动设备状态轮询
      this.startDevicePolling(siteCode);
    } catch (error) {
      console.error("初始化页面数据失败:", error);
      throw error;
    }
  },

  /**
   * 退出时的清理工作
   */
  _cleanupOnExit() {
    this.reportPlayTime();
  },

  // ==================== 视频相关方法 ====================

  /**
   * 处理摄像头点击事件
   * @param {Object} event 点击事件对象
   */
  async handleTap({
    currentTarget: {
      dataset: { id },
    },
  }) {
    // 切换到当前已选摄像头时不做任何操作
    if (id === this.data.activeId) return;

    // 切换摄像头时如在录制中，先提示用户
    if (this.data.isRecording) {
      wx.showModal({
        title: "正在录制",
        content: "切换摄像头将终止当前录制，是否确认切换？",
        confirmText: "切换",
        cancelText: "继续",
        confirmColor: "#ff6b6b",
        success: async (res) => {
          if (res.confirm) {
            await this.handleRecordComplete();
            await this._switchCamera(id);
          }
        },
      });
      return;
    }
    await this._switchCamera(id);
  },

  /**
   * 摄像头切换实际逻辑
   * @param {string} id 摄像头ID
   */
  async _switchCamera(id) {
    // 切换摄像头时清除录制相关定时器
    this._clearTimer("recording");
    this.clearRightsTimer();
    this.reportPlayTime();

    const camera = this.data.cameraList.find((item) => item.id === id);
    this.setData({ activeId: id });

    try {
      this._forceUpdateVideo(camera.liveUrl);
      this.setData({
        playStartTime: Date.now(),
        lastCameraId: id,
      });
      await this.getVideoList();
    } catch (error) {
      console.error("切换摄像头失败:", error);
      wx.showToast({
        title: "切换失败",
        icon: "none",
      });
    }
  },

  /**
   * 切换静音状态
   */
  changeMute() {
    this.setData({
      isMuted: !this.data.isMuted,
    });
  },

  // ==================== 优化后的设备轮询 ====================

  /**
   * 启动设备状态轮询
   * @param {string} siteCode 站点编码
   */
  startDevicePolling(siteCode) {
    this._clearTimer("status");
    const timer = setInterval(async () => {
      try {
        await Promise.all([
          this.pollCameraList(siteCode),
          this.pollVideoList(),
        ]);
      } catch (error) {
        console.error("轮询过程中出错:", error);
      }
    }, 3000); // 增加轮询间隔到3秒

    this._setTimer("status", timer);
  },

  /**
   * 轮询录像列表（防抖版本）
   */
  async pollVideoList() {
    // 如果正在加载或录制中，跳过轮询
    if (this._isLoadingVideoList || this.data.isRecording) {
      return;
    }

    try {
      await this.getVideoList();
    } catch (error) {
      console.error("轮询录像列表失败:", error);
    }
  },

  /**
   * 轮询摄像头列表
   * @param {string} siteCode 站点编码
   */
  async pollCameraList(siteCode) {
    try {
      const res = await querySitDevLivingListApi({
        siteCode: siteCode,
        pageNo: 1,
        pageSize: 100,
      });

      const newList = res.data.list;
      const needUpdate = this._checkIfNeedUpdate(newList);

      if (needUpdate) {
        this._updateCameraList(newList);
      }
    } catch (error) {
      console.error("轮询摄像头列表失败:", error);
      throw error;
    }
  },

  /**
   * 检查是否需要更新摄像头列表
   * @param {Array} newList 新的摄像头列表
   * @returns {boolean} 是否需要更新
   */
  _checkIfNeedUpdate(newList) {
    const currentList = this.data.cameraList;

    if (newList.length !== currentList.length) {
      return true;
    }

    const currentIds = currentList.map((item) => item.id);
    for (let i = 0; i < newList.length; i++) {
      if (!currentIds.includes(newList[i].id)) {
        return true;
      }
    }

    return false;
  },

  /**
   * 更新摄像头列表
   * @param {Array} newList 新的摄像头列表
   */
  _updateCameraList(newList) {
    const currentActiveId = this.data.activeId;

    this.setData({ cameraList: newList });

    const activeCamera = newList.find((item) => item.id === currentActiveId);

    if (activeCamera) {
      this.setData({ videoUrl: activeCamera.liveUrl });
    } else if (newList.length > 0) {
      this._initFirstCamera(newList);
      this.setData({
        cameraList: newList,
        videoUrl: newList[0].liveUrl,
        activeId: newList[0].id,
        playStartTime: Date.now(),
        lastCameraId: newList[0].id,
      });
    }
  },

  // ==================== 播放统计相关方法 ====================

  /**
   * 上报播放时长
   */
  reportPlayTime() {
    if (this.data.playStartTime && this.data.lastCameraId) {
      const playSeconds = Math.floor(
        (Date.now() - this.data.playStartTime) / 1000,
      );

      if (playSeconds > 0) {
        this._submitPlayRecord(playSeconds);
      }
    }
  },

  /**
   * 提交播放记录
   * @param {number} playSeconds 播放秒数
   */
  async _submitPlayRecord(playSeconds) {
    const targetCamera = this.data.cameraList.find(
      (item) => item.id == this.data.lastCameraId,
    );

    try {
      await addWatchLiveRecordApi({
        userId: app.globalData.userInfo.userId,
        siteCode: this.data.siteDetail.siteCode,
        deviceSerial: targetCamera?.deviceSerial,
        watchMinutes: playSeconds,
      });
    } catch (error) {
      console.error("提交播放记录失败:", error);
    }
  },

  // ==================== 分享相关方法 ====================

  /**
   * 分享给朋友
   * @returns {Object} 分享配置
   */
  onShareAppMessage: function () {
    return {
      title: app.globalData.share_config.title,
      imageUrl: app.globalData.share_config.imageUrl,
      path: this.data.shareUrl,
    };
  },

  /**
   * 分享到朋友圈
   * @returns {Object} 分享配置
   */
  onShareTimeline: function () {
    return {
      title: app.globalData.share_config.title,
      path: this.data.shareUrl,
    };
  },

  // ==================== 时间轴控制相关方法 ====================

  /**
   * 切换播放/暂停状态
   */
  togglePlay() {
    if (this.data.isDragging) return;

    const timeRuler = this.selectComponent("#timeRuler");
    const videoContext = wx.createVideoContext("myVideo", this);
    const newPlayState = !this.data.isPlaying;

    // 先同步子组件播放状态
    if (timeRuler) {
      newPlayState ? timeRuler.play() : timeRuler.pause();
    }
    // 再同步父组件自身状态
    this.setData({ isPlaying: newPlayState });

    // 同步 video 播放状态
    if (newPlayState) {
      videoContext.play();
    } else {
      videoContext.pause();
    }
  },

  /**
   * 强制更新视频源
   * @param {string} newVideoUrl 新的视频地址
   * @param {boolean} autoPlay 是否自动播放
   * @param {boolean} loadFirstFrame 是否加载第一帧
   */
  _forceUpdateVideo(newVideoUrl, autoPlay = true, loadFirstFrame = true) {
    this.setData({ showVideo: false });

    setTimeout(() => {
      this.setData({
        videoUrl: newVideoUrl,
        showVideo: true,
        isPlaying: autoPlay,
      });

      setTimeout(() => {
        const videoContext = wx.createVideoContext("myVideo", this);

        if (autoPlay) {
          videoContext.play();
        } else if (loadFirstFrame) {
          videoContext.seek(0);
        }
      }, 300);
    }, 50);
  },

  /**
   * 回到最新时间
   */
  async backToLatest() {
    if (this.data.isDragging) return;

    try {
      const { data } = await getDeviceStatus({ id: this.data.activeId });
      const { liveUrl } = data;

      const timeRuler = this.selectComponent("#timeRuler");
      timeRuler.resetToRealTime();
      this._forceUpdateVideo(liveUrl);
    } catch (error) {
      console.error("回到最新时间失败:", error);
      wx.showToast({
        title: "操作失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  // ==================== 时间轴事件处理方法 ====================

  /**
   * 开始拖拽时间轴
   */
  onDragStart() {
    this.setData({ isDragging: true });
    // 如果正在录制，取消录制监控
    if (this.data.isRecording) {
      this._clearTimer("recording");
      console.log("用户拖拽时间轴，已取消录制监控");
    }
  },

  /**
   * 拖拽进度条结束时校验权益
   * @param {Object} e 事件对象
   */
  async onScrollEnd(e) {
    if (!e.detail || !e.detail.scrollCenterTime) {
      return;
    }

    this.setData({ isDragging: false });

    const timeRuler = this.selectComponent("#timeRuler");
    const videoContext = wx.createVideoContext("myVideo", this);

    if (timeRuler) {
      timeRuler.pause();
    }
    videoContext.pause();

    // 录制中校验权益
    if (this.data.isRecording && this.data.recordTime1) {
      const dragTime = new Date(e.detail.scrollCenterTime).getTime();
      const used = Math.abs(
        Math.floor((dragTime - this.data.recordTime1) / 1000),
      );
      if (used > this.data.userRights.remain) {
        wx.showModal({
          title: "权益超出",
          content: "拖动超出可用权益，请重新选择开始结束时间",
          showCancel: false,
          confirmText: "确定",
          confirmColor: "#ff6b6b",
          success: () => {
            this.resetRecordingState();
          },
        });
        return;
      } else {
        const remain = this.data.userRights.remain - used;
        this.setData({
          userRights: {
            ...this.data.userRights,
            remain,
            remainFormatted: this._formatSecondsToTime(remain),
          },
        });
      }
    }

    const scrollCenterTime =
      e.detail.scrollCenterTime || timeRuler.getCurrentTime();
    const timestamp = Math.floor(new Date(scrollCenterTime).getTime() / 1000);

    try {
      const res = await liveReplay({
        id: this.data.activeId,
        timeStamp: timestamp,
      });

      this.setData({
        videoUrl: res.data,
        isPlaying: false,
      });

      setTimeout(() => {
        videoContext.pause();
      }, 300);
    } catch (error) {
      console.error("回放失败:", error);
      wx.showToast({
        title: "回放失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 时间变化事件处理
   * @param {Object} e 事件对象
   */
  onTimeChange(e) {
    // TODO: 处理时间变化逻辑
  },

  /**
   * 播放状态变化事件处理
   * @param {Object} e 事件对象
   */
  onPlayStateChange(e) {
    // 保证父子组件播放状态一致
    if (this.data.isPlaying !== e.detail.isPlaying) {
      this.setData({ isPlaying: e.detail.isPlaying });
      // 同步 video 播放状态
      const videoContext = wx.createVideoContext("myVideo", this);
      if (e.detail.isPlaying) {
        videoContext.play();
      } else {
        videoContext.pause();
      }
    }
  },

  // ==================== 录制相关方法 ====================

  /**
   * 处理录制开关切换
   */
  async handleRecordToggle() {
    if (this.data.isDragging) return;

    if (this.data.isRecording) {
      await this.stopRecording();
    } else {
      await this.startRecording();
    }
  },

  /**
   * 开始录制（优化版）
   */
  async startRecording() {
    // 1. 权益检查
    const rights = await this._checkRightsBeforeRecord();
    if (!rights) return;

    // 2. 初始化录制状态
    this._initRecordingState(rights);

    // 3. 启动录制监控
    this._startRecordingMonitor(rights.remain);

    // 4. 启动视频播放
    this._startVideoPlayback();

    // 5. 显示提示
    wx.showToast({
      title: "请滑动至结束时间",
      icon: "none",
      duration: 3000,
    });
  },

  /**
   * 录制前检查权益
   * @returns {Object|null} 权益信息
   */
  async _checkRightsBeforeRecord() {
    wx.showLoading({
      title: "检查权益中...",
      mask: true,
    });

    try {
      const rights = await this.checkUserRights();

      if (!rights || rights.remain <= 0) {
        this.showNoRightsModal(rights);
        return null;
      }

      return rights;
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 初始化录制状态
   * @param {Object} rights 权益信息
   */
  _initRecordingState(rights) {
    const currentTime = this.getCurrentFormattedTime();

    this.setData({
      isRecording: true,
      isPlaying: true,
      recordTime1: currentTime,
      playStartTime: new Date(currentTime), // 设置播放开始时间
      userRights: {
        total: rights.total,
        remain: rights.remain,
        remainFormatted: this._formatSecondsToTime(rights.remain),
      },
    });

    // 初始显示录制区域
    this._updateRecordingArea();
  },

  /**
   * 更新录制区域显示
   */
  _updateRecordingArea() {
    if (!this.data.isRecording || !this.data.playStartTime) return;

    const timeRuler = this.selectComponent("#timeRuler");
    if (!timeRuler) return;

    const startTime = this.data.playStartTime;
    let endTime;

    // 获取当前结束时间（拖拽位置或实时时间）
    if (timeRuler.data.isFollowingRealTime) {
      endTime = new Date();
    } else {
      endTime = timeRuler.getCurrentTime();
    }

    // 更新时间轴上的录制区域
    timeRuler.updateRecordingArea(startTime, endTime);
  },

  /**
   * 启动录制监控
   * @param {number} initialRemain 初始剩余权益
   */
  _startRecordingMonitor(initialRemain) {
    this._clearTimer("recording");

    let remain = initialRemain;

    const timer = setInterval(async () => {
      if (!this.data.isRecording) {
        this._clearTimer("recording");
        return;
      }

      // 更新权益倒计时
      remain = Math.max(0, remain - 1);

      this.setData({
        "userRights.remain": remain,
        "userRights.remainFormatted": this._formatSecondsToTime(remain),
      });

      // 更新录制区域显示
      this._updateRecordingArea();

      // 权益用尽时强制停止
      if (remain <= 0) {
        this._clearTimer("recording");
        await this._forceStopRecordingDueToRights();
      }
    }, 1000);

    this._setTimer("recording", timer);
  },

  /**
   * 权益用尽时强制停止录制（修复版）
   */
  async _forceStopRecordingDueToRights() {
    // 1. 重置录制状态
    this.setData({ isRecording: false });

    // 2. 暂停播放
    this._pauseVideoPlayback();

    // 3. 刷新权益状态
    await this.checkUserRights();

    // 4. 自动提交切片任务
    await this._submitSlicingTaskOnRightsEnd();

    // 5. 刷新录像列表
    await this.getVideoList();

    // 6. 显示提示
    wx.showModal({
      title: "权益已结束",
      content: "您的切片权益已用完，已自动提交切片任务",
      showCancel: false,
      confirmText: "确定",
      confirmColor: "#ff6b6b",
    });
  },

  /**
   * 权益用尽时自动提交切片任务
   */
  async _submitSlicingTaskOnRightsEnd() {
    if (!this.data.recordTime1) {
      console.warn("没有录制开始时间，无法提交切片任务");
      return;
    }

    try {
      const startTime = Math.floor(this.data.recordTime1 / 1000);
      // 结束时间 = 开始时间 + 用户总权益时间
      const endTime = startTime + this.data.userRights.remain;

      const { code, data, msg } = await submitSlicingTask({
        id: this.data.activeId,
        startTime: startTime,
        endTime: endTime,
      });

      if (code === 0) {
        console.log("权益用尽时自动提交切片任务成功");
        // 重置录制状态
        this.setData({ recordTime1: null });
      } else {
        console.error("自动提交切片任务失败:", msg);
      }
    } catch (error) {
      console.error("自动提交切片任务异常:", error);
    }
  },

  /**
   * 暂停视频播放
   */
  _pauseVideoPlayback() {
    const videoContext = wx.createVideoContext("myVideo", this);
    const timeRuler = this.selectComponent("#timeRuler");

    videoContext.pause();
    if (timeRuler) {
      timeRuler.pause();
    }
  },

  /**
   * 启动视频播放
   */
  _startVideoPlayback() {
    const videoContext = wx.createVideoContext("myVideo", this);
    const timeRuler = this.selectComponent("#timeRuler");

    videoContext.play();
    if (timeRuler) {
      timeRuler.play();
    }
  },

  /**
   * 清除权益倒计时定时器
   */
  clearRightsTimer() {
    if (this.data.rightsTimer) {
      clearInterval(this.data.rightsTimer);
      this.setData({ rightsTimer: null });
    }
    // 清除自动终止录制定时器
    this._clearTimer("recording");
  },

  /**
   * 停止录制（优化版）
   */
  async stopRecording() {
    this._clearTimer("recording");

    wx.showModal({
      content: "录制将会结束",
      confirmText: "确认",
      cancelText: "继续",
      success: async (res) => {
        if (res.confirm) {
          await this.handleRecordComplete();
        } else {
          // 用户选择继续录制，重新启动监控
          const currentRights = this.data.userRights;
          if (currentRights.remain > 0) {
            this._startRecordingMonitor(currentRights.remain);
          } else {
            // 权益已用完，强制停止
            await this._forceStopRecordingDueToRights();
          }
        }
      },
    });
  },

  /**
   * 停止录制时隐藏录制区域
   */
  resetRecordingState() {
    this._clearTimer("recording");
    this.setData({
      isRecording: false,
      recordTime1: null,
      playStartTime: null,
    });

    // 隐藏录制区域
    const timeRuler = this.selectComponent("#timeRuler");
    if (timeRuler) {
      timeRuler.hideRecordingArea();
    }
  },

  /**
   * 处理时间轴录制区域更新事件
   * @param {Object} e 事件对象
   */
  onRecordingAreaUpdate(e) {
    // 当用户拖拽时间轴时，实时更新录制区域
    if (this.data.isRecording && this.data.playStartTime) {
      this._updateRecordingArea();
    }
  },

  /**
   * 处理录制完成（优化版）
   */
  async handleRecordComplete() {
    const time1 = this.data.recordTime1;
    const timeRuler = this.selectComponent("#timeRuler");

    let time2;
    if (timeRuler?.data?.isFollowingRealTime) {
      time2 = Date.now();
    } else {
      time2 = new Date(timeRuler.getCurrentTime()).getTime();
    }

    const startTime = Math.min(time1, time2);
    const endTime = Math.max(time1, time2);

    wx.showLoading({
      title: "正在提交切片任务...",
      mask: true,
    });

    try {
      const { code, data, msg } = await submitSlicingTask({
        id: this.data.activeId,
        startTime: Math.floor(startTime / 1000),
        endTime: Math.floor(endTime / 1000),
      });

      if (code === 0) {
        wx.showToast({
          title: "切片任务提交成功",
          icon: "success",
          duration: 2000,
        });

        // 重置录制状态
        this.resetRecordingState();

        // 刷新权益和录像列表
        await Promise.all([this.checkUserRights(), this.getVideoList()]);
      } else {
        // 提交失败时也要重置录制状态
        this.resetRecordingState();
        // 重新检查权益状态
        await this.checkUserRights();
      }
    } catch (error) {
      console.error("提交切片任务失败:", error);
      wx.showToast({
        title: "网络错误，请重试",
        icon: "none",
        duration: 2000,
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 获取当前格式化时间
   * @returns {number} 当前时间戳
   */
  getCurrentFormattedTime() {
    const timeRuler = this.selectComponent("#timeRuler");
    if (timeRuler) {
      const scrollCenterTime = timeRuler.getCurrentTime();
      const date = new Date(scrollCenterTime);
      return date.getTime();
    }
    return Date.now();
  },

  // ==================== 优化后的触摸事件处理 ====================

  /**
   * 录像项触摸开始
   * @param {Object} e 触摸事件
   */
  onVideoItemTouchStart(e) {
    this.setData({
      touchState: {
        startTime: Date.now(),
        startX: e.touches[0].clientX,
        startY: e.touches[0].clientY,
        moved: false,
      },
    });
  },

  /**
   * 录像项触摸移动
   * @param {Object} e 触摸事件
   */
  onVideoItemTouchMove(e) {
    const { touchState } = this.data;
    if (!touchState.startX || !touchState.startY) return;

    const deltaX = e.touches[0].clientX - touchState.startX;
    const deltaY = e.touches[0].clientY - touchState.startY;
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    if (absDeltaX > 3 || absDeltaY > 3) {
      this.setData({
        "touchState.moved": true,
      });
    }

    // 垂直滑动优先
    if (absDeltaY > absDeltaX && absDeltaY > 10) return;

    // 水平滑动处理删除按钮
    if (absDeltaX > 5 && absDeltaX > absDeltaY) {
      const { id } = e.currentTarget.dataset;

      if (deltaX < -30) {
        this.setData({ swipeDeleteItemId: id });
      } else if (deltaX > 15 && this.data.swipeDeleteItemId === id) {
        this.setData({ swipeDeleteItemId: null });
      }
    }
  },

  /**
   * 录像项触摸结束
   * @param {Object} e 触摸事件
   */
  onVideoItemTouchEnd(e) {
    const { touchState } = this.data;
    const touchDuration = Date.now() - touchState.startTime;
    const isMoved = touchState.moved;

    // 重置触摸状态
    this.setData({
      touchState: {
        startTime: null,
        startX: null,
        startY: null,
        moved: false,
      },
    });

    if (isMoved || touchDuration >= 200) return;

    const { url, id } = e.currentTarget.dataset;

    // 处理删除按钮状态
    if (this.data.swipeDeleteItemId === id || this.data.swipeDeleteItemId) {
      this.setData({ swipeDeleteItemId: null });
      return;
    }

    // 检查视频状态并跳转播放
    const videoItem = this.data.videoList.find((item) => item.id === id);
    if (videoItem?.sliceStatue === 20) {
      this.navigateToVideoPlay(url, id);
    }
  },

  /**
   * 删除录像项
   * @param {Object} e 事件对象
   */
  onDeleteVideoItem(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }

    const { id } = e.currentTarget.dataset;
    const videoItem = this.data.videoList.find((item) => item.id === id);

    if (!videoItem) return;

    wx.showModal({
      title: "确认删除",
      content: `确定要删除吗？删除后无法恢复。`,
      confirmText: "删除",
      cancelText: "取消",
      confirmColor: "#ff4444",
      success: (res) => {
        if (res.confirm) {
          this.deleteVideoItem(id);
        }
        this.setData({
          swipeDeleteItemId: null,
        });
      },
    });
  },

  /**
   * 执行删除录像项
   * @param {string} videoId 录像ID
   */
  async deleteVideoItem(videoId) {
    wx.showLoading({
      title: "删除中...",
      mask: true,
    });

    try {
      const res = await deleteSlicingTask({ id: videoId });

      if (res.code === 0) {
        await this.getVideoList();
        wx.showToast({
          title: "删除成功",
          icon: "success",
        });
      } else {
        wx.showToast({
          title: res.message || "删除失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("删除录像失败:", error);
      wx.showToast({
        title: error.message || "删除失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 隐藏删除按钮
   */
  hideDeleteButton() {
    if (this.data.swipeDeleteItemId) {
      this.setData({
        swipeDeleteItemId: null,
      });
    }
  },

  /**
   * 跳转到视频播放页面
   * @param {string} videoUrl 视频地址
   * @param {string} videoId 视频ID
   */
  navigateToVideoPlay(videoUrl, videoId) {
    if (!videoUrl) {
      wx.showToast({
        title: "视频链接无效",
        icon: "none",
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/common/play/index?videoUrl=${encodeURIComponent(videoUrl)}&videoId=${videoId}`,
      fail: (error) => {
        console.error("跳转播放页面失败:", error);
        wx.showToast({
          title: "跳转失败",
          icon: "none",
        });
      },
    });
  },

  /**
   * 显示权益不足弹窗
   * @param {Object} rights 权益信息
   */
  showNoRightsModal(rights) {
    const content = rights
      ? "您的切片权益已用完，请联系客服"
      : "无法获取权益信息，请检查网络";
    wx.showModal({
      title: "权益不足",
      content,
      showCancel: false,
      confirmText: "确定",
      confirmColor: "#ff6b6b",
    });
  },
});
