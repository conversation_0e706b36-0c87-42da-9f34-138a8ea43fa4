<NavBar nav-data="{{ navData }}"/>
<view class="page-container" style="margin-top: {{ paddingHeight }}px">
  <!-- 视频播放区域 -->
  <view class="video-section">
    <video
      wx:if="{{showVideo}}"
      class="video-player"
      id="myVideo"
      src="{{ videoUrl }}"
      custom-cache="{{false}}"
      show-center-play-btn="{{ false }}"
      show-fullscreen-btn
      show-play-btn="{{ false }}"
      show-progress="{{ false }}"
      controls
      show-bottom-progress="{{ false }}"
      vslide-gesture-in-fullscreen
      enable-play-gesture="{{true}}"
      vslide-gesture="{{true}}"
      enable-auto-rotation="{{true}}"
      show-snapshot-button="{{true}}"
      autoplay="{{isPlaying}}"
      muted="{{ isMuted }}"
    ></video>

    <view class="video-controls">
      <view class="mute-toggle {{ isMuted ? 'mute-toggle--active' : '' }}" bind:tap="changeMute">
        <image class="mute-toggle__icon" src="/images/camera/muted.png"/>
        <text class="mute-toggle__text">静音</text>
      </view>
    </view>
  </view>

  <!-- 摄像头选择器 -->
  <scroll-view scroll-x class="camera-selector">
    <view class="camera-list">
      <view
        wx:for="{{ cameraList }}"
        wx:key="id"
        data-id="{{ item.id }}"
        bind:tap="handleTap"
        class="camera-item {{ activeId === item.id ? 'camera-item--active' : '' }}"
      >
        <text class="camera-item__name">{{ item.deviceName }}</text>
      </view>
    </view>
  </scroll-view>

  <!-- 时间轴控制区域 -->
  <view class="timeline-section">
    <view class="timeline-controls">
      <view class="userRights">剩余权益: {{userRights.remainFormatted}}</view>
      <view class="control-buttons">
        <view
          class="control-btn control-btn--play {{isPlaying ? 'control-btn--pause' : 'control-btn--play'}} {{isDragging ? 'control-btn--disabled' : ''}}"
          bindtap="togglePlay">
          <text class="control-btn__text">{{isPlaying ? '暂停画面' : '继续播放'}}</text>
        </view>
        <view
          class="control-btn control-btn--back {{isDragging ? 'control-btn--disabled' : ''}}"
          bindtap="backToLatest">
          <text class="control-btn__text">最新画面</text>
        </view>
      </view>
    </view>
    <time-ruler
      id="timeRuler"
      bind:scrollEnd="onScrollEnd"
      bind:dragStart="onDragStart"
      bind:timeChange="onTimeChange"
      bind:playStateChange="onPlayStateChange"
      bind:recordingAreaUpdate="onRecordingAreaUpdate">
    </time-ruler>
  </view>

  <!-- 录制控制区域 -->
  <view class="recording-section">
    <button
      class="recording-btn {{isRecording ? 'recording-btn--active' : 'recording-btn--idle'}} {{isDragging ? 'recording-btn--disabled' : ''}}"
      bindtap="handleRecordToggle">
      <text class="recording-btn__text">{{isRecording ? '结束切片' : '开始切片'}}</text>
    </button>
  </view>
  <!-- 录像列表区域 -->
  <view class="video-list" bindtap="hideDeleteButton">
    <view class="video-item-wrapper {{item.id === swipeDeleteItemId ? 'video-item-wrapper--show-delete' : ''}}"
          wx:for="{{videoList}}"
          wx:key="id">
      <view
        class="video-item {{item.isUpdating ? 'video-item--updating' : ''}} {{item.isNew ? 'video-item--new' : ''}} {{item.statusChangeType ? 'video-item--status-' + item.statusChangeType : ''}} {{item.sliceStatue !== 20 ? 'video-item--disabled' : ''}}"
        data-url="{{item.objectKey}}"
        data-id="{{item.id}}"
        bindtouchstart="onVideoItemTouchStart"
        bindtouchmove="onVideoItemTouchMove"
        bindtouchend="onVideoItemTouchEnd">
        <view class="video-item__thumbnail">
          <image
            class="video-item__image"
            src="{{item.thumbnail}}"
            binderror="onThumbnailError"
            data-id="{{item.id}}"
            data-index="{{index}}"
            data-timestamp="{{item.thumbnailTimestamp || 0}}"
          ></image>
        </view>
        <view class="video-item__content">
          <view class="video-item__header">
            <view class="video-item__title">{{item.name}}</view>
            <view class="video-item__duration">
              <image class="video-item__duration-icon" src="/images/live/camera.png" mode="aspectFit"></image>
              <text class="video-item__duration-text">{{item.duration}}</text>
            </view>
          </view>
          <view class="video-item__time-range">
            {{item.startTimeDisplay}} ~ {{item.endTimeDisplay}}
          </view>
        </view>
        <!-- 失败状态遮罩层 -->
        <view
          wx:if="{{item.sliceStatue === 40}}"
          class="video-item__failure-overlay"
          data-id="{{item.id}}"
          data-start-time="{{item.startTime}}"
          data-end-time="{{item.endTime}}"
          catchtap="retrySlicingTask">
          <view class="video-item__retry-btn">
            <text class="video-item__retry-text">点击重试</text>
          </view>
        </view>
        <!-- 录制中状态遮罩层 -->
        <view
          wx:if="{{item.sliceStatue === 1 || item.sliceStatue === 5 || item.sliceStatue === 10}}"
          class="video-item__processing-overlay">
          <view class="video-item__processing-content">
            <text class="video-item__processing-text">切片中...</text>
          </view>
        </view>
        <!-- 切片成功状态遮罩层 -->
        <view
          wx:if="{{item.statusChangeType === 'success'}}"
          class="video-item__success-overlay">
          <view class="video-item__success-content">
            <text class="video-item__success-text">切片成功！</text>
          </view>
        </view>
      </view>

      <view class="video-item__delete-btn"
            data-id="{{item.id}}"
            bindtap="onDeleteVideoItem"
            catchtap="onDeleteVideoItem">
        <t-icon name="delete-1" size="48rpx" data-name="delete-1"/>
      </view>
    </view>
  </view>
</view>
