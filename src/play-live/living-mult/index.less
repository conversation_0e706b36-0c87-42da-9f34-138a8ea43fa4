// 全局页面样式
page {
  background-color: rgba(0, 0, 0, 0.8);
}

// 主容器
.page-container {
  padding-top: 16rpx;
}

// ==================== 视频播放区域 ====================
.video-section {
  position: relative;
  width: 100%;
  height: 422rpx;
}

.video-player {
  width: 100%;
  height: 422rpx;
}

.video-controls {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10;
  padding: 24rpx 30rpx;
}

.mute-toggle {
  display: flex;
  align-items: center;
  border-radius: 30rpx;
  font-weight: 500;
  font-size: 20rpx;
  color: #FFFFFF;
  padding: 6rpx 12rpx;
  background-color: rgba(0, 0, 0, .6);
  transition: all 0.3s ease;
  cursor: pointer;

  &--active {
    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    color: #052031;
    opacity: 1;
  }

  &__icon {
    width: 28rpx;
    height: 28rpx;
    margin-right: 8rpx;
  }

  &__text {
    flex-shrink: 0;
  }
}

// ==================== 摄像头选择器 ====================
.camera-selector {
  overflow-x: auto;
  overflow-y: hidden;
}

.camera-list {
  padding: 20rpx 20rpx 40rpx;
  display: flex;
  flex-wrap: nowrap;
}

.camera-item {
  flex-shrink: 0;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  color: #ffffff;
  margin-right: 16rpx;
  background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
  opacity: .5;
  border: 1rpx solid transparent;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &--active {
    background: linear-gradient(90deg, #2FD2F8 0%, #2FF8A8 100%);
    border: 1rpx solid #FFFFFF;
    font-weight: bold;
    color: #052031;
    opacity: 1;
  }

  &__name {
    white-space: nowrap;
  }
}

// ==================== 时间轴控制区域 ====================
.timeline-section {
  width: 100%;
  position: relative;
}

.timeline-controls {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.control-buttons {
  z-index: 1;
  display: flex;
  padding-right: 25rpx;
  gap: 10rpx;
}

.control-btn {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  outline: none;

  &--play {
    background-color: #52c41a;
    color: white;

    &:active {
      background-color: #389e0d;
    }
  }

  &--pause {
    background-color: #ff4d4f;
    color: white;

    &:active {
      background-color: #d9363e;
    }
  }

  &--back {
    background-color: #1890ff;
    color: white;

    &:active {
      background-color: #096dd9;
    }
  }

  &--disabled {
    background-color: #d9d9d9 !important;
    color: #999 !important;
    cursor: not-allowed;
    pointer-events: none;
  }

  &__text {
    display: block;
  }
}

// ==================== 录制控制区域 ====================
.recording-section {
  margin: 40rpx auto;
  display: flex;
  justify-content: center;
}

.recording-btn {
  position: relative;
  width: 400rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: none;
  outline: none;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  &--idle {
    background: linear-gradient(135deg, #2FD2F8, #2FF8A8);
    box-shadow: 0 8rpx 20rpx rgba(47, 210, 248, 0.3);
    animation: breathe 2s infinite ease-in-out;
  }

  &--active {
    background: linear-gradient(135deg, #ff4757, #ff3838);
    box-shadow: 0 8rpx 20rpx rgba(255, 71, 87, 0.4);
    animation: recordingPulse 1s infinite ease-in-out;

    &::before {
      opacity: 1;
      animation: recordingDot 1s infinite ease-in-out;
    }
  }

  &--disabled {
    background: #d9d9d9 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
    animation: none !important;
    box-shadow: none !important;
    pointer-events: none;

    &::before {
      opacity: 0 !important;
      animation: none !important;
    }
  }

  &:active:not(.recording-btn--disabled) {
    transform: scale(0.95);
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 20rpx;
    transform: translateY(-50%);
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: #ffffff;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &__text {
    position: relative;
    z-index: 1;
  }
}

// ==================== 录像列表区域 ====================
.video-list {
  height: 32vh;
  width: 100vw;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0 24rpx;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

.video-item-wrapper {
  position: relative;
  overflow: hidden;
  margin-bottom: 24rpx;

  &--show-delete {
    .video-item {
      transform: translateX(-120rpx);
    }

    .video-item__delete-btn {
      transform: translateX(0);
    }
  }
}

.video-item {
  width: 100%;
  display: flex;
  height: 130rpx;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4rpx 20rpx 0 rgba(54, 127, 137, 0.1);
  border-radius: 32rpx;
  transition: transform 0.3s ease-out;
  position: relative;
  z-index: 2;
  overflow: hidden;

  &--new {
    animation: slideInFromTop 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  &--status-success {
    animation: successPulse 1.5s ease-out;
  }

  &--status-failed {
    animation: failedShake 1.5s ease-out;
  }

  &--status-processing {
    animation: updatePulse 1.5s ease-out;
  }

  &--status-thumbnail {
    .video-item__image {
      animation: thumbnailUpdate 0.8s ease-in-out;
    }
  }

  // ==================== 切片成功状态遮罩层 ====================
  &__success-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2ff8a8 0%, #27e09c 100%);
    border-radius: 32rpx;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    backdrop-filter: blur(2rpx);
    animation: overlayFadeIn 0.3s ease-out, overlayFadeOut 0.6s ease-in 0.9s;
  }
  &__success-content {
    background: transparent;
    border-radius: 20rpx;
    padding: 0;
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: none;
  }
  &__success-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #fff;
    text-align: center;
    letter-spacing: 2rpx;
    text-shadow: 0 2rpx 8rpx rgba(39, 224, 156, 0.18);
  }

  &__thumbnail {
    width: 266rpx;
    height: 100%;
    border-radius: 20rpx;
    overflow: hidden;
    flex-shrink: 0;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    pointer-events: none;
  }

  &__content {
    flex: 1;
    padding: 24rpx 20rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: flex-start;
  }

  &__title {
    font-weight: 550;
    font-size: 32rpx;
    color: #FFFFFF;
    flex: 1;
    margin-right: 20rpx;
  }

  &__duration {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  &__duration-icon {
    width: 44rpx;
    height: 44rpx;
    margin-right: 10rpx;
  }

  &__duration-text {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 26rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    background: linear-gradient(270deg, #00CEFF 0%, #57FAE6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  &__time-range {
    width: 100%;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #FFFFFF;
  }

  &__delete-btn {
    height: 130rpx;
    border-radius: 32rpx;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 120rpx;
    background-color: #ff4444;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 28rpx;
    transform: translateX(120rpx);
    transition: transform 0.3s ease;
    z-index: 1;
  }

  // ==================== 失败状态遮罩层 ====================
  &__failure-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 71, 87, 0.9), rgba(255, 56, 56, 0.95));
    border-radius: 32rpx;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    backdrop-filter: blur(2rpx);
    animation: overlayFadeIn 0.3s ease-out;
  }

  &__retry-btn {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 12rpx 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    animation: retryButtonPulse 2s infinite ease-in-out;
  }

  &__retry-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #ff4757;
    text-align: center;
  }

  // ==================== 录制中状态遮罩层 ====================
  &__processing-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(47, 210, 248, 0.85), rgba(47, 248, 168, 0.9));
    border-radius: 32rpx;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    backdrop-filter: blur(2rpx);
    animation: overlayFadeIn 0.3s ease-out;
  }

  &__processing-content {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: processingPulse 1.5s infinite ease-in-out;
  }

  &__processing-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
    animation: loadingDots 1.8s infinite ease-in-out;

    &::after {
      content: '';
      animation: loadingDotsAfter 1.8s infinite ease-in-out;
    }
  }

}

// ==================== 动画定义 ====================
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 20rpx rgba(47, 210, 248, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 30rpx rgba(47, 248, 168, 0.5);
  }
}

@keyframes recordingPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

@keyframes recordingDot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  60% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes updatePulse {
  0% {
    background: rgba(47, 210, 248, 0.1);
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx 0 rgba(47, 210, 248, 0.2);
  }
  25% {
    background: rgba(47, 210, 248, 0.3);
    transform: scale(1.02);
    box-shadow: 0 8rpx 30rpx 0 rgba(47, 210, 248, 0.4);
  }
  50% {
    background: rgba(47, 210, 248, 0.4);
    transform: scale(1.03);
    box-shadow: 0 12rpx 40rpx 0 rgba(47, 210, 248, 0.5);
  }
  75% {
    background: rgba(47, 210, 248, 0.3);
    transform: scale(1.02);
    box-shadow: 0 8rpx 30rpx 0 rgba(47, 210, 248, 0.4);
  }
  100% {
    background: rgba(47, 210, 248, 0.1);
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx 0 rgba(47, 210, 248, 0.2);
  }
}

@keyframes successPulse {
  0%, 100% {
    background: rgba(47, 248, 168, 0.1);
    transform: scale(1);
    box-shadow: 0 4rpx 20rpx 0 rgba(47, 248, 168, 0.2);
  }
  50% {
    background: rgba(47, 248, 168, 0.4);
    transform: scale(1.05);
    box-shadow: 0 16rpx 50rpx 0 rgba(47, 248, 168, 0.5);
  }
}

@keyframes failedShake {
  0%, 100% {
    background: rgba(248, 47, 47, 0.1);
    transform: translateX(0) scale(1);
  }
  10% {
    background: rgba(248, 47, 47, 0.3);
    transform: translateX(-10rpx) scale(1.02);
  }
  25% {
    background: rgba(248, 47, 47, 0.4);
    transform: translateX(10rpx) scale(1.02);
  }
  40% {
    transform: translateX(-8rpx) scale(1.01);
  }
  55% {
    transform: translateX(8rpx) scale(1.01);
  }
  70% {
    transform: translateX(-5rpx) scale(1);
  }
  85% {
    transform: translateX(5rpx) scale(1);
  }
}

@keyframes thumbnailUpdate {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1);
  }
  25% {
    opacity: 0.4;
    transform: scale(0.95);
    filter: brightness(1.2);
  }
  50% {
    opacity: 0.2;
    transform: scale(0.9);
    filter: brightness(1.4);
  }
  75% {
    opacity: 0.6;
    transform: scale(0.95);
    filter: brightness(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1);
  }
}

@keyframes overlayFadeIn {
  0% {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(2rpx);
  }
}

@keyframes overlayFadeOut {
  0% {
    opacity: 1;
    backdrop-filter: blur(2rpx);
  }
  100% {
    opacity: 0;
    backdrop-filter: blur(0);
  }
}

@keyframes retryButtonPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.3);
  }
}


// ==================== 录制中动画定义 ====================
@keyframes processingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes loadingDots {
  0%, 20% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  80%, 100% {
    opacity: 1;
  }
}

@keyframes loadingDotsAfter {
  0% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
  100% {
    content: '';
  }
}

.userRights{
  font-size: 26rpx;
  font-weight: 500;
  color: white;
}
