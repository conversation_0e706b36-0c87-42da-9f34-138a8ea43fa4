{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "miniprogramRoot": "dist/", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": true, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "useStaticServer": true, "condition": true, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "appid": "wxde3172f13dca75fd", "cloudfunctionTemplateRoot": "", "simulatorType": "wechat", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.6.5-29"}, "srcMiniprogramRoot": "dist/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "condition": {}, "libVersion": "2.24.4", "packOptions": {"ignore": [], "include": []}, "projectArchitecture": "multiPlatform"}